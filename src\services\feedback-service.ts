import { supabase } from '@/integrations/supabase/client';
import { User } from '@supabase/supabase-js';

export interface FeedbackData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

/**
 * Submit feedback to the database
 * @param data Feedback data (name, email, subject, message)
 * @param user Current user (optional)
 * @returns Object with success status and error message if applicable
 */
export async function submitFeedback(data: FeedbackData, user: User | null = null) {
  try {
    // Validate input
    if (!data.name.trim()) {
      return { success: false, error: 'Name is required' };
    }

    if (!data.email.trim()) {
      return { success: false, error: 'Email is required' };
    }

    if (!data.subject.trim()) {
      return { success: false, error: 'Subject is required' };
    }

    if (!data.message.trim()) {
      return { success: false, error: 'Message is required' };
    }

    console.log('Submitting feedback:', { name: data.name, email: data.email, subject: data.subject });

    // Insert directly into the database
    const { error } = await supabase
      .from('feedback')
      .insert({
        name: data.name,
        email: data.email,
        subject: data.subject,
        message: data.message,
        user_id: user?.id || null,
        status: 'new'
      });

    if (!error) {
      console.log('✅ Feedback saved to database successfully');
      return { success: true };
    } else {
      console.error('❌ Database error submitting feedback:', error);

      // Provide specific error messages based on error type
      if (error.code === 'PGRST204' && error.message?.includes('schema cache')) {
        return {
          success: false,
          error: 'Database schema is updating. Please try again in a few moments.'
        };
      } else if (error.code === 'PGRST106' || error.message?.includes('does not exist')) {
        return {
          success: false,
          error: 'Feedback system is not properly configured. Please contact support.'
        };
      } else if (error.code === '23505') {
        return {
          success: false,
          error: 'This feedback has already been submitted.'
        };
      } else {
        return {
          success: false,
          error: error.message || 'Failed to submit feedback. Please try again.'
        };
      }
    }
  } catch (error: any) {
    console.error('❌ Exception submitting feedback:', error);
    return {
      success: false,
      error: 'An unexpected error occurred. Please try again later.'
    };
  }
}

/**
 * Generate a UUID for local storage items
 */
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Get all feedback (admin only)
 * @returns Array of feedback items
 */
export async function getAllFeedback() {
  try {
    const { data, error } = await supabase
      .from('feedback')
      .select('*')
      .order('created_at', { ascending: false });

    if (!error) {
      return { success: true, data: data || [] };
    }

    console.error('Database error fetching feedback:', error);

    if (error.code === 'PGRST106' || error.message?.includes('does not exist')) {
      return {
        success: false,
        error: 'Feedback table does not exist. Please run the setup SQL script.',
        data: []
      };
    }

    return {
      success: false,
      error: error.message || 'Failed to fetch feedback',
      data: []
    };
  } catch (error: any) {
    console.error('Error fetching feedback:', error);
    return {
      success: false,
      error: error.message || 'Failed to fetch feedback',
      data: []
    };
  }
}

/**
 * Update feedback status (admin only)
 * @param id Feedback ID
 * @param status New status ('new', 'read', 'responded', 'archived')
 * @returns Success status and error message if applicable
 */
export async function updateFeedbackStatus(id: string, status: 'new' | 'read' | 'responded' | 'archived') {
  try {
    const { error } = await supabase
      .from('feedback')
      .update({
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', id);

    if (!error) {
      return { success: true };
    }

    console.error('Database error updating feedback status:', error);
    return {
      success: false,
      error: error.message || 'Failed to update feedback status'
    };
  } catch (error: unknown) {
    console.error('Error updating feedback status:', error);
    return {
      success: false,
      error: error.message || 'Failed to update feedback status'
    };
  }
}
