import { useState, useCallback, useRef } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '@/hooks/use-auth';
import { usePaystackPayment } from 'react-paystack';
import {
  toKobo,
  generateReference,
  SubscriptionPlan,
  handlePaymentSuccess
} from '@/utils/paystack';
import { PaystackResponse } from '@/types/paystack';
import { toast } from 'sonner';

// Payment processing states
type PaymentState = 'idle' | 'initializing' | 'processing' | 'verifying' | 'success' | 'error';

// Error types for better error handling
type PaymentErrorType = 
  | 'AUTHENTICATION_REQUIRED'
  | 'INITIALIZATION_FAILED'
  | 'PAYMENT_CANCELLED'
  | 'VERIFICATION_FAILED'
  | 'NETWORK_ERROR'
  | 'CONFIGURATION_ERROR'
  | 'UNKNOWN_ERROR';

interface PaymentError {
  type: PaymentErrorType;
  message: string;
  details?: string;
  retryable: boolean;
}

interface SecurePaystackButtonProps {
  plan: SubscriptionPlan;
  className?: string;
  buttonText?: string;
  onPaymentStart?: () => void;
  onPaymentSuccess?: (reference: string) => void;
  onPaymentError?: (error: PaymentError) => void;
  maxRetries?: number;
  enableVerification?: boolean;
}

const SecurePaystackButton = ({ 
  plan, 
  className = '', 
  buttonText = 'Select',
  onPaymentStart,
  onPaymentSuccess,
  onPaymentError,
  maxRetries = 3,
  enableVerification = true
}: SecurePaystackButtonProps) => {
  const { user } = useAuth();
  const [paymentState, setPaymentState] = useState<PaymentState>('idle');
  const [currentError, setCurrentError] = useState<PaymentError | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const paymentRef = useRef<string | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Create payment error helper
  const createPaymentError = useCallback((
    type: PaymentErrorType, 
    message: string, 
    details?: string
  ): PaymentError => {
    const retryableErrors: PaymentErrorType[] = [
      'INITIALIZATION_FAILED',
      'VERIFICATION_FAILED',
      'NETWORK_ERROR',
      'CONFIGURATION_ERROR'
    ];

    return {
      type,
      message,
      details,
      retryable: retryableErrors.includes(type)
    };
  }, []);

  // Handle payment errors with appropriate user feedback
  const handlePaymentError = useCallback((error: PaymentError) => {
    setCurrentError(error);
    setPaymentState('error');
    
    // Call external error handler if provided
    onPaymentError?.(error);

    // Show appropriate toast message
    switch (error.type) {
      case 'AUTHENTICATION_REQUIRED':
        toast.error('Please log in to subscribe to a plan');
        break;
      case 'CONFIGURATION_ERROR':
        toast.error('Payment service configuration error. Please contact support.');
        break;
      case 'INITIALIZATION_FAILED':
        toast.error('Failed to initialize payment. Please try again.');
        break;
      case 'PAYMENT_CANCELLED':
        toast.info('Payment was cancelled');
        break;
      case 'VERIFICATION_FAILED':
        toast.error('Payment verification failed. Please contact support if amount was deducted.');
        break;
      case 'NETWORK_ERROR':
        toast.error('Network error. Please check your connection and try again.');
        break;
      default:
        toast.error('An unexpected error occurred. Please try again.');
    }
  }, [onPaymentError]);

  // Generate fresh payment reference
  const generateFreshReference = useCallback(() => {
    const reference = generateReference();
    paymentRef.current = reference;
    return reference;
  }, []);

  // Validate payment configuration
  const validateConfiguration = useCallback(() => {
    const publicKey = import.meta.env.VITE_PAYSTACK_PUBLIC_KEY;
    
    if (!publicKey) {
      throw createPaymentError(
        'CONFIGURATION_ERROR',
        'Payment service not configured',
        'Paystack public key is missing'
      );
    }

    if (!user?.email) {
      throw createPaymentError(
        'AUTHENTICATION_REQUIRED',
        'User authentication required',
        'No user email available'
      );
    }

    return publicKey;
  }, [user?.email, createPaymentError]);

  // Create payment configuration with validation
  const createPaymentConfig = useCallback(() => {
    const publicKey = validateConfiguration();

    return {
      reference: generateFreshReference(),
      email: user!.email,
      amount: toKobo(plan.amount),
      publicKey,
      currency: 'NGN' as const,
      metadata: {
        custom_fields: [
          {
            display_name: 'Plan',
            variable_name: 'plan',
            value: plan.id
          }
        ]
      }
    };
  }, [validateConfiguration, generateFreshReference, user, plan.amount, plan.id]);

  // Enhanced payment success handler with optional verification
  const handleSuccessfulPayment = useCallback(async (reference: PaystackResponse) => {
    if (!user?.email) {
      handlePaymentError(createPaymentError(
        'AUTHENTICATION_REQUIRED',
        'User authentication lost during payment'
      ));
      return;
    }

    if (enableVerification) {
      setPaymentState('verifying');

      // Set verification timeout
      timeoutRef.current = setTimeout(() => {
        handlePaymentError(createPaymentError(
          'VERIFICATION_FAILED',
          'Payment verification timed out',
          'Verification took longer than expected'
        ));
      }, 30000); // 30 second timeout

      try {
        const result = await handlePaymentSuccess(reference.reference, plan.id, user.email);
        
        // Clear timeout
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
        }

        if (result.success) {
          setPaymentState('success');
          setCurrentError(null);
          setRetryCount(0);
          toast.success(result.message);
          onPaymentSuccess?.(reference.reference);
          
          // Redirect to success page after a short delay
          setTimeout(() => {
            window.location.href = `/payment/success?reference=${reference.reference}&plan=${plan.name}`;
          }, 1500);
        } else {
          handlePaymentError(createPaymentError(
            'VERIFICATION_FAILED',
            result.message || 'Payment verification failed',
            result.error
          ));
        }
      } catch (error) {
        // Clear timeout
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
        }

        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        
        // Determine error type based on error message
        let errorType: PaymentErrorType = 'UNKNOWN_ERROR';
        if (errorMessage.toLowerCase().includes('network') || 
            errorMessage.toLowerCase().includes('fetch')) {
          errorType = 'NETWORK_ERROR';
        } else if (errorMessage.toLowerCase().includes('timeout')) {
          errorType = 'VERIFICATION_FAILED';
        }

        handlePaymentError(createPaymentError(
          errorType,
          'Error processing payment verification',
          errorMessage
        ));
      }
    } else {
      // Skip verification - direct success
      setPaymentState('success');
      setCurrentError(null);
      setRetryCount(0);
      toast.success(`Successfully subscribed to ${plan.name} plan`);
      onPaymentSuccess?.(reference.reference);
      
      // Redirect to success page
      setTimeout(() => {
        window.location.href = `/payment/success?reference=${reference.reference}&plan=${plan.name}`;
      }, 1500);
    }
  }, [
    user?.email, 
    enableVerification, 
    plan.id, 
    plan.name, 
    handlePaymentError, 
    createPaymentError, 
    onPaymentSuccess
  ]);

  // Payment close handler
  const handlePaymentClosure = useCallback(() => {
    setPaymentState('idle');
    handlePaymentError(createPaymentError(
      'PAYMENT_CANCELLED',
      'Payment was cancelled by user'
    ));
  }, [handlePaymentError, createPaymentError]);

  // Retry payment logic
  const retryPayment = useCallback(() => {
    if (retryCount >= maxRetries) {
      toast.error(`Maximum retry attempts (${maxRetries}) reached. Please try again later.`);
      return;
    }

    setRetryCount(prev => prev + 1);
    setCurrentError(null);
    handlePayment();
  }, [retryCount, maxRetries]);

  // Main payment handler
  const handlePayment = useCallback(() => {
    // Reset state
    setCurrentError(null);
    
    setPaymentState('initializing');
    onPaymentStart?.();

    try {
      // Create and validate payment configuration
      const config = createPaymentConfig();
      
      // Initialize Paystack payment
      const initializePayment = usePaystackPayment(config);
      
      setPaymentState('processing');
      
      // Start payment process
      initializePayment(
        handleSuccessfulPayment,
        handlePaymentClosure
      );
      
    } catch (error) {
      if (error instanceof Error && 'type' in error) {
        handlePaymentError(error as PaymentError);
      } else {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        handlePaymentError(createPaymentError(
          'INITIALIZATION_FAILED',
          'Failed to initialize payment',
          errorMessage
        ));
      }
    }
  }, [
    createPaymentConfig, 
    handleSuccessfulPayment, 
    handlePaymentClosure, 
    handlePaymentError, 
    createPaymentError,
    onPaymentStart
  ]);

  // Cleanup timeout on unmount
  useState(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  });

  // Get button text based on state
  const getButtonText = () => {
    switch (paymentState) {
      case 'initializing':
        return 'Initializing...';
      case 'processing':
        return 'Processing Payment...';
      case 'verifying':
        return 'Verifying Payment...';
      case 'success':
        return 'Payment Successful!';
      case 'error':
        return currentError?.retryable ? 'Retry Payment' : 'Payment Failed';
      default:
        return buttonText;
    }
  };

  // Determine if button should be disabled
  const isDisabled = ['initializing', 'processing', 'verifying', 'success'].includes(paymentState);

  // Handle button click
  const handleButtonClick = () => {
    if (paymentState === 'error' && currentError?.retryable) {
      retryPayment();
    } else {
      handlePayment();
    }
  };

  return (
    <div className="w-full">
      <motion.button
        className={`w-full py-3 px-6 rounded-full font-medium transition-colors duration-200 uppercase text-sm tracking-wider ${className} ${
          paymentState === 'error' ? 'border-red-500 text-red-500' : ''
        } ${paymentState === 'success' ? 'bg-green-500 text-white' : ''}`}
        whileHover={!isDisabled ? { scale: 1.05 } : {}}
        whileTap={!isDisabled ? { scale: 0.95 } : {}}
        onClick={handleButtonClick}
        disabled={isDisabled || !user}
        animate={{
          opacity: (isDisabled || !user) ? 0.6 : 1,
        }}
      >
        <div className="flex items-center justify-center gap-2">
          {['initializing', 'processing', 'verifying'].includes(paymentState) && (
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
          )}
          <span>{getButtonText()}</span>
        </div>
      </motion.button>
      
      {/* Authentication required message */}
      {!user && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg"
        >
          <p className="text-sm text-blue-700 font-medium">
            Please log in to subscribe to this plan
          </p>
        </motion.div>
      )}
      
      {/* Error message display */}
      {currentError && paymentState === 'error' && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg"
        >
          <p className="text-sm text-red-700 font-medium">{currentError.message}</p>
          {currentError.details && (
            <p className="text-xs text-red-600 mt-1">{currentError.details}</p>
          )}
          {currentError.retryable && retryCount < maxRetries && (
            <p className="text-xs text-red-600 mt-2">
              Retry attempt {retryCount + 1} of {maxRetries}
            </p>
          )}
          {!currentError.retryable && (
            <p className="text-xs text-red-600 mt-2">
              Please contact support if you continue to experience issues.
            </p>
          )}
        </motion.div>
      )}

      {/* Success message display */}
      {paymentState === 'success' && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className="mt-2 p-3 bg-green-50 border border-green-200 rounded-lg"
        >
          <p className="text-sm text-green-700 font-medium">
            Payment successful! Redirecting to confirmation page...
          </p>
        </motion.div>
      )}

      {/* Processing state information */}
      {paymentState === 'verifying' && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg"
        >
          <p className="text-sm text-yellow-700 font-medium">
            Verifying your payment with our secure servers...
          </p>
          <p className="text-xs text-yellow-600 mt-1">
            This may take a few moments. Please do not close this page.
          </p>
        </motion.div>
      )}
    </div>
  );
};

export default SecurePaystackButton;
