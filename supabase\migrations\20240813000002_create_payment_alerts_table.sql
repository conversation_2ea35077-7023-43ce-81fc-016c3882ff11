-- Create payment alerts table for monitoring and alerting
CREATE TABLE IF NOT EXISTS payment_alerts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    alert_type TEXT NOT NULL,
    severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    metric_value DECIMAL(15,4),
    threshold_value DECIMAL(15,4),
    affected_count INTEGER,
    context JSONB,
    is_resolved BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    resolved_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_payment_alerts_alert_type ON payment_alerts(alert_type);
CREATE INDEX IF NOT EXISTS idx_payment_alerts_severity ON payment_alerts(severity);
CREATE INDEX IF NOT EXISTS idx_payment_alerts_is_resolved ON payment_alerts(is_resolved);
CREATE INDEX IF NOT EXISTS idx_payment_alerts_created_at ON payment_alerts(created_at DESC);

-- Create RLS policy for payment alerts
ALTER TABLE payment_alerts ENABLE ROW LEVEL SECURITY;

-- Allow service role to access all records
CREATE POLICY "Service role can access payment alerts" ON payment_alerts
    FOR ALL USING (auth.role() = 'service_role');

-- Grant necessary permissions
GRANT ALL ON payment_alerts TO service_role;