# Backup and Recovery Procedures for SecQuiz

This document outlines comprehensive backup and recovery procedures for the SecQuiz application and database.

## Database Backup Procedures

### Automated Daily Backups

#### PostgreSQL/Supabase Backup Script
```bash
#!/bin/bash
# backup-database.sh

# Configuration
DB_HOST="your-supabase-host"
DB_NAME="postgres"
DB_USER="postgres"
DB_PASSWORD="your-password"
BACKUP_DIR="/var/backups/secquiz"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="secquiz_backup_${DATE}.sql"
RETENTION_DAYS=30

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# Create database backup
echo "Starting database backup at $(date)"
PGPASSWORD=$DB_PASSWORD pg_dump \
    -h $DB_HOST \
    -U $DB_USER \
    -d $DB_NAME \
    --verbose \
    --clean \
    --if-exists \
    --create \
    --format=custom \
    --file="$BACKUP_DIR/$BACKUP_FILE"

if [ $? -eq 0 ]; then
    echo "Database backup completed successfully: $BACKUP_FILE"
    
    # Compress backup
    gzip "$BACKUP_DIR/$BACKUP_FILE"
    echo "Backup compressed: ${BACKUP_FILE}.gz"
    
    # Upload to cloud storage (optional)
    # aws s3 cp "$BACKUP_DIR/${BACKUP_FILE}.gz" s3://your-backup-bucket/database/
    
    # Clean up old backups
    find $BACKUP_DIR -name "secquiz_backup_*.sql.gz" -mtime +$RETENTION_DAYS -delete
    echo "Old backups cleaned up (older than $RETENTION_DAYS days)"
    
else
    echo "Database backup failed!"
    exit 1
fi
```

#### Cron Job Setup
```bash
# Add to crontab (crontab -e)
# Daily backup at 2 AM
0 2 * * * /path/to/backup-database.sh >> /var/log/secquiz/backup.log 2>&1

# Weekly full backup at 3 AM on Sundays
0 3 * * 0 /path/to/backup-database-full.sh >> /var/log/secquiz/backup.log 2>&1
```

### Manual Backup Commands

#### Full Database Backup
```bash
# Create full backup
pg_dump -h your-host -U postgres -d postgres \
    --clean --if-exists --create --format=custom \
    --file=secquiz_manual_backup_$(date +%Y%m%d).sql

# Create schema-only backup
pg_dump -h your-host -U postgres -d postgres \
    --schema-only --clean --if-exists \
    --file=secquiz_schema_$(date +%Y%m%d).sql

# Create data-only backup
pg_dump -h your-host -U postgres -d postgres \
    --data-only --column-inserts \
    --file=secquiz_data_$(date +%Y%m%d).sql
```

#### Specific Table Backups
```bash
# Backup critical tables only
pg_dump -h your-host -U postgres -d postgres \
    --table=subscriptions \
    --table=payment_transactions \
    --table=user_profiles \
    --data-only --column-inserts \
    --file=secquiz_critical_data_$(date +%Y%m%d).sql
```

### Cloud Storage Backup

#### AWS S3 Backup Script
```bash
#!/bin/bash
# s3-backup.sh

BACKUP_FILE="$1"
S3_BUCKET="your-backup-bucket"
S3_PREFIX="secquiz/database"

# Upload to S3
aws s3 cp "$BACKUP_FILE" "s3://$S3_BUCKET/$S3_PREFIX/"

if [ $? -eq 0 ]; then
    echo "Backup uploaded to S3: s3://$S3_BUCKET/$S3_PREFIX/$(basename $BACKUP_FILE)"
else
    echo "S3 upload failed!"
    exit 1
fi

# Set lifecycle policy for automatic cleanup
aws s3api put-object-lifecycle-configuration \
    --bucket $S3_BUCKET \
    --lifecycle-configuration file://s3-lifecycle.json
```

#### S3 Lifecycle Configuration (s3-lifecycle.json)
```json
{
    "Rules": [
        {
            "ID": "SecQuizBackupLifecycle",
            "Status": "Enabled",
            "Filter": {
                "Prefix": "secquiz/database/"
            },
            "Transitions": [
                {
                    "Days": 30,
                    "StorageClass": "STANDARD_IA"
                },
                {
                    "Days": 90,
                    "StorageClass": "GLACIER"
                }
            ],
            "Expiration": {
                "Days": 2555
            }
        }
    ]
}
```

## Application Code Backup

### Git Repository Backup
```bash
#!/bin/bash
# backup-code.sh

REPO_URL="https://github.com/your-org/secquiz.git"
BACKUP_DIR="/var/backups/secquiz/code"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p "$BACKUP_DIR/$DATE"

# Clone repository
git clone --mirror "$REPO_URL" "$BACKUP_DIR/$DATE/secquiz.git"

# Create archive
tar -czf "$BACKUP_DIR/secquiz_code_$DATE.tar.gz" -C "$BACKUP_DIR/$DATE" .

# Clean up
rm -rf "$BACKUP_DIR/$DATE"

echo "Code backup completed: secquiz_code_$DATE.tar.gz"
```

### Environment Configuration Backup
```bash
#!/bin/bash
# backup-config.sh

CONFIG_DIR="/var/backups/secquiz/config"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p "$CONFIG_DIR"

# Backup environment files (without sensitive data)
cp .env.example "$CONFIG_DIR/.env.example_$DATE"
cp server/.env.example "$CONFIG_DIR/server_.env.example_$DATE"

# Backup nginx configuration
cp /etc/nginx/sites-available/secquiz "$CONFIG_DIR/nginx_secquiz_$DATE"

# Backup SSL certificates info (not the actual certificates)
openssl x509 -in /etc/letsencrypt/live/your-domain.com/fullchain.pem -text -noout > "$CONFIG_DIR/ssl_cert_info_$DATE.txt"

echo "Configuration backup completed"
```

## Recovery Procedures

### Database Recovery

#### Full Database Restore
```bash
#!/bin/bash
# restore-database.sh

BACKUP_FILE="$1"
DB_HOST="your-supabase-host"
DB_NAME="postgres"
DB_USER="postgres"
DB_PASSWORD="your-password"

if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <backup_file>"
    exit 1
fi

# Verify backup file exists
if [ ! -f "$BACKUP_FILE" ]; then
    echo "Backup file not found: $BACKUP_FILE"
    exit 1
fi

# Create confirmation prompt
echo "WARNING: This will replace the current database with the backup."
echo "Backup file: $BACKUP_FILE"
echo "Target database: $DB_HOST/$DB_NAME"
read -p "Are you sure you want to continue? (yes/no): " confirm

if [ "$confirm" != "yes" ]; then
    echo "Restore cancelled"
    exit 0
fi

# Stop application services
echo "Stopping application services..."
systemctl stop secquiz-server
systemctl stop nginx

# Restore database
echo "Starting database restore..."
PGPASSWORD=$DB_PASSWORD pg_restore \
    -h $DB_HOST \
    -U $DB_USER \
    -d $DB_NAME \
    --verbose \
    --clean \
    --if-exists \
    --create \
    "$BACKUP_FILE"

if [ $? -eq 0 ]; then
    echo "Database restore completed successfully"
    
    # Start application services
    echo "Starting application services..."
    systemctl start secquiz-server
    systemctl start nginx
    
    echo "Recovery completed successfully"
else
    echo "Database restore failed!"
    exit 1
fi
```

#### Point-in-Time Recovery
```bash
#!/bin/bash
# point-in-time-recovery.sh

TARGET_TIME="$1"  # Format: YYYY-MM-DD HH:MM:SS
BASE_BACKUP="$2"

if [ -z "$TARGET_TIME" ] || [ -z "$BASE_BACKUP" ]; then
    echo "Usage: $0 <target_time> <base_backup_file>"
    echo "Example: $0 '2024-01-15 14:30:00' /backups/base_backup.sql"
    exit 1
fi

echo "Performing point-in-time recovery to: $TARGET_TIME"
echo "Using base backup: $BASE_BACKUP"

# This would require WAL (Write-Ahead Logging) to be enabled
# and archived for true point-in-time recovery
# For Supabase, this might require their backup/restore features

# Restore base backup first
./restore-database.sh "$BASE_BACKUP"

# Apply WAL files up to target time (if available)
# This is a simplified example - actual implementation depends on your setup
echo "Point-in-time recovery completed"
```

### Application Recovery

#### Service Recovery Script
```bash
#!/bin/bash
# recover-application.sh

RECOVERY_TYPE="$1"  # full, partial, config-only

case $RECOVERY_TYPE in
    "full")
        echo "Performing full application recovery..."
        
        # Stop services
        systemctl stop secquiz-server
        systemctl stop nginx
        
        # Restore code from backup
        cd /var/www/secquiz
        git fetch origin
        git reset --hard origin/main
        
        # Restore dependencies
        npm install
        cd server && npm install && cd ..
        
        # Rebuild application
        npm run build
        
        # Restore configuration (manual step required)
        echo "Please restore environment variables manually"
        echo "Template files are available in /var/backups/secquiz/config/"
        
        # Start services
        systemctl start secquiz-server
        systemctl start nginx
        ;;
        
    "partial")
        echo "Performing partial application recovery..."
        # Implement partial recovery logic
        ;;
        
    "config-only")
        echo "Performing configuration recovery..."
        # Restore only configuration files
        ;;
        
    *)
        echo "Usage: $0 {full|partial|config-only}"
        exit 1
        ;;
esac
```

## Disaster Recovery Plan

### Recovery Time Objectives (RTO)
- **Critical Systems**: 4 hours
- **Database**: 2 hours
- **Application**: 1 hour
- **Full Service**: 6 hours

### Recovery Point Objectives (RPO)
- **Database**: 24 hours (daily backups)
- **Critical Data**: 1 hour (if real-time replication is set up)
- **Configuration**: 24 hours

### Disaster Recovery Steps

#### 1. Assessment Phase
```bash
# Check system status
./check-system-health.sh

# Assess damage
./assess-damage.sh

# Determine recovery strategy
./determine-recovery-strategy.sh
```

#### 2. Recovery Phase
```bash
# Execute recovery plan
./execute-recovery-plan.sh

# Verify system integrity
./verify-system-integrity.sh

# Resume operations
./resume-operations.sh
```

### Emergency Contacts
- **Database Administrator**: [contact info]
- **DevOps Team**: [contact info]
- **Application Team**: [contact info]
- **Hosting Provider Support**: [contact info]

## Testing and Validation

### Backup Validation Script
```bash
#!/bin/bash
# validate-backup.sh

BACKUP_FILE="$1"

if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <backup_file>"
    exit 1
fi

# Test backup integrity
echo "Testing backup file integrity..."
pg_restore --list "$BACKUP_FILE" > /dev/null

if [ $? -eq 0 ]; then
    echo "✅ Backup file is valid"
else
    echo "❌ Backup file is corrupted"
    exit 1
fi

# Test restore to temporary database (if test environment available)
echo "Testing restore process..."
# Implementation depends on your test environment setup

echo "Backup validation completed"
```

### Recovery Testing Schedule
- **Monthly**: Test database restore to staging environment
- **Quarterly**: Full disaster recovery drill
- **Annually**: Complete infrastructure recovery test

## Monitoring and Alerting

### Backup Monitoring
```bash
#!/bin/bash
# monitor-backups.sh

BACKUP_DIR="/var/backups/secquiz"
ALERT_EMAIL="<EMAIL>"

# Check if backup was created today
TODAY=$(date +%Y%m%d)
BACKUP_COUNT=$(find $BACKUP_DIR -name "*$TODAY*" | wc -l)

if [ $BACKUP_COUNT -eq 0 ]; then
    echo "❌ No backup found for today" | mail -s "SecQuiz Backup Alert" $ALERT_EMAIL
else
    echo "✅ Backup found for today"
fi

# Check backup file sizes
find $BACKUP_DIR -name "*.sql.gz" -mtime -1 -size -1M -exec echo "⚠️  Small backup file: {}" \;
```

### Health Check Integration
```javascript
// Add to your health check endpoint
app.get('/api/health', async (req, res) => {
    const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        services: {
            database: 'connected',
            paystack: 'configured',
            environment: 'valid'
        },
        backup: {
            last_backup: await getLastBackupTime(),
            backup_status: await checkBackupStatus()
        }
    };
    
    res.json(health);
});
```

## Documentation Maintenance

### Backup Log Format
```
[TIMESTAMP] [LEVEL] [COMPONENT] Message
2024-01-15 02:00:01 INFO BACKUP Starting daily database backup
2024-01-15 02:05:23 INFO BACKUP Backup completed: secquiz_backup_20240115_020001.sql.gz
2024-01-15 02:05:45 INFO BACKUP Uploaded to S3: s3://backup-bucket/secquiz/database/
2024-01-15 02:06:12 INFO BACKUP Cleanup completed: removed 3 old backups
```

### Recovery Log Template
```
Recovery Event: [EVENT_ID]
Date/Time: [TIMESTAMP]
Incident: [DESCRIPTION]
Recovery Type: [full/partial/config-only]
RTO Target: [TIME]
RTO Actual: [TIME]
RPO Target: [TIME]
RPO Actual: [TIME]
Data Loss: [YES/NO - DESCRIPTION]
Lessons Learned: [NOTES]
```

Remember to:
1. Test all backup and recovery procedures regularly
2. Keep recovery documentation up to date
3. Train team members on recovery procedures
4. Monitor backup success and failures
5. Review and update RTO/RPO targets based on business needs