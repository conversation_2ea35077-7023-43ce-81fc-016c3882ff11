-- Emergency Admin Fix Script - CORRECTED VERSION
-- This script fixes the ambiguous column reference issue in get_all_user_profiles function
-- Run this script to fix critical admin dashboard issues

-- Step 1: Drop existing problematic objects
DROP FUNCTION IF EXISTS public.get_all_user_profiles() CASCADE;
DROP FUNCTION IF EXISTS public.delete_user(UUID) CASCADE;
DROP FUNCTION IF EXISTS public.is_admin() CASCADE;
DROP FUNCTION IF EXISTS public.execute_sql(text) CASCADE;

-- Step 2: Create user_profiles table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.user_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
  email TEXT,
  full_name TEXT,
  is_subscribed BOOLEAN DEFAULT false,
  is_admin BOOLEAN DEFAULT false,
  subscription_expires_at TIMESTAMPTZ,
  subscription_status TEXT DEFAULT 'free',
  subscription_plan TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Step 3: Enable RLS and create policies
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Admins can manage all profiles" ON public.user_profiles;

-- Users can view their own profile
CREATE POLICY "Users can view their own profile"
  ON public.user_profiles FOR SELECT
  USING (auth.uid() = user_id);

-- Users can update their own profile
CREATE POLICY "Users can update their own profile"
  ON public.user_profiles FOR UPDATE
  USING (auth.uid() = user_id);

-- Admins can manage all profiles - FIXED VERSION
CREATE POLICY "Admins can manage all profiles"
  ON public.user_profiles FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles up_check
      WHERE up_check.user_id = auth.uid() AND up_check.is_admin = true
    )
  );

-- Step 4: Create admin_users table for backward compatibility
CREATE TABLE IF NOT EXISTS public.admin_users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
  is_admin BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Enable RLS for admin_users
ALTER TABLE public.admin_users ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Admins can view admin_users" ON public.admin_users;
DROP POLICY IF EXISTS "Admins can manage admin_users" ON public.admin_users;

-- Admins can view admin_users - FIXED VERSION
CREATE POLICY "Admins can view admin_users"
  ON public.admin_users FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles up_check
      WHERE up_check.user_id = auth.uid() AND up_check.is_admin = true
    )
  );

-- Admins can manage admin_users - FIXED VERSION
CREATE POLICY "Admins can manage admin_users"
  ON public.admin_users FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles up_check
      WHERE up_check.user_id = auth.uid() AND up_check.is_admin = true
    )
  );

-- Step 5: Create other required tables
CREATE TABLE IF NOT EXISTS public.subscriptions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
  plan_id TEXT NOT NULL,
  amount_paid DECIMAL(10,2),
  start_date TIMESTAMPTZ DEFAULT now(),
  end_date TIMESTAMPTZ,
  is_active BOOLEAN DEFAULT true,
  last_payment_reference TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

CREATE TABLE IF NOT EXISTS public.payments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending',
  provider_payment_id TEXT,
  metadata JSONB,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Step 6: Backfill user_profiles with existing users
INSERT INTO public.user_profiles (user_id, email, full_name, is_subscribed, is_admin)
SELECT
  au.id as user_id,
  au.email,
  au.raw_user_meta_data->>'full_name' as full_name,
  false as is_subscribed,
  false as is_admin
FROM auth.users au
WHERE au.id NOT IN (SELECT user_id FROM public.user_profiles)
ON CONFLICT (user_id) DO UPDATE SET
  email = COALESCE(EXCLUDED.email, user_profiles.email),
  full_name = COALESCE(EXCLUDED.full_name, user_profiles.full_name),
  updated_at = now();

-- Step 7: Set admin users
UPDATE public.user_profiles
SET is_admin = true, updated_at = now()
WHERE user_id IN (
  SELECT id FROM auth.users
  WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')
);

-- Step 8: Sync admin_users table
INSERT INTO public.admin_users (user_id, is_admin)
SELECT user_id, true
FROM public.user_profiles
WHERE is_admin = true
ON CONFLICT (user_id) DO NOTHING;

-- Step 9: Create is_admin function - FIXED VERSION
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.user_profiles up_check
    WHERE up_check.user_id = auth.uid() AND up_check.is_admin = true
  );
END;
$$ LANGUAGE plpgsql;

-- Step 10: Create get_all_user_profiles function - FIXED VERSION
CREATE OR REPLACE FUNCTION public.get_all_user_profiles()
RETURNS TABLE (
  id UUID,
  user_id UUID,
  email TEXT,
  full_name TEXT,
  is_subscribed BOOLEAN,
  is_admin BOOLEAN,
  subscription_expires_at TIMESTAMPTZ,
  subscription_status TEXT,
  subscription_plan TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  last_sign_in_at TIMESTAMPTZ
) AS $$
BEGIN
  -- Check if current user is admin - FIXED VERSION
  IF NOT EXISTS (
    SELECT 1 FROM public.user_profiles up_check
    WHERE up_check.user_id = auth.uid() AND up_check.is_admin = true
  ) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;

  -- Return all user profiles with auth data
  RETURN QUERY
  SELECT
    up.id,
    up.user_id,
    up.email,
    up.full_name,
    up.is_subscribed,
    up.is_admin,
    up.subscription_expires_at,
    up.subscription_status,
    up.subscription_plan,
    up.created_at,
    up.updated_at,
    au.last_sign_in_at
  FROM public.user_profiles up
  LEFT JOIN auth.users au ON up.user_id = au.id
  ORDER BY up.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 11: Create delete_user function - FIXED VERSION
CREATE OR REPLACE FUNCTION public.delete_user(target_user_id UUID)
RETURNS JSON
SECURITY DEFINER
AS $$
BEGIN
  -- Check if current user is admin - FIXED VERSION
  IF NOT EXISTS (
    SELECT 1 FROM public.user_profiles up_check
    WHERE up_check.user_id = auth.uid() AND up_check.is_admin = true
  ) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;

  -- Delete related records first
  DELETE FROM public.admin_users WHERE user_id = target_user_id;
  DELETE FROM public.subscriptions WHERE user_id = target_user_id;
  DELETE FROM public.payments WHERE user_id = target_user_id;
  DELETE FROM public.user_profiles WHERE user_id = target_user_id;
  
  -- Delete from auth.users (this will cascade to other tables)
  DELETE FROM auth.users WHERE id = target_user_id;
  
  RETURN json_build_object('success', true, 'message', 'User deleted successfully');
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object('success', false, 'error', SQLERRM);
END;
$$ LANGUAGE plpgsql;

-- Step 12: Create execute_sql function for admin tools
CREATE OR REPLACE FUNCTION public.execute_sql(query text)
RETURNS JSON
SECURITY DEFINER
AS $$
DECLARE
  result JSON;
BEGIN
  -- Check if current user is admin - FIXED VERSION
  IF NOT EXISTS (
    SELECT 1 FROM public.user_profiles up_check
    WHERE up_check.user_id = auth.uid() AND up_check.is_admin = true
  ) THEN
    RETURN json_build_object('success', false, 'error', 'Permission denied: Admin access required');
  END IF;

  -- Execute the query
  EXECUTE query;
  
  RETURN json_build_object('success', true, 'message', 'Query executed successfully');
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object('success', false, 'error', SQLERRM);
END;
$$ LANGUAGE plpgsql;

-- Step 13: Grant execute permissions
GRANT EXECUTE ON FUNCTION public.get_all_user_profiles() TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION public.delete_user(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.execute_sql(text) TO authenticated;

-- Step 14: Create triggers for new users and email sync
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.user_profiles (user_id, email, full_name, is_subscribed, is_admin)
  VALUES (
    NEW.id, 
    NEW.email,
    NEW.raw_user_meta_data->>'full_name',
    false, 
    false
  )
  ON CONFLICT (user_id) DO UPDATE SET
    email = EXCLUDED.email,
    full_name = COALESCE(EXCLUDED.full_name, user_profiles.full_name),
    updated_at = now();
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create email sync function
CREATE OR REPLACE FUNCTION public.sync_user_email()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE public.user_profiles 
  SET email = NEW.email, updated_at = now()
  WHERE user_id = NEW.id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS sync_auth_user_email ON auth.users;
CREATE TRIGGER sync_auth_user_email
  AFTER UPDATE OF email ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.sync_user_email();

-- Step 15: Final sync of all user emails
UPDATE public.user_profiles 
SET email = au.email, updated_at = now()
FROM auth.users au 
WHERE user_profiles.user_id = au.id 
AND (user_profiles.email IS NULL OR user_profiles.email != au.email);

-- Step 16: Verification queries (these will show results if successful)
SELECT 'SUCCESS: user_profiles table created' as status, count(*) as user_count FROM public.user_profiles;
SELECT 'SUCCESS: Admin users found' as status, email FROM public.user_profiles up JOIN auth.users au ON up.user_id = au.id WHERE up.is_admin = true;
SELECT 'SUCCESS: Functions created' as status, proname FROM pg_proc WHERE proname IN ('get_all_user_profiles', 'is_admin', 'delete_user', 'execute_sql');