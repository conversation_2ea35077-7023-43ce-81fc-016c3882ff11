import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import PaystackButton from '@/components/PaystackButton';
import * as useAuthModule from '@/hooks/use-auth';
import * as paystackUtils from '@/utils/paystack';
import { usePaystackPayment } from 'react-paystack';

// Mock the hooks and modules
vi.mock('@/hooks/use-auth', async () => {
  const actual = await vi.importActual('@/hooks/use-auth');
  return {
    ...actual,
    useAuth: vi.fn(),
  };
});

vi.mock('react-paystack', () => ({
  usePaystackPayment: vi.fn(),
}));

vi.mock('framer-motion', () => ({
  motion: {
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}));

vi.mock('sonner', () => ({
  toast: {
    error: vi.fn(),
    success: vi.fn(),
    info: vi.fn(),
  },
}));

vi.mock('@/utils/paystack', async () => {
  const actual = await vi.importActual('@/utils/paystack');
  return {
    ...actual,
    PAYSTACK_PUBLIC_KEY: 'pk_test_123456789',
    generateReference: vi.fn().mockReturnValue('test_ref_123'),
    toKobo: vi.fn().mockImplementation((amount) => amount * 100),
    handlePaymentSuccess: vi.fn(),
    handlePaymentClose: vi.fn(),
  };
});

describe('PaystackButton component', () => {
  const mockPlan = {
    id: 'basic',
    name: 'Basic',
    amount: 5000,
    interval: 'weekly' as const,
    features: ['Feature 1', 'Feature 2'],
  };

  const mockUser = {
    id: '123',
    email: '<EMAIL>',
    name: 'Test User'
  };

  const mockInitializePayment = vi.fn();
  const mockHandlePaymentSuccess = vi.fn();
  const mockOnPaymentStart = vi.fn();
  const mockOnPaymentSuccess = vi.fn();
  const mockOnPaymentError = vi.fn();

  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks();

    // Default mock implementations
    vi.mocked(useAuthModule.useAuth).mockReturnValue({
      user: mockUser as any,
      session: {} as any,
      isLoading: false,
      signIn: vi.fn(),
      signUp: vi.fn(),
      signOut: vi.fn(),
    });

    vi.mocked(usePaystackPayment).mockReturnValue(mockInitializePayment);
    vi.mocked(paystackUtils.handlePaymentSuccess).mockImplementation(mockHandlePaymentSuccess);

    // Clear any existing timers
    vi.clearAllTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('Rendering and Basic Functionality', () => {
    it('renders with default button text', () => {
      render(<PaystackButton plan={mockPlan} />);
      expect(screen.getByText('Select')).toBeInTheDocument();
    });

    it('renders with custom button text', () => {
      render(<PaystackButton plan={mockPlan} buttonText="Custom Text" />);
      expect(screen.getByText('Custom Text')).toBeInTheDocument();
    });

    it('applies custom className when provided', () => {
      render(<PaystackButton plan={mockPlan} className="custom-class" />);
      const button = screen.getByRole('button');
      expect(button).toHaveClass('custom-class');
    });

    it('shows disabled state when user is not authenticated', () => {
      vi.mocked(useAuthModule.useAuth).mockReturnValue({
        user: null,
        session: null,
        isLoading: false,
        signIn: vi.fn(),
        signUp: vi.fn(),
        signOut: vi.fn(),
      });

      render(<PaystackButton plan={mockPlan} />);
      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
    });
  });

  describe('Payment Configuration', () => {
    it('configures Paystack with correct parameters', () => {
      render(<PaystackButton plan={mockPlan} />);
      
      const button = screen.getByText('Select');
      fireEvent.click(button);

      expect(usePaystackPayment).toHaveBeenCalledWith({
        reference: 'test_ref_123',
        email: '<EMAIL>',
        amount: 500000, // 5000 * 100 (toKobo)
        publicKey: 'pk_test_123456789',
        currency: 'NGN',
        metadata: {
          custom_fields: [
            {
              display_name: 'Plan',
              variable_name: 'plan',
              value: 'basic',
            },
          ],
        },
      });
    });

    it('generates fresh reference for each payment attempt', () => {
      vi.mocked(paystackUtils.generateReference)
        .mockReturnValueOnce('ref_1')
        .mockReturnValueOnce('ref_2');

      const { rerender } = render(<PaystackButton plan={mockPlan} />);
      
      fireEvent.click(screen.getByText('Select'));
      expect(paystackUtils.generateReference).toHaveBeenCalledTimes(1);

      rerender(<PaystackButton plan={mockPlan} />);
      fireEvent.click(screen.getByText('Select'));
      expect(paystackUtils.generateReference).toHaveBeenCalledTimes(2);
    });
  });

  describe('Payment States', () => {
    it('shows initializing state when payment starts', async () => {
      render(<PaystackButton plan={mockPlan} />);
      
      const button = screen.getByText('Select');
      fireEvent.click(button);

      await waitFor(() => {
        expect(screen.getByText('Initializing...')).toBeInTheDocument();
      });
    });

    it('shows processing state after initialization', async () => {
      render(<PaystackButton plan={mockPlan} />);
      
      const button = screen.getByText('Select');
      fireEvent.click(button);

      await waitFor(() => {
        expect(screen.getByText('Processing Payment...')).toBeInTheDocument();
      });
    });

    it('calls onPaymentStart callback when payment begins', () => {
      render(
        <PaystackButton 
          plan={mockPlan} 
          onPaymentStart={mockOnPaymentStart}
        />
      );
      
      const button = screen.getByText('Select');
      fireEvent.click(button);

      expect(mockOnPaymentStart).toHaveBeenCalledTimes(1);
    });

    it('initializes payment when clicked', () => {
      render(<PaystackButton plan={mockPlan} />);

      const button = screen.getByText('Select');
      fireEvent.click(button);

      expect(mockInitializePayment).toHaveBeenCalledTimes(1);
    });
  });

  describe('Error Handling', () => {
    it('shows authentication error when user is not logged in', async () => {
      vi.mocked(useAuthModule.useAuth).mockReturnValue({
        user: null,
        session: null,
        isLoading: false,
        signIn: vi.fn(),
        signUp: vi.fn(),
        signOut: vi.fn(),
      });

      render(<PaystackButton plan={mockPlan} />);
      
      const button = screen.getByText('Select');
      fireEvent.click(button);

      await waitFor(() => {
        expect(screen.getByText(/Please log in to subscribe/)).toBeInTheDocument();
      });
    });

    it('handles payment initialization errors', async () => {
      const mockError = new Error('Initialization failed');
      vi.mocked(usePaystackPayment).mockImplementation(() => {
        throw mockError;
      });

      render(
        <PaystackButton 
          plan={mockPlan} 
          onPaymentError={mockOnPaymentError}
        />
      );
      
      const button = screen.getByText('Select');
      fireEvent.click(button);

      await waitFor(() => {
        expect(mockOnPaymentError).toHaveBeenCalledWith(
          expect.objectContaining({
            type: 'INITIALIZATION_FAILED',
            message: 'Failed to initialize payment',
            retryable: true
          })
        );
      });
    });

    it('shows retry button for retryable errors', async () => {
      const mockError = new Error('Network error');
      vi.mocked(usePaystackPayment).mockImplementation(() => {
        throw mockError;
      });

      render(<PaystackButton plan={mockPlan} />);
      
      const button = screen.getByText('Select');
      fireEvent.click(button);

      await waitFor(() => {
        expect(screen.getByText('Retry Payment')).toBeInTheDocument();
      });
    });

    it('respects maximum retry attempts', async () => {
      const mockError = new Error('Network error');
      vi.mocked(usePaystackPayment).mockImplementation(() => {
        throw mockError;
      });

      render(<PaystackButton plan={mockPlan} maxRetries={2} />);
      
      const button = screen.getByText('Select');
      
      // First attempt
      fireEvent.click(button);
      await waitFor(() => screen.getByText('Retry Payment'));
      
      // Second attempt (retry 1)
      fireEvent.click(screen.getByText('Retry Payment'));
      await waitFor(() => screen.getByText('Retry Payment'));
      
      // Third attempt (retry 2)
      fireEvent.click(screen.getByText('Retry Payment'));
      await waitFor(() => screen.getByText('Retry Payment'));
      
      // Fourth attempt should show max retries reached
      fireEvent.click(screen.getByText('Retry Payment'));
      
      // Should not allow more retries
      expect(screen.queryByText('Retry Payment')).toBeInTheDocument();
    });
  });

  describe('Payment Success Flow', () => {
    it('handles successful payment verification', async () => {
      vi.useFakeTimers();
      
      mockHandlePaymentSuccess.mockResolvedValue({
        success: true,
        message: 'Payment successful',
        reference: 'test_ref_123'
      });

      render(
        <PaystackButton 
          plan={mockPlan} 
          onPaymentSuccess={mockOnPaymentSuccess}
        />
      );
      
      const button = screen.getByText('Select');
      fireEvent.click(button);

      // Simulate successful payment callback
      const paymentResponse = { reference: 'test_ref_123' };
      const successCallback = mockInitializePayment.mock.calls[0][0];
      
      await act(async () => {
        await successCallback(paymentResponse);
      });

      await waitFor(() => {
        expect(mockHandlePaymentSuccess).toHaveBeenCalledWith(
          'test_ref_123',
          'basic',
          '<EMAIL>'
        );
      });

      await waitFor(() => {
        expect(mockOnPaymentSuccess).toHaveBeenCalledWith('test_ref_123');
      });

      await waitFor(() => {
        expect(screen.getByText('Payment Successful!')).toBeInTheDocument();
      });
    });

    it('handles payment verification timeout', async () => {
      vi.useFakeTimers();
      
      // Mock a hanging promise
      mockHandlePaymentSuccess.mockImplementation(() => 
        new Promise(() => {}) // Never resolves
      );

      render(<PaystackButton plan={mockPlan} />);
      
      const button = screen.getByText('Select');
      fireEvent.click(button);

      // Simulate successful payment callback
      const paymentResponse = { reference: 'test_ref_123' };
      const successCallback = mockInitializePayment.mock.calls[0][0];
      
      act(() => {
        successCallback(paymentResponse);
      });

      // Fast-forward time to trigger timeout
      act(() => {
        vi.advanceTimersByTime(30000);
      });

      await waitFor(() => {
        expect(screen.getByText(/Payment verification timed out/)).toBeInTheDocument();
      });
    });

    it('handles payment verification failure', async () => {
      mockHandlePaymentSuccess.mockResolvedValue({
        success: false,
        message: 'Verification failed',
        error: 'Invalid reference'
      });

      render(<PaystackButton plan={mockPlan} />);
      
      const button = screen.getByText('Select');
      fireEvent.click(button);

      // Simulate successful payment callback
      const paymentResponse = { reference: 'test_ref_123' };
      const successCallback = mockInitializePayment.mock.calls[0][0];
      
      await act(async () => {
        await successCallback(paymentResponse);
      });

      await waitFor(() => {
        expect(screen.getByText(/Verification failed/)).toBeInTheDocument();
      });
    });
  });

  describe('Payment Cancellation', () => {
    it('handles payment window closure', async () => {
      render(<PaystackButton plan={mockPlan} />);
      
      const button = screen.getByText('Select');
      fireEvent.click(button);

      // Simulate payment closure callback
      const closeCallback = mockInitializePayment.mock.calls[0][1];
      
      act(() => {
        closeCallback();
      });

      await waitFor(() => {
        expect(screen.getByText(/Payment was cancelled/)).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility and UX', () => {
    it('disables button during payment processing', async () => {
      render(<PaystackButton plan={mockPlan} />);
      
      const button = screen.getByRole('button');
      fireEvent.click(button);

      await waitFor(() => {
        expect(button).toBeDisabled();
      });
    });

    it('shows loading spinner during processing states', async () => {
      render(<PaystackButton plan={mockPlan} />);
      
      const button = screen.getByText('Select');
      fireEvent.click(button);

      await waitFor(() => {
        const spinner = screen.getByRole('button').querySelector('.animate-spin');
        expect(spinner).toBeInTheDocument();
      });
    });

    it('provides appropriate ARIA labels and states', () => {
      render(<PaystackButton plan={mockPlan} />);
      
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('type', 'button');
    });
  });

  describe('Edge Cases', () => {
    it('handles missing user email gracefully', async () => {
      vi.mocked(useAuthModule.useAuth).mockReturnValue({
        user: { id: '123', email: undefined } as any,
        session: {} as any,
        isLoading: false,
        signIn: vi.fn(),
        signUp: vi.fn(),
        signOut: vi.fn(),
      });

      render(<PaystackButton plan={mockPlan} />);
      
      const button = screen.getByText('Select');
      fireEvent.click(button);

      await waitFor(() => {
        expect(screen.getByText(/User authentication required/)).toBeInTheDocument();
      });
    });

    it('handles invalid plan configuration', () => {
      const invalidPlan = { ...mockPlan, amount: -100 };
      
      render(<PaystackButton plan={invalidPlan} />);
      
      const button = screen.getByText('Select');
      expect(button).toBeInTheDocument();
      
      // Should still render but may fail on payment initialization
      fireEvent.click(button);
    });

    it('cleans up timeouts on component unmount', () => {
      const { unmount } = render(<PaystackButton plan={mockPlan} />);
      
      const button = screen.getByText('Select');
      fireEvent.click(button);
      
      // Unmount component
      unmount();
      
      // Should not cause memory leaks or errors
      expect(() => {
        vi.runAllTimers();
      }).not.toThrow();
    });
  });
});
