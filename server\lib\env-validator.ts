import { configManager } from './config.js';

export interface ValidationError {
  variable: string;
  message: string;
  severity: 'error' | 'warning';
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
}

class EnvironmentValidator {
  private requiredServerVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'SUPABASE_SERVICE_ROLE_KEY',
    'PAYSTACK_SECRET_KEY',
  ];

  private requiredClientVars = [
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_ANON_KEY',
    'VITE_PAYSTACK_PUBLIC_KEY',
  ];

  private optionalVars = [
    'PAYSTACK_WEBHOOK_SECRET',
    'VITE_API_URL',
    'VITE_APP_URL',
    'PORT',
    'NODE_ENV',
  ];

  public validateEnvironment(): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];

    // Validate required server variables
    this.requiredServerVars.forEach(varName => {
      const value = process.env[varName];
      if (!value || value.trim() === '') {
        errors.push({
          variable: varName,
          message: `Required environment variable ${varName} is missing or empty`,
          severity: 'error',
        });
      } else if (this.isPlaceholderValue(value)) {
        errors.push({
          variable: varName,
          message: `Environment variable ${varName} contains placeholder value`,
          severity: 'error',
        });
      }
    });

    // Validate required client variables
    this.requiredClientVars.forEach(varName => {
      const value = process.env[varName];
      if (!value || value.trim() === '') {
        errors.push({
          variable: varName,
          message: `Required environment variable ${varName} is missing or empty`,
          severity: 'error',
        });
      } else if (this.isPlaceholderValue(value)) {
        errors.push({
          variable: varName,
          message: `Environment variable ${varName} contains placeholder value`,
          severity: 'error',
        });
      }
    });

    // Check optional variables for placeholder values
    this.optionalVars.forEach(varName => {
      const value = process.env[varName];
      if (value && this.isPlaceholderValue(value)) {
        warnings.push({
          variable: varName,
          message: `Optional environment variable ${varName} contains placeholder value`,
          severity: 'warning',
        });
      }
    });

    // Validate specific configurations
    this.validatePaystackConfig(errors, warnings);
    this.validateSupabaseConfig(errors, warnings);
    this.validateApiConfig(errors, warnings);

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  private isPlaceholderValue(value: string): boolean {
    const placeholders = [
      'your_',
      'placeholder',
      'example',
      'test_key',
      'dummy',
      'changeme',
      'replace_this',
    ];
    
    const lowerValue = value.toLowerCase();
    return placeholders.some(placeholder => lowerValue.includes(placeholder));
  }

  private validatePaystackConfig(errors: ValidationError[], _warnings: ValidationError[]): void {
    const config = configManager.getPaystackConfig();
    
    // Check if Paystack keys are properly formatted
    if (config.secretKey && !config.secretKey.startsWith('sk_')) {
      errors.push({
        variable: 'PAYSTACK_SECRET_KEY',
        message: 'Paystack secret key should start with "sk_"',
        severity: 'error',
      });
    }

    if (config.publicKey && !config.publicKey.startsWith('pk_')) {
      errors.push({
        variable: 'VITE_PAYSTACK_PUBLIC_KEY',
        message: 'Paystack public key should start with "pk_"',
        severity: 'error',
      });
    }

    // Check for test vs live key consistency
    const isSecretTest = config.secretKey.includes('_test_');
    const isPublicTest = config.publicKey.includes('_test_');
    
    if (isSecretTest !== isPublicTest) {
      _warnings.push({
        variable: 'PAYSTACK_KEYS',
        message: 'Paystack secret and public keys appear to be from different environments (test/live)',
        severity: 'warning',
      });
    }

    if (!config.webhookSecret) {
      _warnings.push({
        variable: 'PAYSTACK_WEBHOOK_SECRET',
        message: 'Paystack webhook secret is not set - webhook signature verification will be skipped',
        severity: 'warning',
      });
    }
  }

  private validateSupabaseConfig(errors: ValidationError[], warnings: ValidationError[]): void {
    const config = configManager.getSupabaseConfig();
    
    // Check if Supabase URL is properly formatted
    if (config.url && !config.url.includes('.supabase.co')) {
      warnings.push({
        variable: 'SUPABASE_URL',
        message: 'Supabase URL does not appear to be a valid Supabase URL',
        severity: 'warning',
      });
    }

    // Check if service role key is properly formatted
    if (config.serviceRoleKey && !config.serviceRoleKey.startsWith('eyJ')) {
      errors.push({
        variable: 'SUPABASE_SERVICE_ROLE_KEY',
        message: 'Supabase service role key does not appear to be a valid JWT token',
        severity: 'error',
      });
    }

    // Check if anon key is properly formatted
    if (config.anonKey && !config.anonKey.startsWith('eyJ')) {
      errors.push({
        variable: 'VITE_SUPABASE_ANON_KEY',
        message: 'Supabase anon key does not appear to be a valid JWT token',
        severity: 'error',
      });
    }
  }

  private validateApiConfig(errors: ValidationError[], _warnings: ValidationError[]): void {
    const serverConfig = configManager.getServerConfig();
    
    // Check if port is valid
    if (serverConfig.port < 1 || serverConfig.port > 65535) {
      errors.push({
        variable: 'PORT',
        message: `Invalid port number: ${serverConfig.port}. Must be between 1 and 65535`,
        severity: 'error',
      });
    }

    // Check if URLs are properly formatted
    if (serverConfig.api.url && !this.isValidUrl(serverConfig.api.url)) {
      errors.push({
        variable: 'VITE_API_URL',
        message: 'API URL is not a valid URL format',
        severity: 'error',
      });
    }

    if (serverConfig.api.appUrl && !this.isValidUrl(serverConfig.api.appUrl)) {
      errors.push({
        variable: 'VITE_APP_URL',
        message: 'App URL is not a valid URL format',
        severity: 'error',
      });
    }
  }

  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  public printValidationResults(result: ValidationResult): void {
    // Import secureLogger here to avoid circular dependencies
    const { secureLogger } = require('./secure-logger.js');
    
    if (result.isValid) {
      secureLogger.info('Environment validation passed');
    } else {
      secureLogger.error('Environment validation failed');
    }

    if (result.errors.length > 0) {
      secureLogger.error('Environment validation errors:', result.errors);
    }

    if (result.warnings.length > 0) {
      secureLogger.warn('Environment validation warnings:', result.warnings);
    }

    if (!result.isValid) {
      secureLogger.error('Please check your environment variables and try again. Refer to .env.example files for the required format.');
    }
  }
}

export const environmentValidator = new EnvironmentValidator();