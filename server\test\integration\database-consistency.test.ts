import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mockSupabaseClient } from '../setup.js';

// Import the services for testing
import {
  updateUserSubscription,
  getSubscriptionStatus,
  checkAndUpdateExpiredSubscriptions,
  cleanupExpiredSubscriptionsWithRetry,
  updateSubscriptionStatusAtomic,
  validateSubscriptionData
} from '../../services/subscription.js';

describe('Database Consistency Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Transaction Atomicity', () => {
    it('should maintain atomicity during payment processing', async () => {
      const testEmail = '<EMAIL>';
      const testUserId = 'user_atomic_123';
      const testReference = 'atomic_ref_123';

      // Mock successful user lookup
      mockSupabaseClient.auth.admin.listUsers.mockResolvedValue({
        data: {
          users: [{ id: testUserId, email: testEmail }]
        },
        error: null
      });

      // Mock atomic function success
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          user_id: testUserId,
          plan_id: 'basic',
          end_date: '2024-12-31T00:00:00Z',
          payment_id: 'pay_atomic_123',
          subscription_id: 'sub_atomic_123',
          reference: testReference
        },
        error: null
      });

      const result = await updateUserSubscription(
        testEmail,
        'basic',
        99800,
        testReference
      );

      expect(result.success).toBe(true);
      expect(result.data).toMatchObject({
        userId: testUserId,
        planId: 'basic',
        reference: testReference
      });

      // Verify atomic function was called with correct parameters
      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith('handle_payment_success_atomic', {
        p_user_id: testUserId,
        p_plan_id: 'basic',
        p_amount: 998, // Converted from kobo
        p_reference: testReference,
        p_currency: 'NGN'
      });
    });

    it('should handle partial transaction failures gracefully', async () => {
      const testEmail = '<EMAIL>';
      const testUserId = 'user_partial_fail';

      // Mock successful user lookup
      mockSupabaseClient.auth.admin.listUsers.mockResolvedValue({
        data: {
          users: [{ id: testUserId, email: testEmail }]
        },
        error: null
      });

      // Mock atomic function failure (e.g., constraint violation)
      mockSupabaseClient.rpc.mockResolvedValue({
        data: null,
        error: {
          message: 'duplicate key value violates unique constraint "payment_transactions_reference_key"',
          code: '23505'
        }
      });

      const result = await updateUserSubscription(
        testEmail,
        'basic',
        99800,
        'duplicate_ref_123'
      );

      expect(result.success).toBe(false);
      expect(result.errorCode).toBe('DATABASE_ERROR');
      expect(result.error).toContain('Database operation failed');
    });

    it('should rollback on atomic function timeout', async () => {
      const testEmail = '<EMAIL>';
      const testUserId = 'user_timeout';

      // Mock successful user lookup
      mockSupabaseClient.auth.admin.listUsers.mockResolvedValue({
        data: {
          users: [{ id: testUserId, email: testEmail }]
        },
        error: null
      });

      // Mock timeout error
      mockSupabaseClient.rpc.mockRejectedValue(new Error('Connection timeout'));

      const result = await updateUserSubscription(
        testEmail,
        'basic',
        99800,
        'timeout_ref_123'
      );

      expect(result.success).toBe(false);
      expect(result.errorCode).toBe('UNKNOWN_ERROR');
    });
  });

  describe('Data Consistency Across Tables', () => {
    it('should maintain consistency between subscriptions and user_profiles', async () => {
      const testUserId = 'user_consistency_123';

      // Mock comprehensive status check
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          profile: {
            is_subscribed: true,
            subscription_status: 'active',
            subscription_expires_at: '2024-12-31T00:00:00Z',
            subscription_plan: 'pro'
          },
          subscription: {
            is_expired: false,
            is_active: true,
            end_date: '2024-12-31T00:00:00Z'
          }
        },
        error: null
      });

      const result = await getSubscriptionStatus(testUserId);

      expect(result.success).toBe(true);
      expect(result.data).toMatchObject({
        userId: testUserId,
        isSubscribed: true,
        subscriptionStatus: 'active',
        planId: 'pro',
        isExpired: false
      });

      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith('get_subscription_status', {
        p_user_id: testUserId
      });
    });

    it('should detect and fix inconsistencies between tables', async () => {
      const testUserId = 'user_inconsistent_123';

      // Mock inconsistent state (profile shows active, but subscription is expired)
      mockSupabaseClient.rpc
        .mockResolvedValueOnce({
          data: {
            success: true,
            profile: {
              is_subscribed: true,
              subscription_status: 'active',
              subscription_expires_at: '2023-12-31T00:00:00Z', // Past date
              subscription_plan: 'basic'
            },
            subscription: {
              is_expired: true,
              is_active: false
            }
          },
          error: null
        })
        .mockResolvedValueOnce({
          data: {
            success: true,
            user_id: testUserId
          },
          error: null
        });

      const result = await getSubscriptionStatus(testUserId);

      expect(result.success).toBe(true);
      expect(result.data?.isSubscribed).toBe(false);
      expect(result.data?.subscriptionStatus).toBe('expired');

      // Verify that status update was called to fix inconsistency
      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith('update_subscription_status_atomic', {
        p_user_id: testUserId,
        p_status: 'expired',
        p_reason: 'Subscription expired during status check'
      });
    });

    it('should handle concurrent status updates safely', async () => {
      const testUserId = 'user_concurrent_status';

      // Mock successful status updates
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          user_id: testUserId
        },
        error: null
      });

      // Simulate concurrent status updates
      const [result1, result2] = await Promise.all([
        updateSubscriptionStatusAtomic(testUserId, 'expired', 'Expired - concurrent test 1'),
        updateSubscriptionStatusAtomic(testUserId, 'cancelled', 'Cancelled - concurrent test 2')
      ]);

      expect(result1.success).toBe(true);
      expect(result2.success).toBe(true);
      expect(mockSupabaseClient.rpc).toHaveBeenCalledTimes(2);

      // Verify both calls were made with correct parameters
      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith('update_subscription_status_atomic', {
        p_user_id: testUserId,
        p_status: 'expired',
        p_reason: 'Expired - concurrent test 1'
      });

      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith('update_subscription_status_atomic', {
        p_user_id: testUserId,
        p_status: 'cancelled',
        p_reason: 'Cancelled - concurrent test 2'
      });
    });
  });

  describe('Bulk Operations and Data Integrity', () => {
    it('should handle bulk subscription cleanup with partial failures', async () => {
      // Mock bulk cleanup with some failures
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          expired_count: 3,
          expired_users: ['user1', 'user2', 'user3'],
          failed_users: ['user4'], // Some users failed to update
          errors: ['User user4: constraint violation']
        },
        error: null
      });

      const result = await checkAndUpdateExpiredSubscriptions();

      expect(result.success).toBe(true);
      expect(result.data?.expiredCount).toBe(3);
      expect(result.data?.cleanedUsers).toEqual(['user1', 'user2', 'user3']);
    });

    it('should retry bulk operations on transient failures', async () => {
      vi.useFakeTimers();

      // Mock initial failure followed by success
      mockSupabaseClient.rpc
        .mockResolvedValueOnce({
          data: {
            success: false,
            error: 'Temporary database lock'
          },
          error: null
        })
        .mockResolvedValueOnce({
          data: {
            success: true,
            expired_count: 2,
            expired_users: ['user1', 'user2']
          },
          error: null
        });

      const resultPromise = cleanupExpiredSubscriptionsWithRetry(2);

      // Fast-forward through retry delay
      vi.advanceTimersByTime(1000);
      await vi.runAllTimersAsync();

      const result = await resultPromise;

      expect(result.success).toBe(true);
      expect(result.data?.expiredCount).toBe(2);
      expect(mockSupabaseClient.rpc).toHaveBeenCalledTimes(2);

      vi.useRealTimers();
    });

    it('should maintain referential integrity during bulk operations', async () => {
      const expiredUsers = ['user1', 'user2', 'user3'];

      // Mock successful bulk operation
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          expired_count: expiredUsers.length,
          expired_users: expiredUsers,
          updated_tables: ['subscriptions', 'user_profiles', 'payment_transactions']
        },
        error: null
      });

      const result = await checkAndUpdateExpiredSubscriptions();

      expect(result.success).toBe(true);
      expect(result.data?.expiredCount).toBe(3);
      expect(result.data?.cleanedUsers).toEqual(expiredUsers);

      // Verify the atomic function was called
      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith('check_subscription_expiration');
    });
  });

  describe('Data Validation and Constraints', () => {
    it('should validate subscription data comprehensively', () => {
      // Test valid data
      const validData = {
        email: '<EMAIL>',
        planId: 'basic',
        amount: 99800,
        reference: 'valid_ref_123456'
      };

      let result = validateSubscriptionData(validData);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);

      // Test multiple validation errors
      const invalidData = {
        email: 'invalid-email',
        planId: 'invalid_plan',
        amount: -100,
        reference: 'short'
      };

      result = validateSubscriptionData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid email format');
      expect(result.errors).toContain('Invalid plan ID');
      expect(result.errors).toContain('Amount must be greater than 0');
      expect(result.errors).toContain('Payment reference must be at least 10 characters');
    });

    it('should handle database constraint violations gracefully', async () => {
      const testEmail = '<EMAIL>';
      const testUserId = 'user_constraint';

      // Mock successful user lookup
      mockSupabaseClient.auth.admin.listUsers.mockResolvedValue({
        data: {
          users: [{ id: testUserId, email: testEmail }]
        },
        error: null
      });

      // Mock unique constraint violation
      mockSupabaseClient.rpc.mockResolvedValue({
        data: null,
        error: {
          message: 'duplicate key value violates unique constraint "subscriptions_user_id_key"',
          code: '23505'
        }
      });

      const result = await updateUserSubscription(
        testEmail,
        'basic',
        99800,
        'constraint_test_ref'
      );

      expect(result.success).toBe(false);
      expect(result.errorCode).toBe('DATABASE_ERROR');
      expect(result.error).toContain('Database operation failed');
    });

    it('should handle foreign key constraint violations', async () => {
      const testUserId = 'nonexistent_user';

      // Mock foreign key constraint violation
      mockSupabaseClient.rpc.mockResolvedValue({
        data: null,
        error: {
          message: 'insert or update on table "subscriptions" violates foreign key constraint',
          code: '23503'
        }
      });

      const result = await updateSubscriptionStatusAtomic(
        testUserId,
        'active',
        'Test foreign key constraint'
      );

      expect(result.success).toBe(false);
      expect(result.errorCode).toBe('DATABASE_ERROR');
    });
  });

  describe('Fallback Mechanisms', () => {
    it('should fallback to manual operations when atomic functions unavailable', async () => {
      const testEmail = '<EMAIL>';
      const testUserId = 'user_fallback';

      // Mock successful user lookup
      mockSupabaseClient.auth.admin.listUsers.mockResolvedValue({
        data: {
          users: [{ id: testUserId, email: testEmail }]
        },
        error: null
      });

      // Mock atomic function not available
      mockSupabaseClient.rpc.mockResolvedValue({
        data: null,
        error: {
          message: 'function handle_payment_success_atomic does not exist',
          code: '42883'
        }
      });

      // Mock successful fallback operations
      const mockQuery = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        maybeSingle: vi.fn().mockResolvedValue({ data: null, error: null }),
        insert: vi.fn().mockResolvedValue({ data: [{}], error: null }),
        update: vi.fn().mockResolvedValue({ data: [{}], error: null })
      };

      mockSupabaseClient.from.mockReturnValue(mockQuery);

      const result = await updateUserSubscription(
        testEmail,
        'basic',
        99800,
        'fallback_ref'
      );

      expect(result.success).toBe(true);
      expect(mockSupabaseClient.from).toHaveBeenCalledWith('subscriptions');
      expect(mockSupabaseClient.from).toHaveBeenCalledWith('payment_transactions');
      expect(mockSupabaseClient.from).toHaveBeenCalledWith('user_profiles');
    });

    it('should handle fallback operation failures', async () => {
      const testEmail = '<EMAIL>';
      const testUserId = 'user_fallback_fail';

      // Mock successful user lookup
      mockSupabaseClient.auth.admin.listUsers.mockResolvedValue({
        data: {
          users: [{ id: testUserId, email: testEmail }]
        },
        error: null
      });

      // Mock atomic function not available
      mockSupabaseClient.rpc.mockResolvedValue({
        data: null,
        error: {
          message: 'function handle_payment_success_atomic does not exist'
        }
      });

      // Mock fallback operation failure
      const mockQuery = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        maybeSingle: vi.fn().mockResolvedValue({ data: null, error: null }),
        insert: vi.fn().mockResolvedValue({
          data: null,
          error: { message: 'Fallback operation failed' }
        })
      };

      mockSupabaseClient.from.mockReturnValue(mockQuery);

      const result = await updateUserSubscription(
        testEmail,
        'basic',
        99800,
        'fallback_fail_ref'
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('Fallback operation failed');
    });
  });

  describe('Data Recovery and Consistency Checks', () => {
    it('should detect and report data inconsistencies', async () => {
      const testUserId = 'user_inconsistent_data';

      // Mock status check that reveals inconsistencies
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          profile: {
            is_subscribed: true,
            subscription_status: 'active',
            subscription_expires_at: '2024-12-31T00:00:00Z'
          },
          subscription: {
            is_expired: false,
            is_active: true,
            end_date: '2024-06-30T00:00:00Z' // Different expiration date
          },
          inconsistencies: [
            'Profile expiration date does not match subscription end date'
          ]
        },
        error: null
      });

      const result = await getSubscriptionStatus(testUserId);

      expect(result.success).toBe(true);
      // The function should handle inconsistencies gracefully
      expect(result.data?.isSubscribed).toBeDefined();
    });

    it('should handle orphaned subscription records', async () => {
      // Mock cleanup operation that finds orphaned records
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          expired_count: 2,
          expired_users: ['user1', 'user2'],
          orphaned_records: ['orphan1', 'orphan2'],
          cleanup_actions: [
            'Removed orphaned subscription records',
            'Updated inconsistent user profiles'
          ]
        },
        error: null
      });

      const result = await checkAndUpdateExpiredSubscriptions();

      expect(result.success).toBe(true);
      expect(result.data?.expiredCount).toBe(2);
    });

    it('should validate data integrity after operations', async () => {
      const testEmail = '<EMAIL>';
      const testUserId = 'user_integrity';

      // Mock successful user lookup
      mockSupabaseClient.auth.admin.listUsers.mockResolvedValue({
        data: {
          users: [{ id: testUserId, email: testEmail }]
        },
        error: null
      });

      // Mock atomic function with integrity validation
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          user_id: testUserId,
          plan_id: 'basic',
          validation_checks: {
            subscription_created: true,
            profile_updated: true,
            payment_logged: true,
            referential_integrity: true
          }
        },
        error: null
      });

      const result = await updateUserSubscription(
        testEmail,
        'basic',
        99800,
        'integrity_ref'
      );

      expect(result.success).toBe(true);
      expect(result.data?.userId).toBe(testUserId);
    });
  });

  describe('Performance Under Load', () => {
    it('should handle high-volume concurrent operations', async () => {
      const userCount = 10;
      const users = Array.from({ length: userCount }, (_, i) => ({
        id: `user_load_${i}`,
        email: `load${i}@example.com`
      }));

      // Mock successful user lookups
      mockSupabaseClient.auth.admin.listUsers.mockResolvedValue({
        data: { users },
        error: null
      });

      // Mock successful atomic operations
      mockSupabaseClient.rpc.mockImplementation((functionName, params) => {
        return Promise.resolve({
          data: {
            success: true,
            user_id: params.p_user_id,
            plan_id: params.p_plan_id
          },
          error: null
        });
      });

      // Create concurrent subscription updates
      const operations = users.map((user, i) =>
        updateUserSubscription(
          user.email,
          'basic',
          99800,
          `load_ref_${i}`
        )
      );

      const results = await Promise.all(operations);

      // All operations should succeed
      results.forEach((result, i) => {
        expect(result.success).toBe(true);
        expect(result.data?.userId).toBe(`user_load_${i}`);
      });

      expect(mockSupabaseClient.rpc).toHaveBeenCalledTimes(userCount);
    });

    it('should handle database connection pool exhaustion', async () => {
      const testEmail = '<EMAIL>';

      // Mock successful user lookup
      mockSupabaseClient.auth.admin.listUsers.mockResolvedValue({
        data: {
          users: [{ id: 'user_pool', email: testEmail }]
        },
        error: null
      });

      // Mock connection pool exhaustion error
      mockSupabaseClient.rpc.mockRejectedValue(
        new Error('remaining connection slots are reserved for non-replication superuser connections')
      );

      const result = await updateUserSubscription(
        testEmail,
        'basic',
        99800,
        'pool_exhaustion_ref'
      );

      expect(result.success).toBe(false);
      expect(result.errorCode).toBe('NETWORK_ERROR');
    });
  });
});