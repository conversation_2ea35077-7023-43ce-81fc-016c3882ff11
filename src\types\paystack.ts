// Type definitions for react-paystack library

export interface PaystackConfig {
  reference: string;
  email: string;
  amount: number;
  publicKey: string;
  currency?: string;
  metadata?: {
    custom_fields?: Array<{
      display_name: string;
      variable_name: string;
      value: string;
    }>;
    [key: string]: unknown;
  };
}

export interface PaystackResponse {
  reference: string;
  status: string;
  trans: string;
  transaction: string;
  trxref: string;
  redirecturl?: string;
}

export interface PaystackHookResult {
  (onSuccess: (response: PaystackResponse) => void, onClose: () => void): void;
}

// Extend the react-paystack module types
declare module 'react-paystack' {
  export function usePaystackPayment(config: PaystackConfig): PaystackHookResult;
}