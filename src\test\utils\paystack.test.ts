import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
  generateReference,
  toKobo,
  handlePaymentSuccess,
  subscriptionPlans,
  PAYSTACK_PUBLIC_KEY
} from '@/utils/paystack';

// Mock fetch globally
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock environment variables
Object.defineProperty(import.meta, 'env', {
  value: {
    VITE_PAYSTACK_PUBLIC_KEY: 'pk_test_123456789',
    VITE_API_URL: 'http://localhost:3001',
    DEV: false,
  },
  writable: true,
});

// Mock window.location
Object.defineProperty(window, 'location', {
  value: {
    href: '',
  },
  writable: true,
});

describe('Paystack Utilities', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.clearAllTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('generateReference', () => {
    it('generates unique references', () => {
      const ref1 = generateReference();
      const ref2 = generateReference();
      
      expect(ref1).not.toBe(ref2);
      expect(ref1).toMatch(/^secquiz-\d+-[a-z0-9]+$/);
      expect(ref2).toMatch(/^secquiz-\d+-[a-z0-9]+$/);
    });

    it('includes timestamp in reference', () => {
      const beforeTime = Date.now();
      const reference = generateReference();
      const afterTime = Date.now();
      
      const timestampMatch = reference.match(/secquiz-(\d+)-/);
      expect(timestampMatch).toBeTruthy();
      
      const timestamp = parseInt(timestampMatch![1]);
      expect(timestamp).toBeGreaterThanOrEqual(beforeTime);
      expect(timestamp).toBeLessThanOrEqual(afterTime);
    });

    it('includes random string in reference', () => {
      const reference = generateReference();
      const parts = reference.split('-');
      
      expect(parts).toHaveLength(3);
      expect(parts[0]).toBe('secquiz');
      expect(parts[1]).toMatch(/^\d+$/);
      expect(parts[2]).toMatch(/^[a-z0-9]+$/);
      expect(parts[2].length).toBeGreaterThan(0);
    });
  });

  describe('toKobo', () => {
    it('converts Naira to Kobo correctly', () => {
      expect(toKobo(1)).toBe(100);
      expect(toKobo(10)).toBe(1000);
      expect(toKobo(100)).toBe(10000);
      expect(toKobo(1000)).toBe(100000);
    });

    it('handles decimal amounts', () => {
      expect(toKobo(1.5)).toBe(150);
      expect(toKobo(10.99)).toBe(1099);
      expect(toKobo(0.01)).toBe(1);
    });

    it('handles zero and negative amounts', () => {
      expect(toKobo(0)).toBe(0);
      expect(toKobo(-10)).toBe(-1000);
    });
  });

  describe('subscriptionPlans', () => {
    it('contains all required plans', () => {
      expect(subscriptionPlans).toHaveProperty('basic');
      expect(subscriptionPlans).toHaveProperty('pro');
      expect(subscriptionPlans).toHaveProperty('elite');
    });

    it('has correct plan structure', () => {
      Object.values(subscriptionPlans).forEach(plan => {
        expect(plan).toHaveProperty('id');
        expect(plan).toHaveProperty('name');
        expect(plan).toHaveProperty('amount');
        expect(plan).toHaveProperty('interval');
        expect(plan).toHaveProperty('features');
        
        expect(typeof plan.id).toBe('string');
        expect(typeof plan.name).toBe('string');
        expect(typeof plan.amount).toBe('number');
        expect(['weekly', 'monthly', 'one-time']).toContain(plan.interval);
        expect(Array.isArray(plan.features)).toBe(true);
      });
    });

    it('has reasonable pricing', () => {
      expect(subscriptionPlans.basic.amount).toBeGreaterThan(0);
      expect(subscriptionPlans.pro.amount).toBeGreaterThan(subscriptionPlans.basic.amount);
      expect(subscriptionPlans.elite.amount).toBeGreaterThan(0);
    });
  });

  describe('PAYSTACK_PUBLIC_KEY', () => {
    it('loads from environment variables', () => {
      expect(PAYSTACK_PUBLIC_KEY).toBe('pk_test_123456789');
    });
  });

  describe('handlePaymentSuccess', () => {
    const mockReference = 'test_ref_123';
    const mockPlanId = 'basic';
    const mockUserEmail = '<EMAIL>';

    beforeEach(() => {
      mockFetch.mockClear();
    });

    describe('Successful Payment Verification', () => {
      it('handles successful verification response', async () => {
        const mockResponse = {
          success: true,
          message: 'Payment verified successfully',
          data: {
            subscription: { id: 'sub_123' },
            processingTime: '2.5s'
          }
        };

        mockFetch.mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockResponse)
        });

        const result = await handlePaymentSuccess(mockReference, mockPlanId, mockUserEmail);

        expect(result).toEqual({
          success: true,
          message: 'Payment verified successfully',
          reference: mockReference,
          plan: subscriptionPlans.basic,
          data: mockResponse.data
        });

        expect(mockFetch).toHaveBeenCalledWith(
          'http://localhost:3001/api/payments/verify',
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ reference: mockReference }),
            signal: expect.any(AbortSignal)
          }
        );
      });

      it('uses default success message when none provided', async () => {
        const mockResponse = {
          success: true,
          data: {}
        };

        mockFetch.mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockResponse)
        });

        const result = await handlePaymentSuccess(mockReference, mockPlanId, mockUserEmail);

        expect(result.message).toBe('Successfully subscribed to Basic plan');
      });
    });

    describe('Error Handling', () => {
      it('handles invalid plan ID', async () => {
        const result = await handlePaymentSuccess(mockReference, 'invalid_plan', mockUserEmail);

        expect(result).toEqual({
          success: false,
          message: 'Invalid subscription plan',
          error: "Plan 'invalid_plan' not found"
        });

        expect(mockFetch).not.toHaveBeenCalled();
      });

      it('handles 404 response', async () => {
        mockFetch.mockResolvedValueOnce({
          ok: false,
          status: 404
        });

        const result = await handlePaymentSuccess(mockReference, mockPlanId, mockUserEmail);

        expect(result).toEqual({
          success: false,
          message: 'Payment reference not found',
          error: 'The payment reference could not be found in our system'
        });
      });

      it('handles 401 response', async () => {
        mockFetch.mockResolvedValueOnce({
          ok: false,
          status: 401
        });

        const result = await handlePaymentSuccess(mockReference, mockPlanId, mockUserEmail);

        expect(result).toEqual({
          success: false,
          message: 'Authentication failed',
          error: 'Unable to authenticate with payment service'
        });
      });

      it('handles 429 rate limiting', async () => {
        mockFetch.mockResolvedValueOnce({
          ok: false,
          status: 429
        });

        const result = await handlePaymentSuccess(mockReference, mockPlanId, mockUserEmail);

        expect(result).toEqual({
          success: false,
          message: 'Too many requests',
          error: 'Please wait a moment before trying again'
        });
      });

      it('handles server errors with retry', async () => {
        // First call fails with 500
        mockFetch.mockResolvedValueOnce({
          ok: false,
          status: 500
        });

        // Second call succeeds
        mockFetch.mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            message: 'Payment verified on retry'
          })
        });

        const result = await handlePaymentSuccess(mockReference, mockPlanId, mockUserEmail);

        expect(result.success).toBe(true);
        expect(result.message).toBe('Payment verified on retry');
        expect(mockFetch).toHaveBeenCalledTimes(2);
      });

      it('handles max retries exceeded', async () => {
        // All calls fail with 500
        mockFetch.mockResolvedValue({
          ok: false,
          status: 500
        });

        const result = await handlePaymentSuccess(mockReference, mockPlanId, mockUserEmail);

        expect(result).toEqual({
          success: false,
          message: 'Payment service temporarily unavailable',
          error: 'Please try again later or contact support'
        });

        expect(mockFetch).toHaveBeenCalledTimes(4); // Initial + 3 retries
      });

      it('handles JSON parsing errors', async () => {
        mockFetch.mockResolvedValueOnce({
          ok: true,
          json: () => Promise.reject(new Error('Invalid JSON'))
        });

        const result = await handlePaymentSuccess(mockReference, mockPlanId, mockUserEmail);

        expect(result).toEqual({
          success: false,
          message: 'Invalid response from payment service',
          error: 'Unable to parse server response'
        });
      });

      it('handles network timeout', async () => {
        vi.useFakeTimers();

        // Mock a hanging request
        mockFetch.mockImplementationOnce(() => 
          new Promise(() => {}) // Never resolves
        );

        const resultPromise = handlePaymentSuccess(mockReference, mockPlanId, mockUserEmail);

        // Fast-forward time to trigger timeout
        vi.advanceTimersByTime(30000);

        const result = await resultPromise;

        expect(result).toEqual({
          success: false,
          message: 'Payment verification timed out',
          error: 'The verification request took too long to complete'
        });
      });

      it('handles network errors with retry', async () => {
        // First call fails with network error
        mockFetch.mockRejectedValueOnce(new Error('NetworkError: Failed to fetch'));

        // Second call succeeds
        mockFetch.mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            message: 'Payment verified after network retry'
          })
        });

        const result = await handlePaymentSuccess(mockReference, mockPlanId, mockUserEmail);

        expect(result.success).toBe(true);
        expect(result.message).toBe('Payment verified after network retry');
        expect(mockFetch).toHaveBeenCalledTimes(2);
      });
    });

    describe('Retry Logic', () => {
      it('implements exponential backoff', async () => {
        vi.useFakeTimers();

        // All calls fail with retryable error
        mockFetch.mockResolvedValue({
          ok: false,
          status: 500
        });

        const resultPromise = handlePaymentSuccess(mockReference, mockPlanId, mockUserEmail);

        // Should retry with exponential backoff: 1s, 2s, 4s
        expect(mockFetch).toHaveBeenCalledTimes(1);

        vi.advanceTimersByTime(1000);
        await vi.runAllTimersAsync();
        expect(mockFetch).toHaveBeenCalledTimes(2);

        vi.advanceTimersByTime(2000);
        await vi.runAllTimersAsync();
        expect(mockFetch).toHaveBeenCalledTimes(3);

        vi.advanceTimersByTime(4000);
        await vi.runAllTimersAsync();
        expect(mockFetch).toHaveBeenCalledTimes(4);

        const result = await resultPromise;
        expect(result.success).toBe(false);
      });

      it('retries on retryable server errors', async () => {
        const mockResponse = {
          success: false,
          error: {
            code: 'VERIFICATION_TIMEOUT',
            message: 'Verification timed out'
          }
        };

        // First call fails with retryable error
        mockFetch.mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockResponse)
        });

        // Second call succeeds
        mockFetch.mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            message: 'Payment verified on retry'
          })
        });

        const result = await handlePaymentSuccess(mockReference, mockPlanId, mockUserEmail);

        expect(result.success).toBe(true);
        expect(mockFetch).toHaveBeenCalledTimes(2);
      });

      it('does not retry on non-retryable errors', async () => {
        const mockResponse = {
          success: false,
          error: {
            code: 'INVALID_REFERENCE',
            message: 'Reference not found'
          }
        };

        mockFetch.mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockResponse)
        });

        const result = await handlePaymentSuccess(mockReference, mockPlanId, mockUserEmail);

        expect(result.success).toBe(false);
        expect(result.message).toBe('Reference not found');
        expect(mockFetch).toHaveBeenCalledTimes(1); // No retry
      });
    });

    describe('Development Mode Behavior', () => {
      it('redirects to success page in development mode on error', async () => {
        // Enable development mode
        Object.defineProperty(import.meta, 'env', {
          value: {
            ...import.meta.env,
            DEV: true,
          },
          writable: true,
        });

        vi.useFakeTimers();

        mockFetch.mockRejectedValueOnce(new Error('Network error'));

        const result = await handlePaymentSuccess(mockReference, mockPlanId, mockUserEmail);

        expect(result.success).toBe(false);

        // Should set timeout for redirect
        vi.advanceTimersByTime(2000);
        expect(window.location.href).toBe('/payment/success?reference=test_ref_123&plan=Basic');

        // Restore production mode
        Object.defineProperty(import.meta, 'env', {
          value: {
            ...import.meta.env,
            DEV: false,
          },
          writable: true,
        });
      });
    });

    describe('Custom Retry Parameters', () => {
      it('respects custom retry count', async () => {
        mockFetch.mockResolvedValue({
          ok: false,
          status: 500
        });

        const result = await handlePaymentSuccess(
          mockReference, 
          mockPlanId, 
          mockUserEmail,
          0, // retryCount
          1  // maxRetries
        );

        expect(result.success).toBe(false);
        expect(mockFetch).toHaveBeenCalledTimes(2); // Initial + 1 retry
      });

      it('handles zero max retries', async () => {
        mockFetch.mockResolvedValue({
          ok: false,
          status: 500
        });

        const result = await handlePaymentSuccess(
          mockReference, 
          mockPlanId, 
          mockUserEmail,
          0, // retryCount
          0  // maxRetries
        );

        expect(result.success).toBe(false);
        expect(mockFetch).toHaveBeenCalledTimes(1); // No retries
      });
    });

    describe('Edge Cases', () => {
      it('handles empty response body', async () => {
        mockFetch.mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(null)
        });

        const result = await handlePaymentSuccess(mockReference, mockPlanId, mockUserEmail);

        expect(result.success).toBe(false);
        expect(result.message).toBe('Payment verification failed');
      });

      it('handles undefined response data', async () => {
        mockFetch.mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(undefined)
        });

        const result = await handlePaymentSuccess(mockReference, mockPlanId, mockUserEmail);

        expect(result.success).toBe(false);
        expect(result.message).toBe('Payment verification failed');
      });

      it('handles response with missing success field', async () => {
        mockFetch.mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            message: 'Some message'
          })
        });

        const result = await handlePaymentSuccess(mockReference, mockPlanId, mockUserEmail);

        expect(result.success).toBe(false);
        expect(result.message).toBe('Some message');
      });
    });
  });
});