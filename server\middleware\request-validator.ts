/**
 * Request validation middleware
 */

import { Request, Response, NextFunction } from 'express';
import { secureLogger } from '../lib/secure-logger.js';

/**
 * Validates request size and content type
 */
export function requestSizeValidator(maxSize: number = 10 * 1024 * 1024) { // 10MB default
  return (req: Request, res: Response, next: NextFunction) => {
    const contentLength = parseInt(req.headers['content-length'] || '0');
    
    if (contentLength > maxSize) {
      secureLogger.security('Request size exceeded', {
        ip: req.ip,
        path: req.path,
        contentLength,
        maxSize,
        userAgent: req.headers['user-agent']
      });

      return res.status(413).json({
        status: 'error',
        message: 'Request entity too large'
      });
    }

    next();
  };
}

/**
 * Validates JSON content type for POST/PUT requests
 */
export function contentTypeValidator(req: Request, res: Response, next: NextFunction) {
  if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
    const contentType = req.headers['content-type'];
    
    if (!contentType || !contentType.includes('application/json')) {
      secureLogger.security('Invalid content type', {
        ip: req.ip,
        path: req.path,
        method: req.method,
        contentType,
        userAgent: req.headers['user-agent']
      });

      return res.status(400).json({
        status: 'error',
        message: 'Content-Type must be application/json'
      });
    }
  }

  next();
}

/**
 * Validates request headers for suspicious patterns
 */
export function headerValidator(req: Request, res: Response, next: NextFunction) {
  const suspiciousHeaders = [
    'x-forwarded-host',
    'x-original-url',
    'x-rewrite-url'
  ];

  const userAgent = req.headers['user-agent'] || '';
  const suspiciousUserAgents = [
    'sqlmap',
    'nikto',
    'nmap',
    'masscan',
    'nessus',
    'openvas',
    'burp',
    'w3af'
  ];

  // Check for suspicious headers
  for (const header of suspiciousHeaders) {
    if (req.headers[header]) {
      secureLogger.security('Suspicious header detected', {
        ip: req.ip,
        path: req.path,
        header,
        value: req.headers[header],
        userAgent
      });

      return res.status(400).json({
        status: 'error',
        message: 'Invalid request headers'
      });
    }
  }

  // Check for suspicious user agents
  const lowerUserAgent = userAgent.toLowerCase();
  for (const suspicious of suspiciousUserAgents) {
    if (lowerUserAgent.includes(suspicious)) {
      secureLogger.security('Suspicious user agent detected', {
        ip: req.ip,
        path: req.path,
        userAgent
      });

      return res.status(403).json({
        status: 'error',
        message: 'Access denied'
      });
    }
  }

  next();
}

/**
 * Validates request path for suspicious patterns
 */
export function pathValidator(req: Request, res: Response, next: NextFunction) {
  const path = req.path.toLowerCase();
  const suspiciousPatterns = [
    /\.\./,  // Directory traversal
    /\/etc\/passwd/,
    /\/proc\/self/,
    /\/windows\/system32/,
    /<script/,
    /javascript:/,
    /vbscript:/,
    /onload=/,
    /onerror=/,
    /eval\(/,
    /union.*select/,
    /drop.*table/,
    /insert.*into/,
    /delete.*from/,
    /update.*set/
  ];

  for (const pattern of suspiciousPatterns) {
    if (pattern.test(path) || pattern.test(req.url || '')) {
      secureLogger.security('Suspicious path pattern detected', {
        ip: req.ip,
        path: req.path,
        url: req.url,
        pattern: pattern.toString(),
        userAgent: req.headers['user-agent']
      });

      return res.status(400).json({
        status: 'error',
        message: 'Invalid request path'
      });
    }
  }

  next();
}

/**
 * Validates query parameters for suspicious content
 */
export function queryValidator(req: Request, res: Response, next: NextFunction) {
  const queryString = JSON.stringify(req.query).toLowerCase();
  const suspiciousPatterns = [
    /<script/,
    /javascript:/,
    /vbscript:/,
    /union.*select/,
    /drop.*table/,
    /insert.*into/,
    /delete.*from/,
    /update.*set/,
    /exec\(/,
    /eval\(/,
    /system\(/,
    /cmd\(/
  ];

  for (const pattern of suspiciousPatterns) {
    if (pattern.test(queryString)) {
      secureLogger.security('Suspicious query parameter detected', {
        ip: req.ip,
        path: req.path,
        query: req.query,
        pattern: pattern.toString(),
        userAgent: req.headers['user-agent']
      });

      return res.status(400).json({
        status: 'error',
        message: 'Invalid query parameters'
      });
    }
  }

  next();
}

/**
 * Comprehensive request validation middleware
 */
export function comprehensiveRequestValidator(req: Request, res: Response, next: NextFunction) {
  // Chain all validators
  requestSizeValidator()(req, res, (err) => {
    if (err) return next(err);
    
    contentTypeValidator(req, res, (err) => {
      if (err) return next(err);
      
      headerValidator(req, res, (err) => {
        if (err) return next(err);
        
        pathValidator(req, res, (err) => {
          if (err) return next(err);
          
          queryValidator(req, res, next);
        });
      });
    });
  });
}

/**
 * Payment-specific request validator
 */
export function paymentRequestValidator(req: Request, res: Response, next: NextFunction) {
  // Additional validation for payment endpoints
  const body = req.body || {};
  
  // Check for required fields based on endpoint
  if (req.path.includes('/verify')) {
    if (!body.reference || typeof body.reference !== 'string') {
      secureLogger.security('Invalid payment verification request', {
        ip: req.ip,
        path: req.path,
        hasReference: !!body.reference,
        userAgent: req.headers['user-agent']
      });

      return res.status(400).json({
        status: 'error',
        message: 'Payment reference is required'
      });
    }
  }

  // Apply comprehensive validation
  comprehensiveRequestValidator(req, res, next);
}