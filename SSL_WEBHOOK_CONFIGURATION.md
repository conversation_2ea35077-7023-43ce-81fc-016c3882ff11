# SSL and Webhook Configuration Guide

This guide covers the setup of SSL certificates and webhook configurations for production deployment of SecQuiz.

## SSL Certificate Setup

### Option 1: Let's Encrypt (Recommended for most deployments)

#### Using Certbot
```bash
# Install certbot
sudo apt-get update
sudo apt-get install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d your-domain.com -d api.your-domain.com

# Verify auto-renewal
sudo certbot renew --dry-run
```

#### Nginx Configuration
```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;

    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    # SSL Configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Client application
    location / {
        try_files $uri $uri/ /index.html;
        root /var/www/secquiz/dist;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}

# API Server
server {
    listen 443 ssl http2;
    server_name api.your-domain.com;

    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    # Same SSL configuration as above
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Special handling for webhook endpoint
    location /api/webhooks/paystack {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Webhook-specific settings
        proxy_buffering off;
        proxy_request_buffering off;
        client_max_body_size 1M;
    }
}
```

### Option 2: Commercial SSL Certificate

#### Purchase and Install
1. Purchase SSL certificate from trusted CA (DigiCert, Comodo, etc.)
2. Generate Certificate Signing Request (CSR)
3. Submit CSR to CA and complete validation
4. Download and install certificate

#### CSR Generation
```bash
# Generate private key
openssl genrsa -out your-domain.com.key 2048

# Generate CSR
openssl req -new -key your-domain.com.key -out your-domain.com.csr

# Provide required information:
# Country Name: US
# State: Your State
# City: Your City
# Organization: Your Company
# Organizational Unit: IT Department
# Common Name: your-domain.com
# Email: <EMAIL>
```

### Option 3: Cloud Provider SSL (AWS, Cloudflare, etc.)

#### AWS Certificate Manager
```bash
# Request certificate
aws acm request-certificate \
    --domain-name your-domain.com \
    --subject-alternative-names api.your-domain.com \
    --validation-method DNS

# Validate domain ownership through DNS records
# Certificate will be automatically renewed
```

## Webhook Configuration

### Paystack Webhook Setup

#### 1. Configure Webhook in Paystack Dashboard
1. Log in to Paystack Dashboard
2. Navigate to Settings → API Keys & Webhooks
3. Click "Add Webhook"
4. Set URL: `https://api.your-domain.com/api/webhooks/paystack`
5. Select events to listen for:
   - `charge.success`
   - `subscription.create`
   - `subscription.disable`
   - `invoice.create`
   - `invoice.payment_failed`
6. Save webhook and copy the secret

#### 2. Webhook Security Configuration

##### Server-side Verification
```javascript
// webhook-verification.js
const crypto = require('crypto');

function verifyPaystackSignature(payload, signature, secret) {
    const expectedSignature = crypto
        .createHmac('sha512', secret)
        .update(payload)
        .digest('hex');
    
    // Use timing-safe comparison to prevent timing attacks
    return crypto.timingSafeEqual(
        Buffer.from(signature, 'hex'),
        Buffer.from(expectedSignature, 'hex')
    );
}

// Express middleware
function validateWebhookSignature(req, res, next) {
    const signature = req.headers['x-paystack-signature'];
    const payload = JSON.stringify(req.body);
    const secret = process.env.PAYSTACK_WEBHOOK_SECRET;
    
    if (!signature) {
        return res.status(400).json({ error: 'Missing signature' });
    }
    
    if (!verifyPaystackSignature(payload, signature, secret)) {
        return res.status(401).json({ error: 'Invalid signature' });
    }
    
    next();
}
```

##### Nginx Rate Limiting for Webhooks
```nginx
# Rate limiting configuration
http {
    limit_req_zone $binary_remote_addr zone=webhook:10m rate=10r/m;
    
    server {
        # ... other configuration
        
        location /api/webhooks/paystack {
            limit_req zone=webhook burst=5 nodelay;
            
            # Only allow POST requests
            if ($request_method != POST) {
                return 405;
            }
            
            # Restrict to Paystack IP ranges (optional)
            allow ************;
            allow *************;
            allow *************;
            deny all;
            
            proxy_pass http://localhost:5000;
            # ... other proxy settings
        }
    }
}
```

### Webhook Testing

#### Local Testing with ngrok
```bash
# Install ngrok
npm install -g ngrok

# Expose local server
ngrok http 5000

# Use the HTTPS URL for webhook configuration
# Example: https://abc123.ngrok.io/api/webhooks/paystack
```

#### Production Testing
```bash
# Test webhook endpoint
curl -X POST https://api.your-domain.com/api/webhooks/paystack \
  -H "Content-Type: application/json" \
  -H "x-paystack-signature: test_signature" \
  -d '{
    "event": "charge.success",
    "data": {
      "reference": "test_reference",
      "amount": 100000,
      "customer": {
        "email": "<EMAIL>"
      }
    }
  }'
```

### Webhook Monitoring

#### Health Check Endpoint
```javascript
// Add to your server
app.get('/api/webhooks/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        webhook_endpoint: '/api/webhooks/paystack',
        last_webhook_received: getLastWebhookTimestamp(),
        webhook_processing_status: 'operational'
    });
});
```

#### Webhook Failure Handling
```javascript
// Implement retry logic for failed webhook processing
async function processWebhookWithRetry(eventData, maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            await processWebhookEvent(eventData);
            return { success: true, attempt };
        } catch (error) {
            console.error(`Webhook processing attempt ${attempt} failed:`, error);
            
            if (attempt === maxRetries) {
                // Log to error tracking service
                await logWebhookFailure(eventData, error);
                throw error;
            }
            
            // Exponential backoff
            await new Promise(resolve => 
                setTimeout(resolve, Math.pow(2, attempt) * 1000)
            );
        }
    }
}
```

## Security Best Practices

### SSL Security
- Use TLS 1.2 or higher
- Implement HSTS headers
- Use strong cipher suites
- Regular certificate renewal
- Monitor certificate expiration

### Webhook Security
- Always verify webhook signatures
- Use HTTPS for webhook URLs
- Implement rate limiting
- Log all webhook events
- Monitor for suspicious activity
- Use IP whitelisting if possible

### General Security
- Keep server software updated
- Use fail2ban for intrusion prevention
- Implement proper firewall rules
- Regular security audits
- Monitor server logs

## Troubleshooting

### SSL Issues
- Certificate chain incomplete: Ensure full chain is installed
- Mixed content warnings: Ensure all resources use HTTPS
- Certificate mismatch: Verify certificate matches domain
- Expired certificate: Set up auto-renewal

### Webhook Issues
- Signature verification fails: Check webhook secret
- Webhook not received: Verify URL accessibility
- Processing failures: Check server logs and error handling
- Rate limiting: Adjust limits based on expected traffic

### Monitoring Commands
```bash
# Check SSL certificate
openssl s_client -connect your-domain.com:443 -servername your-domain.com

# Test webhook endpoint
curl -I https://api.your-domain.com/api/webhooks/paystack

# Check server logs
tail -f /var/log/nginx/access.log
tail -f /var/log/secquiz/app.log

# Monitor webhook processing
grep "webhook" /var/log/secquiz/app.log | tail -20
```