# Paystack Integration Guide for SecQuiz

This guide explains how to set up and configure the Paystack payment integration for the SecQuiz application.

## Overview

The SecQuiz application uses Paystack to process payments for subscription plans. The integration includes:

1. Client-side payment processing using the `react-paystack` library
2. Server-side payment verification and subscription management with comprehensive error handling
3. Secure webhook handling for real-time payment notifications with signature verification
4. Database consistency management across subscription and user profile tables
5. Comprehensive logging and monitoring for payment events

## Prerequisites

Before you begin, you'll need:

1. A Paystack account (create one at [paystack.com](https://paystack.com))
2. API keys from your Paystack dashboard
3. A Supabase account for database management

## Setup Instructions

### 1. Set Up Environment Variables

#### Client-Side (.env file)

Create a `.env` file in the root of your project (or copy from `.env.example`) and add:

```bash
# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Application Settings
VITE_APP_NAME=SecQuiz
VITE_APP_DESCRIPTION=A cybersecurity education platform with interactive quizzes
VITE_APP_URL=http://localhost:5173

# Feature Flags
VITE_ENABLE_ADMIN_FEATURES=true
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_DEBUG_MODE=true

# API Configuration
VITE_API_TIMEOUT=30000
VITE_MAX_UPLOAD_SIZE=5242880
VITE_API_URL=http://localhost:5000

# Paystack Configuration
VITE_PAYSTACK_PUBLIC_KEY=your_paystack_public_key_here

# Authentication Settings
VITE_AUTH_REDIRECT_URL=http://localhost:5173/auth/verify
```

#### Server-Side (server/.env file)

Create a `.env` file in the server directory (or copy from `server/.env.example`) and add:

```bash
# Supabase
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Paystack
PAYSTACK_SECRET_KEY=your_paystack_secret_key
PAYSTACK_WEBHOOK_SECRET=your_paystack_webhook_secret

# Server
PORT=5000
NODE_ENV=development

# Security
CORS_ORIGIN=http://localhost:5173
```

**Required Environment Variables:**

- `VITE_PAYSTACK_PUBLIC_KEY`: Your Paystack public key (starts with `pk_`)
- `PAYSTACK_SECRET_KEY`: Your Paystack secret key (starts with `sk_`)
- `PAYSTACK_WEBHOOK_SECRET`: Your Paystack webhook secret for signature verification
- `VITE_SUPABASE_URL`: Your Supabase project URL
- `VITE_SUPABASE_ANON_KEY`: Your Supabase anonymous key
- `SUPABASE_SERVICE_ROLE_KEY`: Your Supabase service role key

### 2. Set Up Database Tables

The payment system uses the following database tables:

- `subscriptions` - Stores user subscription information with plan details
- `payment_transactions` - Records payment history and transaction status
- `user_profiles` - Maintains user subscription status and expiration dates

**Database Functions:**
- `handle_payment_success()` - Atomically updates subscription and user profile data
- `check_subscription_status()` - Validates current subscription status
- `cleanup_expired_subscriptions()` - Manages expired subscription cleanup

Run the migration scripts in the `supabase/migrations/` directory to set up the database schema.

### 3. Start the Server

Navigate to the server directory and run:

```bash
npm install
npm run dev
```

This will start the server on port 5000 (or the port specified in your .env file).

### 4. Configure Paystack Webhook

1. Log in to your Paystack dashboard
2. Go to Settings > API Keys & Webhooks
3. Add a new webhook URL: `https://your-domain.com/api/webhooks/paystack`
4. Select the following events to listen for:
   - `charge.success`
   - `subscription.create`
   - `subscription.disable`
   - `invoice.create`
   - `invoice.payment_failed`
5. Copy the webhook secret and add it to your server environment variables as `PAYSTACK_WEBHOOK_SECRET`
6. Save the webhook

**Security Features:**
- All webhook requests are verified using HMAC SHA512 signature verification
- Idempotent processing prevents duplicate webhook handling
- Comprehensive logging for webhook events and failures

For local development, you can use a service like ngrok to expose your local server to the internet.

## Files to Update with Your Paystack API Keys

Here are the files where you need to add your Paystack API keys:

1. **Client-side Public Key**:
   - `.env` file: `VITE_PAYSTACK_PUBLIC_KEY=your_paystack_public_key_here`

2. **Server-side Secret Key**:
   - `server/.env` file: `PAYSTACK_SECRET_KEY=your_paystack_secret_key_here`

## Testing the Integration

1. Start both the client and server applications
2. Navigate to the pricing page
3. Select a plan and click the Subscribe button
4. You should see the Paystack payment popup
5. Use Paystack test cards to complete the payment:
   - Card Number: `4084 0840 8408 4081`
   - Expiry Date: Any future date
   - CVV: Any 3 digits
   - PIN: Any 4 digits
   - OTP: `123456`

## Subscription Plans

The application has three subscription plans:

1. **Basic** - ₦998/week
   - Access to 4 quiz domains
   - 400 questions weekly

2. **Pro** - ₦1,979/week
   - Access to all quiz domains
   - Unlimited questions
   - Cancel anytime

3. **Elite** - ₦5,000 one-time
   - Everything in Pro
   - Community Access
   - 24/7 Priority Mentorship & Support
   - CV Design and Job readiness Assist
   - Daily cybersecurity related jobs
   - Referrals for job openings

## Current Implementation Features

### Payment Components
- `PaystackButton`: Basic payment button with error handling
- `SecurePaystackButton`: Enhanced payment button with comprehensive error handling and loading states
- `PaymentSuccess`: Success page with subscription activation confirmation
- `PaymentTroubleshoot`: Troubleshooting page for payment issues

### API Endpoints
- `POST /api/payments/verify`: Verify payment transactions with Paystack
- `POST /api/webhooks/paystack`: Handle Paystack webhook events
- `GET /api/health`: Health check endpoint with payment system status
- `GET /api/subscriptions/status`: Check user subscription status

### Security Features
- Environment variable validation on server startup
- Webhook signature verification using timing-safe comparison
- Input validation and sanitization
- Comprehensive error logging without exposing sensitive data
- Rate limiting on payment endpoints

### Error Handling
- Comprehensive error messages for different failure scenarios
- Retry mechanisms for failed payments
- Transaction rollback on database operation failures
- Graceful degradation when external services are unavailable

## Troubleshooting

### Common Issues and Solutions

#### Payment popup not appearing
- **Cause**: Missing or incorrect Paystack public key
- **Solution**: Verify `VITE_PAYSTACK_PUBLIC_KEY` in your `.env` file
- **Check**: Ensure the key starts with `pk_test_` (test) or `pk_live_` (production)

#### Payment verification failing
- **Cause**: Server not running or incorrect API configuration
- **Solution**: 
  1. Ensure server is running on the correct port
  2. Verify `VITE_API_URL` points to your server
  3. Check server logs for detailed error messages
  4. Validate `PAYSTACK_SECRET_KEY` is correctly set

#### Webhook not working
- **Cause**: Incorrect webhook configuration or signature verification failure
- **Solution**:
  1. Verify webhook URL in Paystack dashboard
  2. Ensure `PAYSTACK_WEBHOOK_SECRET` matches the secret in your dashboard
  3. Check webhook event types are correctly configured
  4. Review server logs for webhook processing errors

#### Database consistency issues
- **Cause**: Failed database operations or missing constraints
- **Solution**:
  1. Check database connection and permissions
  2. Verify all required tables exist
  3. Run database migration scripts
  4. Check for foreign key constraint violations

#### TypeScript compilation errors
- **Cause**: Missing type definitions or incorrect imports
- **Solution**:
  1. Run `npm install` to ensure all dependencies are installed
  2. Check for proper type definitions in payment components
  3. Verify ES6 import syntax is used consistently

#### Environment variable errors
- **Cause**: Missing or incorrectly formatted environment variables
- **Solution**:
  1. Copy from `.env.example` files
  2. Verify all required variables are set
  3. Check for trailing spaces or incorrect formatting
  4. Restart server after environment changes

### Debug Mode
Enable debug mode by setting `VITE_ENABLE_DEBUG_MODE=true` in your client `.env` file for additional logging and error information.

### Health Check
Visit `/api/health` endpoint to check the status of:
- Database connection
- Paystack API connectivity
- Environment variable validation
- Server configuration

## Production Deployment

For production deployment:

1. Update the API URL in your client-side .env file to point to your production server
2. Set up proper error handling and logging
3. Implement additional security measures like CSRF protection
4. Configure your server to use HTTPS
5. Update the webhook URL in your Paystack dashboard to point to your production server

## Support

If you encounter any issues with the Paystack integration, please contact:

- Paystack Support: [<EMAIL>](mailto:<EMAIL>)
- SecQuiz Support: [<EMAIL>](mailto:<EMAIL>)
