/**
 * Page State Persistence Utility
 * Handles saving and restoring the current page/route state across browser refreshes
 */

const PAGE_STATE_KEY = 'secquiz-page-state';

export interface PageState {
  pathname: string;
  search: string;
  timestamp: number;
}

/**
 * Save the current page state to localStorage
 */
export function savePageState(pathname: string, search: string = ''): void {
  try {
    const pageState: PageState = {
      pathname,
      search,
      timestamp: Date.now()
    };
    
    localStorage.setItem(PAGE_STATE_KEY, JSON.stringify(pageState));
  } catch (error) {
    console.warn('Failed to save page state:', error);
  }
}

/**
 * Get the saved page state from localStorage
 * Returns null if no valid state is found or if it's too old (>1 hour)
 */
export function getSavedPageState(): PageState | null {
  try {
    const savedState = localStorage.getItem(PAGE_STATE_KEY);
    if (!savedState) return null;
    
    const pageState: PageState = JSON.parse(savedState);
    
    // Check if the saved state is not too old (1 hour = 3600000 ms)
    const oneHour = 60 * 60 * 1000;
    if (Date.now() - pageState.timestamp > oneHour) {
      clearPageState();
      return null;
    }
    
    return pageState;
  } catch (error) {
    console.warn('Failed to get saved page state:', error);
    clearPageState();
    return null;
  }
}

/**
 * Clear the saved page state
 */
export function clearPageState(): void {
  try {
    localStorage.removeItem(PAGE_STATE_KEY);
  } catch (error) {
    console.warn('Failed to clear page state:', error);
  }
}

/**
 * Check if a route should be persisted
 * Some routes like auth pages shouldn't be restored
 */
export function shouldPersistRoute(pathname: string): boolean {
  const excludedRoutes = [
    '/auth',
    '/auth/verify',
    '/verify',
    '/payment/success',
    '/payment/troubleshoot'
  ];
  
  return !excludedRoutes.some(route => pathname.startsWith(route));
}
