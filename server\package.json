{"name": "secquiz-server", "version": "1.0.0", "description": "Server for SecQuiz application with Paystack integration", "type": "module", "main": "index.js", "scripts": {"start": "node dist/index.js", "dev": "npx ts-node --esm index.ts", "dev:watch": "npx nodemon --exec npx ts-node --esm index.ts", "build": "tsc", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "dependencies": {"@supabase/supabase-js": "^2.39.0", "axios": "^1.6.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20.10.4", "@types/supertest": "^6.0.3", "@vitest/coverage-v8": "^3.2.4", "nodemon": "^3.0.2", "supertest": "^7.1.4", "ts-node": "^10.9.1", "typescript": "^5.3.3", "vitest": "^3.1.1"}}