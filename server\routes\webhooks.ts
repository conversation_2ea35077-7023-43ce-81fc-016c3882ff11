import express, { Request, Response } from "express";
import crypto from "crypto";
import { updateUserSubscription } from "../services/subscription.js";
import { supabase } from "../lib/supabase.js";
import { timingSafeEqual } from "crypto";
import { configManager } from "../lib/config.js";
import { createValidationMiddleware, WEBHOOK_VALIDATION_RULES } from '../lib/input-validator.js';
import { secureLogger } from '../lib/secure-logger.js';
import { webhookEventLogger } from '../middleware/security-logger.js';
import { paymentEventLogger } from '../lib/payment-event-logger.js';

const router = express.Router();
const paystackConfig = configManager.getPaystackConfig();

// Webhook event processing status
interface WebhookProcessingResult {
  success: boolean;
  message: string;
  eventId?: string;
  error?: string;
}

// Webhook event log interface
interface WebhookEventLog {
  event_id: string;
  event_type: string;
  reference?: string;
  status: 'received' | 'processing' | 'completed' | 'failed' | 'duplicate';
  payload_hash: string;
  processing_attempts: number;
  error_message?: string;
  created_at: string;
  updated_at: string;
}

// Create a hash of the webhook payload for idempotency
function createPayloadHash(payload: any): string {
  return crypto.createHash('sha256')
    .update(JSON.stringify(payload))
    .digest('hex');
}

// Log webhook event to database for monitoring and idempotency
async function logWebhookEvent(
  eventId: string,
  eventType: string,
  payload: any,
  status: WebhookEventLog['status'],
  reference?: string,
  errorMessage?: string
): Promise<void> {
  try {
    const payloadHash = createPayloadHash(payload);
    const now = new Date().toISOString();

    const { error } = await supabase
      .from('webhook_events')
      .upsert({
        event_id: eventId,
        event_type: eventType,
        reference,
        status,
        payload_hash: payloadHash,
        processing_attempts: 1,
        error_message: errorMessage,
        created_at: now,
        updated_at: now
      }, {
        onConflict: 'event_id',
        ignoreDuplicates: false
      });

    if (error) {
      console.error('Failed to log webhook event:', error);
    }
  } catch (error) {
    console.error('Error logging webhook event:', error);
  }
}

// Check if webhook event has already been processed (idempotency check)
async function isEventAlreadyProcessed(eventId: string, payloadHash: string): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('webhook_events')
      .select('event_id, status, payload_hash')
      .eq('event_id', eventId)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error checking event processing status:', error);
      return false;
    }

    if (data) {
      // Check if it's the same payload (to handle replay attacks)
      if (data.payload_hash === payloadHash) {
        return data.status === 'completed';
      } else {
        console.warn('Event ID reused with different payload', {
          eventId,
          existingHash: data.payload_hash,
          newHash: payloadHash
        });
        return false;
      }
    }

    return false;
  } catch (error) {
    console.error('Error checking event idempotency:', error);
    return false;
  }
}

// Sanitize webhook payload for logging (remove sensitive data)
function sanitizeWebhookPayload(payload: any): any {
  if (!payload || typeof payload !== 'object') return payload;

  const sanitized = { ...payload };
  
  // Remove or mask sensitive fields
  if (sanitized.data) {
    const data = { ...sanitized.data };
    
    // Mask customer information
    if (data.customer) {
      data.customer = {
        ...data.customer,
        email: data.customer.email ? 
          data.customer.email.replace(/(.{2}).*@/, '$1***@') : undefined,
        phone: data.customer.phone ? '[REDACTED]' : undefined
      };
    }
    
    // Mask authorization data
    if (data.authorization) {
      data.authorization = '[REDACTED]';
    }
    
    // Keep metadata structure but mask sensitive values
    if (data.metadata) {
      data.metadata = Object.keys(data.metadata).reduce((acc, key) => {
        if (typeof data.metadata[key] === 'string' && key !== 'plan' && key !== 'subscription_type') {
          acc[key] = '[REDACTED]';
        } else {
          acc[key] = data.metadata[key];
        }
        return acc;
      }, {} as any);
    }
    
    sanitized.data = data;
  }
  
  return sanitized;
}

// Paystack webhook endpoint
router.post("/paystack", webhookEventLogger, async (req: Request, res: Response) => {
  const startTime = Date.now();
  let eventId = 'unknown';
  let eventType = 'unknown';
  
  try {
    // Extract basic event information for logging
    const { event, data } = req.body || {};
    eventType = event || 'unknown';
    eventId = data?.id || data?.reference || `${eventType}_${Date.now()}`;

    console.info('Webhook received', {
      eventId,
      eventType,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      contentLength: req.get('Content-Length')
    });

    // Log webhook received event
    await paymentEventLogger.logWebhookEvent({
      eventId,
      eventType,
      reference: data?.reference,
      status: 'received',
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    // Validate request body
    if (!req.body || typeof req.body !== 'object') {
      console.warn('Invalid webhook payload - not an object', { eventId, eventType });
      return res.status(400).json({
        success: false,
        message: 'Invalid payload format'
      });
    }

    // Validate Paystack configuration
    if (!paystackConfig.secretKey) {
      console.error("Paystack secret key not configured", { eventId, eventType });
      await logWebhookEvent(eventId, eventType, req.body, 'failed', undefined, 'Paystack secret key not configured');
      return res.status(500).json({
        success: false,
        message: 'Payment service configuration error'
      });
    }

    // Use webhook secret if available, otherwise fall back to secret key
    const webhookSecret = paystackConfig.webhookSecret || paystackConfig.secretKey;

    if (!paystackConfig.webhookSecret) {
      console.warn('Using secret key for webhook verification - webhook secret not configured', { eventId, eventType });
    }

    // Get the raw body for signature verification
    const rawBody = JSON.stringify(req.body);
    
    // Verify that the request is from Paystack using enhanced signature verification
    const computedHash = crypto
      .createHmac("sha512", webhookSecret)
      .update(rawBody)
      .digest("hex");

    // Get signature from headers
    const signature = req.headers["x-paystack-signature"];
    if (!signature || typeof signature !== "string") {
      console.warn('Missing webhook signature', { 
        eventId, 
        eventType,
        headers: Object.keys(req.headers)
      });
      await logWebhookEvent(eventId, eventType, req.body, 'failed', data?.reference, 'Missing signature');
      return res.status(401).json({
        success: false,
        message: 'Missing signature'
      });
    }

    // Enhanced signature verification with timing-safe comparison
    try {
      // Ensure both strings are the same length by padding if necessary
      const maxLength = Math.max(computedHash.length, signature.length);
      const paddedHash = computedHash.padEnd(maxLength, '0');
      const paddedSignature = signature.padEnd(maxLength, '0');

      const hashBuffer = Buffer.from(paddedHash, "utf8");
      const signatureBuffer = Buffer.from(paddedSignature, "utf8");

      // Use timing-safe comparison to prevent timing attacks
      const isValidSignature = timingSafeEqual(hashBuffer, signatureBuffer) && 
                              computedHash.length === signature.length;

      if (!isValidSignature) {
        console.warn('Invalid webhook signature', {
          eventId,
          eventType,
          signatureLength: signature.length,
          computedHashLength: computedHash.length,
          ip: req.ip
        });
        await logWebhookEvent(eventId, eventType, req.body, 'failed', data?.reference, 'Invalid signature');
        return res.status(401).json({
          success: false,
          message: 'Invalid signature'
        });
      }
    } catch (error) {
      console.error("Signature verification error", {
        eventId,
        eventType,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      await logWebhookEvent(eventId, eventType, req.body, 'failed', data?.reference, 'Signature verification failed');
      return res.status(401).json({
        success: false,
        message: 'Signature verification failed'
      });
    }

    console.info('Webhook signature verified successfully', { eventId, eventType });

    // Check for idempotency - has this event already been processed?
    const payloadHash = createPayloadHash(req.body);
    const alreadyProcessed = await isEventAlreadyProcessed(eventId, payloadHash);
    
    if (alreadyProcessed) {
      console.info('Webhook event already processed - returning success', { eventId, eventType });
      await logWebhookEvent(eventId, eventType, req.body, 'duplicate', data?.reference);
      return res.status(200).json({
        success: true,
        message: 'Event already processed'
      });
    }

    // Log event as received and being processed
    await logWebhookEvent(eventId, eventType, req.body, 'processing', data?.reference);

    // Process the webhook event with comprehensive error handling
    let processingResult: WebhookProcessingResult;

    try {
      processingResult = await processWebhookEvent(eventType, data, eventId);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown processing error';
      console.error('Webhook event processing failed', {
        eventId,
        eventType,
        error: errorMessage
      });
      
      await logWebhookEvent(eventId, eventType, req.body, 'failed', data?.reference, errorMessage);
      
      return res.status(500).json({
        success: false,
        message: 'Event processing failed',
        eventId
      });
    }

    // Log final processing result
    const finalStatus = processingResult.success ? 'completed' : 'failed';
    await logWebhookEvent(
      eventId, 
      eventType, 
      req.body, 
      finalStatus, 
      data?.reference, 
      processingResult.error
    );

    const processingTime = Date.now() - startTime;
    console.info('Webhook processing completed', {
      eventId,
      eventType,
      success: processingResult.success,
      processingTimeMs: processingTime,
      message: processingResult.message
    });

    // Log final webhook processing result
    await paymentEventLogger.logWebhookEvent({
      eventId,
      eventType,
      reference: data?.reference,
      status: processingResult.success ? 'processed' : 'failed',
      processingTime,
      errorMessage: processingResult.error,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    // Return appropriate response
    if (processingResult.success) {
      return res.status(200).json({
        success: true,
        message: processingResult.message,
        eventId,
        processingTime: `${processingTime}ms`
      });
    } else {
      return res.status(422).json({
        success: false,
        message: processingResult.message,
        eventId,
        error: processingResult.error
      });
    }

  } catch (error) {
    const processingTime = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    console.error("Unexpected webhook processing error", {
      eventId,
      eventType,
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined,
      processingTimeMs: processingTime
    });

    // Log the failure
    await logWebhookEvent(eventId, eventType, req.body || {}, 'failed', undefined, errorMessage);

    return res.status(500).json({
      success: false,
      message: 'Internal server error during webhook processing',
      eventId
    });
  }
});

// Process different webhook event types
async function processWebhookEvent(
  eventType: string, 
  data: any, 
  eventId: string
): Promise<WebhookProcessingResult> {
  
  console.info('Processing webhook event', {
    eventId,
    eventType,
    sanitizedData: sanitizeWebhookPayload({ data })
  });

  switch (eventType) {
    case "charge.success":
      return await processChargeSuccessEvent(data, eventId);
    
    case "charge.failed":
      return await processChargeFailedEvent(data, eventId);
    
    case "subscription.create":
      return await processSubscriptionCreateEvent(data, eventId);
    
    case "subscription.disable":
      return await processSubscriptionDisableEvent(data, eventId);
    
    case "subscription.not_renew":
      return await processSubscriptionNotRenewEvent(data, eventId);
    
    case "invoice.create":
      return await processInvoiceCreateEvent(data, eventId);
    
    case "invoice.payment_failed":
      return await processInvoicePaymentFailedEvent(data, eventId);
    
    case "customeridentification.success":
      return await processCustomerIdentificationSuccessEvent(data, eventId);
    
    case "customeridentification.failed":
      return await processCustomerIdentificationFailedEvent(data, eventId);
    
    default:
      console.warn('Unhandled webhook event type', { eventId, eventType });
      return {
        success: true,
        message: `Event type '${eventType}' received but not processed`,
        eventId
      };
  }
}

// Process charge.success event
async function processChargeSuccessEvent(data: any, eventId: string): Promise<WebhookProcessingResult> {
  try {
    const { reference, amount, customer, metadata } = data;

    if (!customer?.email) {
      return {
        success: false,
        message: 'Missing customer email in charge.success event',
        eventId,
        error: 'Invalid event data'
      };
    }

    if (!reference) {
      return {
        success: false,
        message: 'Missing payment reference in charge.success event',
        eventId,
        error: 'Invalid event data'
      };
    }

    if (!amount || amount <= 0) {
      return {
        success: false,
        message: 'Invalid amount in charge.success event',
        eventId,
        error: 'Invalid event data'
      };
    }

    // Extract plan information from metadata
    let planId: string | null = null;

    if (metadata) {
      // Check various metadata structures
      if (metadata.custom_fields && Array.isArray(metadata.custom_fields)) {
        const planField = metadata.custom_fields.find(
          (field: { variable_name: string; value: string }) => field.variable_name === "plan"
        );
        planId = planField?.value || null;
      }

      if (!planId && metadata.plan) {
        planId = metadata.plan;
      }

      if (!planId && metadata.subscription_type) {
        planId = metadata.subscription_type;
      }
    }

    // Default to 'pro' if no plan is specified
    if (!planId) {
      planId = "pro";
      console.warn('No plan specified in metadata, defaulting to pro', {
        eventId,
        reference,
        customer: customer.email.replace(/(.{2}).*@/, '$1***@')
      });
    }

    console.info('Processing successful charge', {
      eventId,
      reference,
      planId,
      amount,
      customer: customer.email.replace(/(.{2}).*@/, '$1***@')
    });

    // Update user subscription
    const subscriptionResult = await updateUserSubscription(
      customer.email,
      planId,
      amount,
      reference
    );

    if (subscriptionResult.success) {
      console.info('Subscription updated successfully via webhook', {
        eventId,
        reference,
        customer: customer.email.replace(/(.{2}).*@/, '$1***@'),
        planId
      });

      return {
        success: true,
        message: 'Payment processed and subscription updated successfully',
        eventId
      };
    } else {
      console.error('Failed to update subscription via webhook', {
        eventId,
        reference,
        customer: customer.email.replace(/(.{2}).*@/, '$1***@'),
        error: subscriptionResult.error
      });

      return {
        success: false,
        message: 'Failed to update subscription',
        eventId,
        error: subscriptionResult.error
      };
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error processing charge.success event', {
      eventId,
      error: errorMessage
    });

    return {
      success: false,
      message: 'Error processing successful charge',
      eventId,
      error: errorMessage
    };
  }
}

// Process charge.failed event
async function processChargeFailedEvent(data: any, eventId: string): Promise<WebhookProcessingResult> {
  console.info('Processing failed charge', {
    eventId,
    reference: data.reference,
    gateway_response: data.gateway_response,
    customer: data.customer?.email?.replace(/(.{2}).*@/, '$1***@')
  });

  // Log failed payment for monitoring
  // Could implement retry logic or notification system here
  
  return {
    success: true,
    message: 'Failed charge event processed',
    eventId
  };
}

// Process subscription.create event
async function processSubscriptionCreateEvent(data: any, eventId: string): Promise<WebhookProcessingResult> {
  console.info('Processing subscription creation', {
    eventId,
    subscriptionCode: data.subscription_code,
    customer: data.customer?.email?.replace(/(.{2}).*@/, '$1***@')
  });

  return {
    success: true,
    message: 'Subscription creation event processed',
    eventId
  };
}

// Process subscription.disable event
async function processSubscriptionDisableEvent(data: any, eventId: string): Promise<WebhookProcessingResult> {
  try {
    if (data.subscription_code) {
      console.info('Processing subscription disable', {
        eventId,
        subscriptionCode: data.subscription_code
      });

      // Find and disable the subscription
      const { data: subscriptionData, error: findError } = await supabase
        .from("subscriptions")
        .select("id, user_id")
        .eq("subscription_code", data.subscription_code)
        .single();

      if (findError && findError.code !== 'PGRST116') {
        console.error('Error finding subscription to disable', {
          eventId,
          subscriptionCode: data.subscription_code,
          error: findError
        });
        return {
          success: false,
          message: 'Error finding subscription to disable',
          eventId,
          error: findError.message
        };
      }

      if (subscriptionData) {
        // Update subscription status
        const { error: updateError } = await supabase
          .from("subscriptions")
          .update({
            is_active: false,
            updated_at: new Date().toISOString(),
          })
          .eq("id", subscriptionData.id);

        if (updateError) {
          console.error('Error disabling subscription', {
            eventId,
            subscriptionId: subscriptionData.id,
            error: updateError
          });
          return {
            success: false,
            message: 'Error disabling subscription',
            eventId,
            error: updateError.message
          };
        }

        // Update user profile
        const { error: profileError } = await supabase
          .from("user_profiles")
          .update({
            is_subscribed: false,
            updated_at: new Date().toISOString()
          })
          .eq("user_id", subscriptionData.user_id);

        if (profileError) {
          console.warn('Error updating user profile after subscription disable', {
            eventId,
            userId: subscriptionData.user_id,
            error: profileError
          });
        }

        console.info('Subscription disabled successfully', {
          eventId,
          subscriptionCode: data.subscription_code,
          subscriptionId: subscriptionData.id
        });
      }
    }

    return {
      success: true,
      message: 'Subscription disable event processed',
      eventId
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error processing subscription disable event', {
      eventId,
      error: errorMessage
    });

    return {
      success: false,
      message: 'Error processing subscription disable',
      eventId,
      error: errorMessage
    };
  }
}

// Process subscription.not_renew event
async function processSubscriptionNotRenewEvent(data: any, eventId: string): Promise<WebhookProcessingResult> {
  console.info('Processing subscription not renew', {
    eventId,
    subscriptionCode: data.subscription_code
  });

  return {
    success: true,
    message: 'Subscription not renew event processed',
    eventId
  };
}

// Process invoice.create event
async function processInvoiceCreateEvent(data: any, eventId: string): Promise<WebhookProcessingResult> {
  console.info('Processing invoice creation', {
    eventId,
    invoiceCode: data.invoice_code,
    subscriptionCode: data.subscription?.subscription_code
  });

  return {
    success: true,
    message: 'Invoice creation event processed',
    eventId
  };
}

// Process invoice.payment_failed event
async function processInvoicePaymentFailedEvent(data: any, eventId: string): Promise<WebhookProcessingResult> {
  console.info('Processing invoice payment failure', {
    eventId,
    invoiceCode: data.invoice_code,
    subscriptionCode: data.subscription?.subscription_code
  });

  return {
    success: true,
    message: 'Invoice payment failed event processed',
    eventId
  };
}

// Process customeridentification.success event
async function processCustomerIdentificationSuccessEvent(data: any, eventId: string): Promise<WebhookProcessingResult> {
  console.info('Processing customer identification success', {
    eventId,
    customerCode: data.customer_code
  });

  return {
    success: true,
    message: 'Customer identification success event processed',
    eventId
  };
}

// Process customeridentification.failed event
async function processCustomerIdentificationFailedEvent(data: any, eventId: string): Promise<WebhookProcessingResult> {
  console.info('Processing customer identification failure', {
    eventId,
    customerCode: data.customer_code,
    reason: data.reason
  });

  return {
    success: true,
    message: 'Customer identification failed event processed',
    eventId
  };
}

export default router;
