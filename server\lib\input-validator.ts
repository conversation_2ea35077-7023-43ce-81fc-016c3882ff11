/**
 * Input validation and sanitization utilities
 */

import { secureLogger } from './secure-logger.js';

export interface ValidationRule {
  required?: boolean;
  type?: 'string' | 'number' | 'email' | 'uuid' | 'paystack_reference';
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  allowedValues?: string[];
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  sanitizedValue?: any;
}

/**
 * Sanitizes string input by removing potentially dangerous characters
 */
export function sanitizeString(input: string): string {
  if (typeof input !== 'string') {
    return '';
  }

  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/['"]/g, '') // Remove quotes that could break SQL
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .substring(0, 1000); // Limit length
}

/**
 * Validates email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailRegex.test(email) && email.length <= 254;
}

/**
 * Validates UUID format
 */
export function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

/**
 * Validates Paystack transaction reference format
 */
export function isValidPaystackReference(reference: string): boolean {
  // Paystack references are typically alphanumeric with underscores and hyphens
  const referenceRegex = /^[a-zA-Z0-9_-]{1,100}$/;
  return referenceRegex.test(reference);
}

/**
 * Validates a single field based on rules
 */
export function validateField(value: any, rules: ValidationRule): ValidationResult {
  const errors: string[] = [];
  let sanitizedValue = value;

  // Check if required
  if (rules.required && (value === undefined || value === null || value === '')) {
    errors.push('Field is required');
    return { isValid: false, errors };
  }

  // If not required and empty, return valid
  if (!rules.required && (value === undefined || value === null || value === '')) {
    return { isValid: true, errors: [], sanitizedValue: null };
  }

  // Type validation and sanitization
  switch (rules.type) {
    case 'string':
      if (typeof value !== 'string') {
        errors.push('Must be a string');
      } else {
        sanitizedValue = sanitizeString(value);
      }
      break;

    case 'number':
      const num = Number(value);
      if (isNaN(num)) {
        errors.push('Must be a valid number');
      } else {
        sanitizedValue = num;
      }
      break;

    case 'email':
      if (typeof value !== 'string' || !isValidEmail(value)) {
        errors.push('Must be a valid email address');
      } else {
        sanitizedValue = sanitizeString(value.toLowerCase());
      }
      break;

    case 'uuid':
      if (typeof value !== 'string' || !isValidUUID(value)) {
        errors.push('Must be a valid UUID');
      } else {
        sanitizedValue = value.toLowerCase();
      }
      break;

    case 'paystack_reference':
      if (typeof value !== 'string' || !isValidPaystackReference(value)) {
        errors.push('Must be a valid Paystack reference');
      } else {
        sanitizedValue = sanitizeString(value);
      }
      break;
  }

  // Length validation
  if (rules.minLength && typeof sanitizedValue === 'string' && sanitizedValue.length < rules.minLength) {
    errors.push(`Must be at least ${rules.minLength} characters long`);
  }

  if (rules.maxLength && typeof sanitizedValue === 'string' && sanitizedValue.length > rules.maxLength) {
    errors.push(`Must be no more than ${rules.maxLength} characters long`);
  }

  // Pattern validation
  if (rules.pattern && typeof sanitizedValue === 'string' && !rules.pattern.test(sanitizedValue)) {
    errors.push('Does not match required format');
  }

  // Allowed values validation
  if (rules.allowedValues && !rules.allowedValues.includes(sanitizedValue)) {
    errors.push(`Must be one of: ${rules.allowedValues.join(', ')}`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    sanitizedValue
  };
}

/**
 * Validates an object based on field rules
 */
export function validateObject(data: any, rules: Record<string, ValidationRule>): {
  isValid: boolean;
  errors: Record<string, string[]>;
  sanitizedData: Record<string, any>;
} {
  const errors: Record<string, string[]> = {};
  const sanitizedData: Record<string, any> = {};
  let isValid = true;

  for (const [field, rule] of Object.entries(rules)) {
    const result = validateField(data[field], rule);
    
    if (!result.isValid) {
      errors[field] = result.errors;
      isValid = false;
    } else {
      sanitizedData[field] = result.sanitizedValue;
    }
  }

  return { isValid, errors, sanitizedData };
}

/**
 * Middleware factory for request validation
 */
export function createValidationMiddleware(rules: Record<string, ValidationRule>) {
  return (req: any, res: any, next: any) => {
    const validation = validateObject(req.body, rules);
    
    if (!validation.isValid) {
      secureLogger.security('Input validation failed', {
        path: req.path,
        method: req.method,
        errors: validation.errors,
        ip: req.ip
      });

      return res.status(400).json({
        status: 'error',
        message: 'Invalid input data',
        errors: validation.errors
      });
    }

    // Replace request body with sanitized data
    req.body = validation.sanitizedData;
    next();
  };
}

/**
 * Common validation rules for payment-related endpoints
 */
export const PAYMENT_VALIDATION_RULES = {
  email: {
    required: true,
    type: 'email' as const,
    maxLength: 254
  },
  reference: {
    required: true,
    type: 'paystack_reference' as const,
    minLength: 1,
    maxLength: 100
  },
  planId: {
    required: true,
    type: 'string' as const,
    allowedValues: ['weekly', 'monthly', 'yearly']
  },
  amount: {
    required: true,
    type: 'number' as const
  }
};

/**
 * Validation rules for webhook data
 */
export const WEBHOOK_VALIDATION_RULES = {
  event: {
    required: true,
    type: 'string' as const,
    allowedValues: ['charge.success', 'subscription.create', 'subscription.disable']
  },
  data: {
    required: true
  }
};