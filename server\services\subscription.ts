import { supabase } from '../lib/supabase.js';
import { User } from '@supabase/supabase-js';
import { secureLogger } from '../lib/secure-logger.js';
import { paymentEventLogger, PaymentEventType } from '../lib/payment-event-logger.js';

// Define interfaces for subscription data
interface SubscriptionData {
  id: string;
  user_id: string;
  plan_id: string;
  amount_paid: number;
  start_date: string;
  end_date: string;
  is_active: boolean;
  last_payment_reference: string;
  created_at: string;
  updated_at: string;
}

interface UpdateSubscriptionResult {
  success: boolean;
  data?: {
    userId: string;
    planId: string;
    expiresAt?: string;
    endDate?: string;
    paymentId?: string;
    subscriptionId?: string;
    reference?: string;
  };
  error?: string;
  errorCode?: string;
}

interface SubscriptionStatusResult {
  success: boolean;
  data?: {
    userId: string;
    isSubscribed: boolean;
    subscriptionStatus: string;
    expiresAt?: string;
    planId?: string;
    isExpired?: boolean;
  };
  error?: string;
  errorCode?: string;
}

interface CleanupResult {
  success: boolean;
  data?: {
    expiredCount: number;
    cleanedUsers: string[];
  };
  error?: string;
  errorCode?: string;
}

// Enhanced error types for better error handling
enum SubscriptionErrorCode {
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  INVALID_PLAN = 'INVALID_PLAN',
  DUPLICATE_PAYMENT = 'DUPLICATE_PAYMENT',
  DATABASE_ERROR = 'DATABASE_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  TRANSACTION_FAILED = 'TRANSACTION_FAILED',
  NETWORK_ERROR = 'NETWORK_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

// Define subscription plans and their durations
const SUBSCRIPTION_DURATIONS: Record<string, number> = {
  'basic': 7, // 7 days (weekly)
  'pro': 7,   // 7 days (weekly)
  'elite': 365 // 365 days (effectively lifetime or one-time)
};

export async function updateUserSubscription(email: string, planId: string, amount: number, reference: string): Promise<UpdateSubscriptionResult> {
  // Input validation
  if (!email || !planId || !amount || !reference) {
    return {
      success: false,
      error: 'Missing required parameters: email, planId, amount, and reference are required',
      errorCode: SubscriptionErrorCode.VALIDATION_ERROR
    };
  }

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return {
      success: false,
      error: 'Invalid email format',
      errorCode: SubscriptionErrorCode.VALIDATION_ERROR
    };
  }

  // Validate plan ID
  const validPlans = ['basic', 'pro', 'elite', 'premium'];
  if (!validPlans.includes(planId)) {
    return {
      success: false,
      error: `Invalid plan ID. Must be one of: ${validPlans.join(', ')}`,
      errorCode: SubscriptionErrorCode.INVALID_PLAN
    };
  }

  // Validate amount
  if (amount <= 0) {
    return {
      success: false,
      error: 'Amount must be greater than 0',
      errorCode: SubscriptionErrorCode.VALIDATION_ERROR
    };
  }

  try {
    secureLogger.payment('Updating subscription', reference, { planId, amount });

    // First, get the user ID from email
    let userId: string;
    try {
      const { data: userData, error: userError } = await supabase.auth.admin.listUsers();
      
      if (userError) {
        console.error('Error fetching users:', userError);
        return {
          success: false,
          error: 'Failed to fetch user data',
          errorCode: SubscriptionErrorCode.DATABASE_ERROR
        };
      }

      const user = userData.users.find((u: User) => u.email === email);
      if (!user) {
        console.error('User not found with email:', email);
        return {
          success: false,
          error: 'User not found',
          errorCode: SubscriptionErrorCode.USER_NOT_FOUND
        };
      }

      userId = user.id;
    } catch (error) {
      console.error('Error finding user:', error);
      return {
        success: false,
        error: 'Failed to find user',
        errorCode: SubscriptionErrorCode.DATABASE_ERROR
      };
    }

    // Use the new atomic payment success function
    const { data, error } = await supabase.rpc('handle_payment_success_atomic', {
      p_user_id: userId,
      p_plan_id: planId,
      p_amount: amount / 100, // Convert from kobo to naira
      p_reference: reference,
      p_currency: 'NGN'
    });

    if (error) {
      console.error('Error calling handle_payment_success_atomic:', error);
      
      // Determine error type and provide appropriate fallback
      if (error.message?.includes('function') && error.message?.includes('does not exist')) {
        secureLogger.warn('Atomic function not available, using fallback method');
        return await updateUserSubscriptionFallback(email, planId, amount, reference);
      }

      return {
        success: false,
        error: `Database operation failed: ${error.message}`,
        errorCode: SubscriptionErrorCode.DATABASE_ERROR
      };
    }

    // Parse the response from the database function
    if (!data || typeof data !== 'object') {
      console.error('Invalid response from database function:', data);
      return {
        success: false,
        error: 'Invalid response from payment processing',
        errorCode: SubscriptionErrorCode.TRANSACTION_FAILED
      };
    }

    if (!data.success) {
      console.error('Payment processing failed:', data.error);
      
      // Map database errors to appropriate error codes
      let errorCode = SubscriptionErrorCode.TRANSACTION_FAILED;
      if (data.error?.includes('already processed')) {
        errorCode = SubscriptionErrorCode.DUPLICATE_PAYMENT;
      } else if (data.error?.includes('not found')) {
        errorCode = SubscriptionErrorCode.USER_NOT_FOUND;
      }

      return {
        success: false,
        error: data.error || 'Payment processing failed',
        errorCode
      };
    }

    secureLogger.payment('Payment processed successfully', data?.reference);
    
    // Log subscription update success
    await paymentEventLogger.logSubscriptionEvent({
      eventType: PaymentEventType.SUBSCRIPTION_UPDATED,
      userId: data.user_id,
      planId: data.plan_id,
      reference: data.reference,
      metadata: {
        subscription_id: data.subscription_id,
        payment_id: data.payment_id,
        expires_at: data.end_date
      }
    });
    
    return {
      success: true,
      data: {
        userId: data.user_id,
        planId: data.plan_id,
        expiresAt: data.end_date,
        paymentId: data.payment_id,
        subscriptionId: data.subscription_id,
        reference: data.reference
      }
    };

  } catch (error) {
    console.error('Error in updateUserSubscription:', error);
    
    // Determine error type
    let errorCode = SubscriptionErrorCode.UNKNOWN_ERROR;
    let errorMessage = 'Unknown error occurred';

    if (error instanceof Error) {
      errorMessage = error.message;
      
      // Network/connection errors
      if (error.message.includes('network') || error.message.includes('connection')) {
        errorCode = SubscriptionErrorCode.NETWORK_ERROR;
      }
      // Database errors
      else if (error.message.includes('database') || error.message.includes('relation')) {
        errorCode = SubscriptionErrorCode.DATABASE_ERROR;
      }
    }

    return {
      success: false,
      error: errorMessage,
      errorCode
    };
  }
}

// Fallback function using the old method
async function updateUserSubscriptionFallback(email: string, planId: string, amount: number, reference: string): Promise<UpdateSubscriptionResult> {
  try {
    // First, get the user ID from the email using auth.users
    const { data: userData, error: userError } = await supabase.auth.admin.listUsers();

    if (userError) {
      console.error('Error fetching users:', userError);
      return {
        success: false,
        error: 'Failed to fetch user data'
      };
    }

    // Find the user by email
    const user = userData.users.find((u: User) => u.email === email);

    if (!user) {
      console.error('User not found with email:', email);
      return {
        success: false,
        error: 'User not found'
      };
    }

    const userId = user.id;

    secureLogger.info(`Processing subscription update for user ${userId} with plan ${planId}`);

    // Calculate subscription end date based on plan
    const startDate = new Date();
    const endDate = new Date();
    endDate.setDate(startDate.getDate() + (SUBSCRIPTION_DURATIONS[planId] || 7));

    secureLogger.debug(`Subscription dates: start=${startDate.toISOString()}, end=${endDate.toISOString()}`);

    // Check if user already has a subscription
    const { data: existingSubscription } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle(); // Use maybeSingle instead of single to avoid errors when no record exists

    let result;

    if (existingSubscription) {
      secureLogger.info(`Updating existing subscription for user ${userId}`);
      // Update existing subscription
      result = await supabase
        .from('subscriptions')
        .update({
          plan_id: planId,
          amount_paid: amount / 100, // Convert from kobo to naira
          start_date: startDate.toISOString(),
          end_date: endDate.toISOString(),
          is_active: true,
          last_payment_reference: reference,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);
    } else {
      secureLogger.info(`Creating new subscription for user ${userId}`);
      // Create new subscription
      result = await supabase
        .from('subscriptions')
        .insert({
          user_id: userId,
          plan_id: planId,
          amount_paid: amount / 100, // Convert from kobo to naira
          start_date: startDate.toISOString(),
          end_date: endDate.toISOString(),
          is_active: true,
          last_payment_reference: reference,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
    }

    if (result.error) {
      console.error('Error updating/creating subscription:', result.error);
      return {
        success: false,
        error: result.error.message || 'Failed to update subscription'
      };
    }

    secureLogger.info('Subscription updated successfully, logging payment transaction...');

    // Also log the payment in a transactions table
    const transactionResult = await supabase
      .from('payment_transactions')
      .insert({
        user_id: userId,
        reference: reference,
        amount: amount / 100, // Convert from kobo to naira
        plan_id: planId,
        status: 'success',
        payment_method: 'paystack',
        created_at: new Date().toISOString()
      });

    if (transactionResult.error) {
      console.error('Error logging payment transaction:', transactionResult.error);
      // Don't fail the whole process for transaction logging errors
    } else {
      secureLogger.info('Payment transaction logged successfully');
    }

    secureLogger.info('Updating user profile...');

    // Update user_profiles table to ensure subscription status is consistent
    const { data: userProfile } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle();

    let profileResult;
    if (userProfile) {
      secureLogger.debug('Updating existing user profile');
      // Update existing profile
      profileResult = await supabase
        .from('user_profiles')
        .update({
          is_subscribed: true,
          subscription_expires_at: endDate.toISOString()
        })
        .eq('user_id', userId);
    } else {
      secureLogger.debug('Creating new user profile');
      // Create new profile - let the database handle id, created_at, and updated_at with defaults
      profileResult = await supabase
        .from('user_profiles')
        .insert({
          user_id: userId,
          is_subscribed: true,
          subscription_expires_at: endDate.toISOString()
        });
    }

    if (profileResult.error) {
      console.error('Error updating user profile:', profileResult.error);
      // Don't fail the whole process for profile update errors, but log them
    } else {
      secureLogger.info('User profile updated successfully');
    }

    secureLogger.info(`Subscription update completed successfully for user ${userId}`);

    return {
      success: true,
      data: {
        userId,
        planId,
        endDate: endDate.toISOString(),
        reference
      }
    };
  } catch (error) {
    console.error('Error updating subscription:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

export async function isUserSubscribed(userId: string): Promise<boolean> {
  try {
    if (!userId) {
      console.warn('isUserSubscribed called with empty userId');
      return false;
    }

    // Use the new atomic function to get comprehensive subscription status
    const statusResult = await getSubscriptionStatus(userId);
    
    if (!statusResult.success) {
      console.error('Error getting subscription status:', statusResult.error);
      return false;
    }

    return statusResult.data?.isSubscribed || false;

  } catch (error) {
    console.error('Error checking subscription:', error);
    return false;
  }
}

/**
 * Enhanced subscription status checking with comprehensive error handling
 */
export async function getSubscriptionStatus(userId: string): Promise<SubscriptionStatusResult> {
  // Input validation
  if (!userId) {
    return {
      success: false,
      error: 'User ID is required',
      errorCode: SubscriptionErrorCode.VALIDATION_ERROR
    };
  }

  try {
    // Use the new database function for comprehensive status checking
    const { data, error } = await supabase.rpc('get_subscription_status', {
      p_user_id: userId
    });

    if (error) {
      console.error('Error calling get_subscription_status:', error);
      
      // Fallback to manual checking if function doesn't exist
      if (error.message?.includes('function') && error.message?.includes('does not exist')) {
        return await getSubscriptionStatusFallback(userId);
      }

      return {
        success: false,
        error: `Database error: ${error.message}`,
        errorCode: SubscriptionErrorCode.DATABASE_ERROR
      };
    }

    if (!data || !data.success) {
      return {
        success: false,
        error: data?.error || 'Failed to get subscription status',
        errorCode: SubscriptionErrorCode.TRANSACTION_FAILED
      };
    }

    // Parse the comprehensive status data
    const profile = data.profile;
    const subscription = data.subscription;

    let isSubscribed = false;
    let subscriptionStatus = 'free';
    let expiresAt: string | undefined;
    let planId: string | undefined;
    let isExpired = false;

    if (profile) {
      isSubscribed = profile.is_subscribed || false;
      subscriptionStatus = profile.subscription_status || 'free';
      expiresAt = profile.subscription_expires_at;
      planId = profile.subscription_plan;
    }

    if (subscription) {
      isExpired = subscription.is_expired || false;
      if (isExpired && isSubscribed) {
        // Update status if subscription is expired but profile shows active
        await updateSubscriptionStatusAtomic(userId, 'expired', 'Subscription expired during status check');
        isSubscribed = false;
        subscriptionStatus = 'expired';
      }
    }

    return {
      success: true,
      data: {
        userId,
        isSubscribed,
        subscriptionStatus,
        expiresAt,
        planId,
        isExpired
      }
    };

  } catch (error) {
    console.error('Error in getSubscriptionStatus:', error);
    
    let errorCode = SubscriptionErrorCode.UNKNOWN_ERROR;
    if (error instanceof Error) {
      if (error.message.includes('network') || error.message.includes('connection')) {
        errorCode = SubscriptionErrorCode.NETWORK_ERROR;
      } else if (error.message.includes('database')) {
        errorCode = SubscriptionErrorCode.DATABASE_ERROR;
      }
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      errorCode
    };
  }
}

/**
 * Fallback method for subscription status checking
 */
async function getSubscriptionStatusFallback(userId: string): Promise<SubscriptionStatusResult> {
  try {
    // Check user_profiles table first
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('is_subscribed, subscription_expires_at, subscription_status, subscription_plan')
      .eq('user_id', userId)
      .maybeSingle();

    if (profileError) {
      console.error('Error fetching user profile:', profileError);
      return {
        success: false,
        error: 'Failed to fetch user profile',
        errorCode: SubscriptionErrorCode.DATABASE_ERROR
      };
    }

    let isSubscribed = false;
    let subscriptionStatus = 'free';
    let expiresAt: string | undefined;
    let planId: string | undefined;
    let isExpired = false;

    if (profile) {
      isSubscribed = profile.is_subscribed || false;
      subscriptionStatus = profile.subscription_status || 'free';
      expiresAt = profile.subscription_expires_at;
      planId = profile.subscription_plan;

      // Check if subscription has expired
      if (expiresAt) {
        const expirationDate = new Date(expiresAt);
        const now = new Date();
        isExpired = expirationDate <= now;

        if (isExpired && isSubscribed) {
          // Update status atomically
          await updateSubscriptionStatusAtomic(userId, 'expired', 'Subscription expired during fallback check');
          isSubscribed = false;
          subscriptionStatus = 'expired';
        }
      }
    }

    return {
      success: true,
      data: {
        userId,
        isSubscribed,
        subscriptionStatus,
        expiresAt,
        planId,
        isExpired
      }
    };

  } catch (error) {
    console.error('Error in getSubscriptionStatusFallback:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      errorCode: SubscriptionErrorCode.UNKNOWN_ERROR
    };
  }
}

/**
 * Update subscription status atomically using database function
 */
export async function updateSubscriptionStatusAtomic(
  userId: string, 
  status: string, 
  reason?: string
): Promise<SubscriptionStatusResult> {
  // Input validation
  if (!userId || !status) {
    return {
      success: false,
      error: 'User ID and status are required',
      errorCode: SubscriptionErrorCode.VALIDATION_ERROR
    };
  }

  const validStatuses = ['active', 'inactive', 'expired', 'cancelled', 'suspended'];
  if (!validStatuses.includes(status)) {
    return {
      success: false,
      error: `Invalid status. Must be one of: ${validStatuses.join(', ')}`,
      errorCode: SubscriptionErrorCode.VALIDATION_ERROR
    };
  }

  try {
    const { data, error } = await supabase.rpc('update_subscription_status_atomic', {
      p_user_id: userId,
      p_status: status,
      p_reason: reason || null
    });

    if (error) {
      console.error('Error calling update_subscription_status_atomic:', error);
      return {
        success: false,
        error: `Database error: ${error.message}`,
        errorCode: SubscriptionErrorCode.DATABASE_ERROR
      };
    }

    if (!data || !data.success) {
      return {
        success: false,
        error: data?.error || 'Failed to update subscription status',
        errorCode: SubscriptionErrorCode.TRANSACTION_FAILED
      };
    }

    return {
      success: true,
      data: {
        userId: data.user_id,
        isSubscribed: status === 'active',
        subscriptionStatus: status
      }
    };

  } catch (error) {
    console.error('Error in updateSubscriptionStatusAtomic:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      errorCode: SubscriptionErrorCode.UNKNOWN_ERROR
    };
  }
}

export async function getUserSubscriptionDetails(userId: string) {
  try {
    if (!userId) {
      console.warn('getUserSubscriptionDetails called with empty userId');
      return null;
    }

    // Use the enhanced subscription status function
    const statusResult = await getSubscriptionStatus(userId);
    
    if (!statusResult.success) {
      console.error('Error getting subscription details:', statusResult.error);
      return null;
    }

    const statusData = statusResult.data;
    if (!statusData) {
      return {
        status: 'free',
        isExpired: false
      };
    }

    // Get additional subscription details if user is subscribed
    if (statusData.isSubscribed || statusData.subscriptionStatus !== 'free') {
      try {
        const { data: subscription, error } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('user_id', userId)
          .order('created_at', { ascending: false })
          .limit(1)
          .maybeSingle();

        if (!error && subscription) {
          return {
            ...subscription,
            status: statusData.subscriptionStatus,
            isExpired: statusData.isExpired || false,
            expiresAt: statusData.expiresAt
          };
        }
      } catch (error) {
        console.error('Error fetching subscription details:', error);
      }
    }

    // Return basic status information
    return {
      status: statusData.subscriptionStatus,
      isExpired: statusData.isExpired || false,
      expiresAt: statusData.expiresAt,
      planId: statusData.planId
    };

  } catch (error) {
    console.error('Error getting subscription details:', error);
    return null;
  }
}

/**
 * Enhanced function to check for expired subscriptions and update their status atomically
 */
export async function checkAndUpdateExpiredSubscriptions(): Promise<CleanupResult> {
  try {
    secureLogger.info('Starting expired subscription cleanup process');

    // Use the new atomic function for comprehensive expiration checking
    const { data, error } = await supabase.rpc('check_subscription_expiration');

    if (error) {
      console.error('Error calling check_subscription_expiration:', error);
      
      // Fallback to manual checking if function doesn't exist
      if (error.message?.includes('function') && error.message?.includes('does not exist')) {
        return await checkAndUpdateExpiredSubscriptionsFallback();
      }

      return {
        success: false,
        error: `Database error: ${error.message}`,
        errorCode: SubscriptionErrorCode.DATABASE_ERROR
      };
    }

    if (!data || !data.success) {
      return {
        success: false,
        error: data?.error || 'Failed to check expired subscriptions',
        errorCode: SubscriptionErrorCode.TRANSACTION_FAILED
      };
    }

    secureLogger.info(`Expired subscription cleanup completed: ${data.expired_count} subscriptions updated`);

    return {
      success: true,
      data: {
        expiredCount: data.expired_count || 0,
        cleanedUsers: data.expired_users || []
      }
    };

  } catch (error) {
    console.error('Error in checkAndUpdateExpiredSubscriptions:', error);
    
    let errorCode = SubscriptionErrorCode.UNKNOWN_ERROR;
    if (error instanceof Error) {
      if (error.message.includes('network') || error.message.includes('connection')) {
        errorCode = SubscriptionErrorCode.NETWORK_ERROR;
      } else if (error.message.includes('database')) {
        errorCode = SubscriptionErrorCode.DATABASE_ERROR;
      }
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      errorCode
    };
  }
}

/**
 * Fallback method for expired subscription cleanup
 */
async function checkAndUpdateExpiredSubscriptionsFallback(): Promise<CleanupResult> {
  try {
    const now = new Date().toISOString();
    const cleanedUsers: string[] = [];

    // Find all active subscriptions that have expired
    const { data: expiredSubscriptions, error } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('is_active', true)
      .lt('end_date', now);

    if (error) {
      console.error('Error finding expired subscriptions:', error);
      return {
        success: false,
        error: error.message,
        errorCode: SubscriptionErrorCode.DATABASE_ERROR
      };
    }

    if (!expiredSubscriptions || expiredSubscriptions.length === 0) {
      return {
        success: true,
        data: {
          expiredCount: 0,
          cleanedUsers: []
        }
      };
    }

    // Process each expired subscription with proper error handling
    const updateResults = await Promise.allSettled(
      expiredSubscriptions.map(async (sub: SubscriptionData) => {
        try {
          // Use atomic status update function
          const result = await updateSubscriptionStatusAtomic(
            sub.user_id, 
            'expired', 
            'Subscription expired during cleanup'
          );

          if (result.success) {
            cleanedUsers.push(sub.user_id);
            return true;
          } else {
            console.error(`Failed to update subscription for user ${sub.user_id}:`, result.error);
            return false;
          }
        } catch (error) {
          console.error(`Error processing subscription for user ${sub.user_id}:`, error);
          return false;
        }
      })
    );

    // Count successful updates
    const successCount = updateResults.filter(
      (result) => result.status === 'fulfilled' && result.value === true
    ).length;

    const failedCount = expiredSubscriptions.length - successCount;
    if (failedCount > 0) {
      console.warn(`${failedCount} subscription updates failed during cleanup`);
    }

    return {
      success: true,
      data: {
        expiredCount: successCount,
        cleanedUsers
      }
    };

  } catch (error) {
    console.error('Error in checkAndUpdateExpiredSubscriptionsFallback:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      errorCode: SubscriptionErrorCode.UNKNOWN_ERROR
    };
  }
}

/**
 * Enhanced subscription cleanup with retry mechanism
 */
export async function cleanupExpiredSubscriptionsWithRetry(maxRetries: number = 3): Promise<CleanupResult> {
  let lastError: string | undefined;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      secureLogger.debug(`Cleanup attempt ${attempt}/${maxRetries}`);
      
      const result = await checkAndUpdateExpiredSubscriptions();
      
      if (result.success) {
        if (attempt > 1) {
          secureLogger.info(`Cleanup succeeded on attempt ${attempt}`);
        }
        return result;
      }
      
      lastError = result.error;
      
      if (attempt < maxRetries) {
        // Wait before retrying (exponential backoff)
        const waitTime = Math.pow(2, attempt - 1) * 1000; // 1s, 2s, 4s...
        secureLogger.warn(`Cleanup attempt ${attempt} failed, retrying in ${waitTime}ms...`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
      
    } catch (error) {
      lastError = error instanceof Error ? error.message : 'Unknown error';
      console.error(`Cleanup attempt ${attempt} threw error:`, error);
      
      if (attempt < maxRetries) {
        const waitTime = Math.pow(2, attempt - 1) * 1000;
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
  }
  
  return {
    success: false,
    error: `Cleanup failed after ${maxRetries} attempts. Last error: ${lastError}`,
    errorCode: SubscriptionErrorCode.TRANSACTION_FAILED
  };
}
/**
 * Validate subscription data before processing
 */
export function validateSubscriptionData(data: {
  email?: string;
  planId?: string;
  amount?: number;
  reference?: string;
}): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!data.email) {
    errors.push('Email is required');
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
    errors.push('Invalid email format');
  }

  if (!data.planId) {
    errors.push('Plan ID is required');
  } else if (!['basic', 'pro', 'elite', 'premium'].includes(data.planId)) {
    errors.push('Invalid plan ID');
  }

  if (!data.amount) {
    errors.push('Amount is required');
  } else if (data.amount <= 0) {
    errors.push('Amount must be greater than 0');
  }

  if (!data.reference) {
    errors.push('Payment reference is required');
  } else if (data.reference.length < 10) {
    errors.push('Payment reference must be at least 10 characters');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Get subscription error message based on error code
 */
export function getSubscriptionErrorMessage(errorCode: string): string {
  switch (errorCode) {
    case SubscriptionErrorCode.USER_NOT_FOUND:
      return 'User account not found. Please ensure you have a valid account.';
    case SubscriptionErrorCode.INVALID_PLAN:
      return 'Invalid subscription plan selected. Please choose a valid plan.';
    case SubscriptionErrorCode.DUPLICATE_PAYMENT:
      return 'This payment has already been processed. Please check your subscription status.';
    case SubscriptionErrorCode.DATABASE_ERROR:
      return 'Database error occurred. Please try again later or contact support.';
    case SubscriptionErrorCode.VALIDATION_ERROR:
      return 'Invalid data provided. Please check your input and try again.';
    case SubscriptionErrorCode.TRANSACTION_FAILED:
      return 'Transaction failed to complete. Please try again or contact support.';
    case SubscriptionErrorCode.NETWORK_ERROR:
      return 'Network error occurred. Please check your connection and try again.';
    default:
      return 'An unexpected error occurred. Please try again or contact support.';
  }
}

/**
 * Log subscription events for monitoring and debugging
 */
export async function logSubscriptionEvent(
  userId: string,
  event: string,
  data: Record<string, unknown>,
  success: boolean = true
): Promise<void> {
  try {
    const logData = {
      user_id: userId,
      amount: 0,
      currency: 'NGN',
      status: success ? 'system_log' : 'system_error',
      provider: 'system',
      provider_payment_id: `log_${Date.now()}`,
      metadata: {
        event,
        success,
        timestamp: new Date().toISOString(),
        ...data
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { error } = await supabase
      .from('payments')
      .insert(logData);

    if (error) {
      console.error('Failed to log subscription event:', error);
    }
  } catch (error) {
    console.error('Error logging subscription event:', error);
  }
}

/**
 * Health check for subscription service
 */
export async function subscriptionServiceHealthCheck(): Promise<{
  healthy: boolean;
  checks: Record<string, boolean>;
  errors: string[];
}> {
  const checks: Record<string, boolean> = {};
  const errors: string[] = [];

  try {
    // Test database connection
    const { error: dbError } = await supabase
      .from('user_profiles')
      .select('id')
      .limit(1);
    
    checks.database = !dbError;
    if (dbError) {
      errors.push(`Database connection failed: ${dbError.message}`);
    }

    // Test atomic functions availability
    const { error: functionError } = await supabase.rpc('get_subscription_status', {
      p_user_id: '00000000-0000-0000-0000-000000000000'
    });
    
    checks.atomicFunctions = !functionError || !functionError.message?.includes('does not exist');
    if (functionError && functionError.message?.includes('does not exist')) {
      errors.push('Atomic subscription functions not available');
    }

    // Test subscription table access
    const { error: subscriptionError } = await supabase
      .from('subscriptions')
      .select('id')
      .limit(1);
    
    checks.subscriptionTable = !subscriptionError;
    if (subscriptionError) {
      errors.push(`Subscription table access failed: ${subscriptionError.message}`);
    }

    // Test payments table access
    const { error: paymentError } = await supabase
      .from('payments')
      .select('id')
      .limit(1);
    
    checks.paymentTable = !paymentError;
    if (paymentError) {
      errors.push(`Payment table access failed: ${paymentError.message}`);
    }

    const healthy = Object.values(checks).every(check => check);

    return {
      healthy,
      checks,
      errors
    };

  } catch (error) {
    return {
      healthy: false,
      checks: { general: false },
      errors: [error instanceof Error ? error.message : 'Unknown error']
    };
  }
}

// Export error codes for use in other modules
export { SubscriptionErrorCode };