-- Create a working get_all_user_profiles function
-- This version uses a completely different approach to avoid any conflicts

-- First, drop any existing function
DROP FUNCTION IF EXISTS public.get_all_user_profiles() CASCADE;

-- Create the function using a different structure
CREATE OR REPLACE FUNCTION public.get_all_user_profiles()
RETURNS SETOF RECORD
AS $function$
DECLARE
    current_user_uuid UUID;
    admin_check BOOLEAN;
    rec RECORD;
BEGIN
    -- Get current user
    current_user_uuid := auth.uid();
    
    -- Check admin status
    SELECT COALESCE(is_admin, false) INTO admin_check
    FROM public.user_profiles 
    WHERE user_profiles.user_id = current_user_uuid;
    
    -- Require admin access
    IF NOT admin_check THEN
        RAISE EXCEPTION 'Access denied. Admin privileges required.';
    END IF;
    
    -- Return records
    FOR rec IN 
        SELECT 
            up.id,
            up.user_id,
            up.email,
            up.full_name,
            up.is_subscribed,
            up.is_admin,
            up.subscription_expires_at,
            up.subscription_status,
            up.subscription_plan,
            up.created_at,
            up.updated_at,
            au.last_sign_in_at
        FROM public.user_profiles up
        LEFT JOIN auth.users au ON up.user_id = au.id
        ORDER BY up.created_at DESC
    LOOP
        RETURN NEXT rec;
    END LOOP;
    
    RETURN;
END;
$function$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.get_all_user_profiles() TO authenticated;