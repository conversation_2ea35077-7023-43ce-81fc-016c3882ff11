import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import PaystackButton from '@/components/PaystackButton';
import SecurePaystackButton from '@/components/SecurePaystackButton';
import * as useAuthModule from '@/hooks/use-auth';
import * as paystackUtils from '@/utils/paystack';
import { usePaystackPayment } from 'react-paystack';

// Mock the external dependencies
vi.mock('@/hooks/use-auth');
vi.mock('react-paystack');
vi.mock('framer-motion', () => ({
  motion: {
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}));
vi.mock('sonner', () => ({
  toast: {
    error: vi.fn(),
    success: vi.fn(),
    info: vi.fn(),
  },
}));

// Mock fetch for API calls
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock window.location
Object.defineProperty(window, 'location', {
  value: {
    href: '',
    assign: vi.fn(),
  },
  writable: true,
});

// Mock environment variables
Object.defineProperty(import.meta, 'env', {
  value: {
    VITE_PAYSTACK_PUBLIC_KEY: 'pk_test_integration_123',
    VITE_API_URL: 'http://localhost:3001',
    DEV: false,
  },
  writable: true,
});

describe('Payment Flow Integration Tests', () => {
  const mockUser = {
    id: 'user_123',
    email: '<EMAIL>',
    name: 'Integration Test User'
  };

  const mockPlan = {
    id: 'basic',
    name: 'Basic Plan',
    amount: 998,
    interval: 'weekly' as const,
    features: ['Feature 1', 'Feature 2']
  };

  const mockInitializePayment = vi.fn();
  const mockPaystackResponse = {
    reference: 'test_ref_integration_123'
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default auth mock
    vi.mocked(useAuthModule.useAuth).mockReturnValue({
      user: mockUser as any,
      session: {} as any,
      isLoading: false,
      signIn: vi.fn(),
      signUp: vi.fn(),
      signOut: vi.fn(),
    });

    // Setup Paystack payment mock
    vi.mocked(usePaystackPayment).mockReturnValue(mockInitializePayment);

    // Reset window location
    window.location.href = '';
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('Complete Payment Verification Flow', () => {
    it('handles successful end-to-end payment flow', async () => {
      // Mock successful API response
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          message: 'Payment verified successfully',
          data: {
            subscription: {
              id: 'sub_123',
              userId: 'user_123',
              planId: 'basic',
              expiresAt: '2024-01-08T00:00:00Z'
            },
            processingTime: '2.1s'
          }
        })
      });

      const TestComponent = () => (
        <BrowserRouter>
          <PaystackButton plan={mockPlan} />
        </BrowserRouter>
      );

      render(<TestComponent />);

      // 1. User clicks payment button
      const paymentButton = screen.getByText('Select');
      fireEvent.click(paymentButton);

      // 2. Payment should initialize
      expect(mockInitializePayment).toHaveBeenCalledWith(
        expect.any(Function), // success callback
        expect.any(Function)  // close callback
      );

      // 3. Simulate successful Paystack payment
      const successCallback = mockInitializePayment.mock.calls[0][0];
      await act(async () => {
        await successCallback(mockPaystackResponse);
      });

      // 4. Verify API call was made
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3001/api/payments/verify',
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ reference: 'test_ref_integration_123' })
        })
      );

      // 5. Verify success state
      await waitFor(() => {
        expect(screen.getByText('Payment Successful!')).toBeInTheDocument();
      });
    });

    it('handles payment verification failure gracefully', async () => {
      // Mock failed API response
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: false,
          message: 'Payment verification failed',
          error: 'Invalid payment reference'
        })
      });

      const TestComponent = () => (
        <BrowserRouter>
          <PaystackButton plan={mockPlan} />
        </BrowserRouter>
      );

      render(<TestComponent />);

      // 1. Initiate payment
      const paymentButton = screen.getByText('Select');
      fireEvent.click(paymentButton);

      // 2. Simulate successful Paystack payment
      const successCallback = mockInitializePayment.mock.calls[0][0];
      await act(async () => {
        await successCallback(mockPaystackResponse);
      });

      // 3. Verify error handling
      await waitFor(() => {
        expect(screen.getByText(/Payment verification failed/)).toBeInTheDocument();
      });

      // 4. Should show retry option for retryable errors
      await waitFor(() => {
        expect(screen.getByText('Retry Payment')).toBeInTheDocument();
      });
    });

    it('handles network errors with retry mechanism', async () => {
      // First call fails with network error
      mockFetch
        .mockRejectedValueOnce(new Error('NetworkError: Failed to fetch'))
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            message: 'Payment verified on retry'
          })
        });

      const TestComponent = () => (
        <BrowserRouter>
          <PaystackButton plan={mockPlan} />
        </BrowserRouter>
      );

      render(<TestComponent />);

      // 1. Initiate payment
      const paymentButton = screen.getByText('Select');
      fireEvent.click(paymentButton);

      // 2. Simulate successful Paystack payment
      const successCallback = mockInitializePayment.mock.calls[0][0];
      await act(async () => {
        await successCallback(mockPaystackResponse);
      });

      // 3. Should eventually succeed after retry
      await waitFor(() => {
        expect(screen.getByText('Payment Successful!')).toBeInTheDocument();
      }, { timeout: 10000 });

      // 4. Verify retry was attempted
      expect(mockFetch).toHaveBeenCalledTimes(2);
    });

    it('handles server errors with appropriate user feedback', async () => {
      // Mock server error
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500
      });

      const TestComponent = () => (
        <BrowserRouter>
          <PaystackButton plan={mockPlan} />
        </BrowserRouter>
      );

      render(<TestComponent />);

      // 1. Initiate payment
      const paymentButton = screen.getByText('Select');
      fireEvent.click(paymentButton);

      // 2. Simulate successful Paystack payment
      const successCallback = mockInitializePayment.mock.calls[0][0];
      await act(async () => {
        await successCallback(mockPaystackResponse);
      });

      // 3. Should show appropriate error message
      await waitFor(() => {
        expect(screen.getByText(/Payment service temporarily unavailable/)).toBeInTheDocument();
      }, { timeout: 10000 });
    });
  });

  describe('Secure Payment Flow with Enhanced Features', () => {
    it('completes secure payment flow with verification', async () => {
      vi.useFakeTimers();

      // Mock successful verification
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          message: 'Secure payment verified',
          data: {
            subscription: {
              id: 'sub_secure_123',
              userId: 'user_123',
              planId: 'basic'
            }
          }
        })
      });

      const TestComponent = () => (
        <BrowserRouter>
          <SecurePaystackButton plan={mockPlan} />
        </BrowserRouter>
      );

      render(<TestComponent />);

      // 1. Initiate secure payment
      const paymentButton = screen.getByText('Select');
      fireEvent.click(paymentButton);

      // 2. Simulate successful Paystack payment
      const successCallback = mockInitializePayment.mock.calls[0][0];
      await act(async () => {
        await successCallback(mockPaystackResponse);
      });

      // 3. Should show verification state
      await waitFor(() => {
        expect(screen.getByText('Verifying Payment...')).toBeInTheDocument();
      });

      // 4. Should show success and redirect
      await waitFor(() => {
        expect(screen.getByText('Payment Successful!')).toBeInTheDocument();
      });

      // 5. Should redirect after delay
      act(() => {
        vi.advanceTimersByTime(1500);
      });

      expect(window.location.href).toBe('/payment/success?reference=test_ref_integration_123&plan=Basic Plan');
    });

    it('handles secure payment without verification when disabled', async () => {
      vi.useFakeTimers();

      const TestComponent = () => (
        <BrowserRouter>
          <SecurePaystackButton 
            plan={mockPlan} 
            enableVerification={false}
          />
        </BrowserRouter>
      );

      render(<TestComponent />);

      // 1. Initiate payment
      const paymentButton = screen.getByText('Select');
      fireEvent.click(paymentButton);

      // 2. Simulate successful Paystack payment
      const successCallback = mockInitializePayment.mock.calls[0][0];
      await act(async () => {
        await successCallback(mockPaystackResponse);
      });

      // 3. Should skip verification and go directly to success
      await waitFor(() => {
        expect(screen.getByText('Payment Successful!')).toBeInTheDocument();
      });

      // 4. Should not call verification API
      expect(mockFetch).not.toHaveBeenCalled();

      // 5. Should redirect
      act(() => {
        vi.advanceTimersByTime(1500);
      });

      expect(window.location.href).toBe('/payment/success?reference=test_ref_integration_123&plan=Basic Plan');
    });
  });

  describe('Error Recovery and Edge Cases', () => {
    it('recovers from temporary API failures', async () => {
      vi.useFakeTimers();

      // Simulate temporary failure followed by success
      mockFetch
        .mockResolvedValueOnce({
          ok: false,
          status: 503 // Service unavailable
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            message: 'Payment verified after recovery'
          })
        });

      const TestComponent = () => (
        <BrowserRouter>
          <PaystackButton plan={mockPlan} />
        </BrowserRouter>
      );

      render(<TestComponent />);

      // 1. Initiate payment
      const paymentButton = screen.getByText('Select');
      fireEvent.click(paymentButton);

      // 2. Simulate successful Paystack payment
      const successCallback = mockInitializePayment.mock.calls[0][0];
      
      const paymentPromise = act(async () => {
        await successCallback(mockPaystackResponse);
      });

      // 3. Fast-forward through retry delay
      act(() => {
        vi.advanceTimersByTime(1000);
      });

      await paymentPromise;

      // 4. Should eventually succeed
      await waitFor(() => {
        expect(screen.getByText('Payment Successful!')).toBeInTheDocument();
      });

      expect(mockFetch).toHaveBeenCalledTimes(2);
    });

    it('handles payment cancellation gracefully', async () => {
      const TestComponent = () => (
        <BrowserRouter>
          <PaystackButton plan={mockPlan} />
        </BrowserRouter>
      );

      render(<TestComponent />);

      // 1. Initiate payment
      const paymentButton = screen.getByText('Select');
      fireEvent.click(paymentButton);

      // 2. Simulate payment cancellation
      const closeCallback = mockInitializePayment.mock.calls[0][1];
      act(() => {
        closeCallback();
      });

      // 3. Should show cancellation message
      await waitFor(() => {
        expect(screen.getByText(/Payment was cancelled/)).toBeInTheDocument();
      });

      // 4. Should not make API calls
      expect(mockFetch).not.toHaveBeenCalled();
    });

    it('handles authentication loss during payment', async () => {
      const TestComponent = () => (
        <BrowserRouter>
          <PaystackButton plan={mockPlan} />
        </BrowserRouter>
      );

      render(<TestComponent />);

      // 1. Initiate payment
      const paymentButton = screen.getByText('Select');
      fireEvent.click(paymentButton);

      // 2. Simulate user logout during payment
      vi.mocked(useAuthModule.useAuth).mockReturnValue({
        user: null,
        session: null,
        isLoading: false,
        signIn: vi.fn(),
        signUp: vi.fn(),
        signOut: vi.fn(),
      });

      // 3. Simulate successful Paystack payment (but user is now logged out)
      const successCallback = mockInitializePayment.mock.calls[0][0];
      await act(async () => {
        await successCallback(mockPaystackResponse);
      });

      // 4. Should show authentication error
      await waitFor(() => {
        expect(screen.getByText(/User authentication lost/)).toBeInTheDocument();
      });
    });
  });

  describe('Multiple Payment Scenarios', () => {
    it('handles different subscription plans correctly', async () => {
      const proPlan = {
        id: 'pro',
        name: 'Pro Plan',
        amount: 1979,
        interval: 'weekly' as const,
        features: ['All features', 'Priority support']
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          message: 'Pro plan subscription successful',
          data: {
            subscription: {
              id: 'sub_pro_123',
              planId: 'pro',
              amount: 1979
            }
          }
        })
      });

      const TestComponent = () => (
        <BrowserRouter>
          <PaystackButton plan={proPlan} />
        </BrowserRouter>
      );

      render(<TestComponent />);

      // 1. Initiate payment for pro plan
      const paymentButton = screen.getByText('Select');
      fireEvent.click(paymentButton);

      // 2. Verify correct configuration
      expect(usePaystackPayment).toHaveBeenCalledWith(
        expect.objectContaining({
          amount: 197900, // 1979 * 100
          metadata: {
            custom_fields: [
              {
                display_name: 'Plan',
                variable_name: 'plan',
                value: 'pro'
              }
            ]
          }
        })
      );

      // 3. Complete payment flow
      const successCallback = mockInitializePayment.mock.calls[0][0];
      await act(async () => {
        await successCallback(mockPaystackResponse);
      });

      await waitFor(() => {
        expect(screen.getByText('Payment Successful!')).toBeInTheDocument();
      });
    });

    it('handles concurrent payment attempts correctly', async () => {
      const TestComponent = () => (
        <BrowserRouter>
          <div>
            <PaystackButton plan={mockPlan} />
            <SecurePaystackButton plan={mockPlan} />
          </div>
        </BrowserRouter>
      );

      render(<TestComponent />);

      // 1. Click both payment buttons rapidly
      const paymentButtons = screen.getAllByText('Select');
      fireEvent.click(paymentButtons[0]);
      fireEvent.click(paymentButtons[1]);

      // 2. Should initialize payments independently
      expect(mockInitializePayment).toHaveBeenCalledTimes(2);

      // 3. Both should be in processing state
      await waitFor(() => {
        const processingButtons = screen.getAllByText('Processing Payment...');
        expect(processingButtons).toHaveLength(2);
      });
    });
  });

  describe('Performance and Reliability', () => {
    it('handles slow API responses without timing out', async () => {
      vi.useFakeTimers();

      // Mock slow API response
      let resolvePayment: (value: any) => void;
      const slowPaymentPromise = new Promise(resolve => {
        resolvePayment = resolve;
      });

      mockFetch.mockReturnValueOnce(slowPaymentPromise);

      const TestComponent = () => (
        <BrowserRouter>
          <PaystackButton plan={mockPlan} />
        </BrowserRouter>
      );

      render(<TestComponent />);

      // 1. Initiate payment
      const paymentButton = screen.getByText('Select');
      fireEvent.click(paymentButton);

      // 2. Simulate successful Paystack payment
      const successCallback = mockInitializePayment.mock.calls[0][0];
      act(() => {
        successCallback(mockPaystackResponse);
      });

      // 3. Should show verifying state
      await waitFor(() => {
        expect(screen.getByText('Verifying Payment...')).toBeInTheDocument();
      });

      // 4. Fast-forward time but not to timeout
      act(() => {
        vi.advanceTimersByTime(25000); // 25 seconds
      });

      // 5. Should still be verifying
      expect(screen.getByText('Verifying Payment...')).toBeInTheDocument();

      // 6. Resolve the slow API call
      act(() => {
        resolvePayment!({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            message: 'Slow payment verified'
          })
        });
      });

      // 7. Should complete successfully
      await waitFor(() => {
        expect(screen.getByText('Payment Successful!')).toBeInTheDocument();
      });
    });

    it('handles API timeout appropriately', async () => {
      vi.useFakeTimers();

      // Mock hanging API call
      mockFetch.mockReturnValueOnce(new Promise(() => {})); // Never resolves

      const TestComponent = () => (
        <BrowserRouter>
          <PaystackButton plan={mockPlan} />
        </BrowserRouter>
      );

      render(<TestComponent />);

      // 1. Initiate payment
      const paymentButton = screen.getByText('Select');
      fireEvent.click(paymentButton);

      // 2. Simulate successful Paystack payment
      const successCallback = mockInitializePayment.mock.calls[0][0];
      act(() => {
        successCallback(mockPaystackResponse);
      });

      // 3. Fast-forward to timeout
      act(() => {
        vi.advanceTimersByTime(30000); // 30 seconds
      });

      // 4. Should show timeout error
      await waitFor(() => {
        expect(screen.getByText(/Payment verification timed out/)).toBeInTheDocument();
      });
    });
  });

  describe('User Experience Flow', () => {
    it('provides clear feedback throughout payment process', async () => {
      vi.useFakeTimers();

      // Mock successful but slow verification
      mockFetch.mockImplementationOnce(() => 
        new Promise(resolve => 
          setTimeout(() => resolve({
            ok: true,
            json: () => Promise.resolve({
              success: true,
              message: 'Payment verified successfully'
            })
          }), 3000)
        )
      );

      const TestComponent = () => (
        <BrowserRouter>
          <SecurePaystackButton plan={mockPlan} />
        </BrowserRouter>
      );

      render(<TestComponent />);

      // 1. Initial state
      expect(screen.getByText('Select')).toBeInTheDocument();

      // 2. Click to initiate
      const paymentButton = screen.getByText('Select');
      fireEvent.click(paymentButton);

      // 3. Should show initializing state
      await waitFor(() => {
        expect(screen.getByText('Initializing...')).toBeInTheDocument();
      });

      // 4. Should progress to processing
      await waitFor(() => {
        expect(screen.getByText('Processing Payment...')).toBeInTheDocument();
      });

      // 5. Simulate successful Paystack payment
      const successCallback = mockInitializePayment.mock.calls[0][0];
      await act(async () => {
        await successCallback(mockPaystackResponse);
      });

      // 6. Should show verification state with guidance
      await waitFor(() => {
        expect(screen.getByText('Verifying Payment...')).toBeInTheDocument();
        expect(screen.getByText(/Verifying your payment with our secure servers/)).toBeInTheDocument();
        expect(screen.getByText(/Please do not close this page/)).toBeInTheDocument();
      });

      // 7. Complete verification
      act(() => {
        vi.advanceTimersByTime(3000);
      });

      // 8. Should show success
      await waitFor(() => {
        expect(screen.getByText('Payment Successful!')).toBeInTheDocument();
        expect(screen.getByText(/Redirecting to confirmation page/)).toBeInTheDocument();
      });
    });
  });
});