module.exports = {
  extends: ['../eslint.config.js'],
  env: {
    node: true,
    es2020: true,
  },
  rules: {
    // Server-specific overrides
    '@typescript-eslint/no-explicit-any': 'warn', // More lenient for server error handling
    'security/detect-object-injection': 'off', // Necessary for dynamic config access
    '@typescript-eslint/no-unused-vars': ['error', { 
      argsIgnorePattern: '^_',
      varsIgnorePattern: '^_'
    }],
  },
};