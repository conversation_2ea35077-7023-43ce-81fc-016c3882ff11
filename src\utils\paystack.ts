// Paystack integration utility

// Get Paystack public key from environment variables
export const PAYSTACK_PUBLIC_KEY = import.meta.env.VITE_PAYSTACK_PUBLIC_KEY;

// Validate that the public key is available
if (!PAYSTACK_PUBLIC_KEY) {
  console.error('VITE_PAYSTACK_PUBLIC_KEY is not configured. Please check your environment variables.');
}

// Public key is used for client-side initialization
// No need to log it to console

export interface PaymentConfig {
  reference: string;
  email: string;
  amount: number; // in kobo (multiply Naira amount by 100)
  plan?: string;
  currency?: string;
  metadata?: {
    custom_fields: {
      display_name: string;
      variable_name: string;
      value: string;
    }[];
  };
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  amount: number; // in Naira
  interval: 'weekly' | 'monthly' | 'one-time';
  features: string[];
}

// Define subscription plans
export const subscriptionPlans: Record<string, SubscriptionPlan> = {
  basic: {
    id: 'basic',
    name: 'Basic',
    amount: 998, // ₦998
    interval: 'weekly',
    features: [
      'Access to 4 quiz domains',
      '400 questions weekly'
    ]
  },
  pro: {
    id: 'pro',
    name: 'Pro',
    amount: 1979, // ₦1,979
    interval: 'weekly',
    features: [
      'Access to all quiz domains',
      'Unlimited questions',
      'Cancel anytime'
    ]
  },
  elite: {
    id: 'elite',
    name: 'Elite',
    amount: 5000, // ₦5,000
    interval: 'one-time',
    features: [
      'Everything in Pro',
      'Community Access',
      '24/7 Priority Mentorship & Support',
      'CV Design and Job readiness Assist',
      'Daily cybersecurity related jobs',
      'Referrals for job openings'
    ]
  }
};

// Generate a unique reference for each transaction
export const generateReference = (): string => {
  const timestamp = new Date().getTime();
  const randomString = Math.random().toString(36).substring(2, 15);
  return `secquiz-${timestamp}-${randomString}`;
};

// Convert Naira to Kobo (Paystack requires amount in kobo)
export const toKobo = (amount: number): number => {
  return amount * 100;
};

// Enhanced payment verification result interface
export interface PaymentVerificationResult {
  success: boolean;
  message: string;
  reference?: string;
  plan?: SubscriptionPlan;
  error?: string;
  data?: {
    subscription?: any;
    processingTime?: string;
  };
}

// Handle successful payment with enhanced error handling and retry logic
export const handlePaymentSuccess = async (
  reference: string, 
  planId: string, 
  userEmail: string,
  retryCount = 0,
  maxRetries = 3
): Promise<PaymentVerificationResult> => {
  const apiUrl = import.meta.env.VITE_API_URL || '';
  const plan = subscriptionPlans[planId];

  if (!plan) {
    return {
      success: false,
      message: 'Invalid subscription plan',
      error: `Plan '${planId}' not found`
    };
  }

  try {
    // Add timeout to prevent hanging requests
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

    const response = await fetch(`${apiUrl}/api/payments/verify`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ reference }),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    // Check if response is ok
    if (!response.ok) {
      // Handle different HTTP status codes
      if (response.status === 404) {
        return {
          success: false,
          message: 'Payment reference not found',
          error: 'The payment reference could not be found in our system'
        };
      } else if (response.status === 401) {
        return {
          success: false,
          message: 'Authentication failed',
          error: 'Unable to authenticate with payment service'
        };
      } else if (response.status === 429) {
        return {
          success: false,
          message: 'Too many requests',
          error: 'Please wait a moment before trying again'
        };
      } else if (response.status >= 500) {
        // Server error - might be retryable
        if (retryCount < maxRetries) {
          console.warn(`Server error (${response.status}), retrying... (${retryCount + 1}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000)); // Exponential backoff
          return handlePaymentSuccess(reference, planId, userEmail, retryCount + 1, maxRetries);
        }
        
        return {
          success: false,
          message: 'Payment service temporarily unavailable',
          error: 'Please try again later or contact support'
        };
      }

      return {
        success: false,
        message: 'Payment verification failed',
        error: `Server returned status ${response.status}`
      };
    }

    let result;
    try {
      result = await response.json();
    } catch (parseError) {
      return {
        success: false,
        message: 'Invalid response from payment service',
        error: 'Unable to parse server response'
      };
    }

    if (result.success) {
      return {
        success: true,
        message: result.message || `Successfully subscribed to ${plan.name} plan`,
        reference,
        plan,
        data: result.data
      };
    } else {
      // Check if this is a retryable error
      const retryableErrors = [
        'VERIFICATION_TIMEOUT',
        'SUBSCRIPTION_SERVICE_ERROR',
        'INTERNAL_SERVER_ERROR'
      ];

      const isRetryable = result.error?.code && retryableErrors.includes(result.error.code);
      
      if (isRetryable && retryCount < maxRetries) {
        console.warn(`Retryable error, attempting retry... (${retryCount + 1}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000)); // Exponential backoff
        return handlePaymentSuccess(reference, planId, userEmail, retryCount + 1, maxRetries);
      }

      return {
        success: false,
        message: result.message || 'Payment verification failed',
        error: result.error?.message || result.error || 'Unknown verification error'
      };
    }
  } catch (error) {
    // Handle network errors and timeouts
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        return {
          success: false,
          message: 'Payment verification timed out',
          error: 'The verification request took too long to complete'
        };
      }

      // Network errors might be retryable
      const networkErrors = ['NetworkError', 'TypeError'];
      const isNetworkError = networkErrors.some(errorType => 
        error.message.includes(errorType.toLowerCase()) || 
        error.name === errorType
      );

      if (isNetworkError && retryCount < maxRetries) {
        console.warn(`Network error, retrying... (${retryCount + 1}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000)); // Exponential backoff
        return handlePaymentSuccess(reference, planId, userEmail, retryCount + 1, maxRetries);
      }

      return {
        success: false,
        message: 'Network error during payment verification',
        error: error.message
      };
    }

    // For development/demo purposes, still redirect to success page
    // In production, you would handle this error differently
    if (import.meta.env.DEV) {
      console.warn('Development mode: Redirecting to success page despite error');
      setTimeout(() => {
        window.location.href = `/payment/success?reference=${reference}&plan=${plan.name}`;
      }, 2000);
    }

    return {
      success: false,
      message: 'Unexpected error during payment verification',
      error: 'An unexpected error occurred'
    };
  }
};

// Handle payment closure
export const handlePaymentClose = () => {
  // Payment window was closed by user
  return {
    success: false,
    message: 'Payment was cancelled'
  };
};
