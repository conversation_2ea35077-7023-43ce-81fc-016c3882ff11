import express, { Request, Response } from 'express';
import dotenv from 'dotenv';
import { configManager, serverConfig } from './lib/config.js';
import { environmentValidator } from './lib/env-validator.js';
import { secureLogger } from './lib/secure-logger.js';
import { corsWithLogging } from './middleware/cors-config.js';

// Load environment variables first
dotenv.config();

// Validate environment variables before starting the server
const validationResult = environmentValidator.validateEnvironment();
environmentValidator.printValidationResults(validationResult);

if (!validationResult.isValid) {
  secureLogger.error('Server startup aborted due to environment validation errors');
  process.exit(1);
}

import paymentsRouter from './routes/payments.js';
import webhooksRouter from './routes/webhooks.js';
import subscriptionsRouter from './routes/subscriptions.js';
import paystackProxyRouter from './routes/paystack-proxy.js';
import healthRouter from './routes/health.js';
import cspMiddleware from './middleware/csp.js';
import errorHandler, { notFoundHandler } from './middleware/error-handler.js';
import { generalRateLimiter, paymentRateLimiter, webhookRateLimiter } from './middleware/rate-limiter.js';
import { comprehensiveRequestValidator } from './middleware/request-validator.js';
import { comprehensiveSecurityLogger, securityErrorLogger } from './middleware/security-logger.js';
import { monitoringScheduler } from './lib/monitoring-scheduler.js';

const app = express();
const PORT = serverConfig.port;

// Security Middleware (order matters)
app.use(corsWithLogging());
app.use(express.json({ limit: '10mb' })); // Add size limit for security
app.use(cspMiddleware); // Add Content Security Policy
app.use(comprehensiveRequestValidator); // Validate all requests
app.use(comprehensiveSecurityLogger); // Log security events
app.use(generalRateLimiter); // General rate limiting

// Routes with specific rate limiting
app.use('/api/payments', paymentRateLimiter, paymentsRouter);
app.use('/api/webhooks', webhookRateLimiter, webhooksRouter);
app.use('/api/subscriptions', paymentRateLimiter, subscriptionsRouter);
app.use('/api/paystack-proxy', paymentRateLimiter, paystackProxyRouter);

// Comprehensive health check and debugging endpoints
app.use('/api', healthRouter);

// Legacy debug endpoint for backward compatibility
app.get('/api/debug', (_req: Request, res: Response) => {
  const paystackConfig = configManager.getPaystackConfig();
  const supabaseConfig = configManager.getSupabaseConfig();
  
  res.status(200).json({
    status: 'ok',
    environment: serverConfig.nodeEnv,
    hasPaystackPublicKey: !!paystackConfig.publicKey,
    hasPaystackSecretKey: !!paystackConfig.secretKey,
    hasPaystackWebhookSecret: !!paystackConfig.webhookSecret,
    hasSupabaseUrl: !!supabaseConfig.url,
    hasSupabaseServiceKey: !!supabaseConfig.serviceRoleKey,
    hasSupabaseAnonKey: !!supabaseConfig.anonKey,
    apiUrl: serverConfig.api.url,
    appUrl: serverConfig.api.appUrl,
    port: serverConfig.port,
    note: 'This endpoint is deprecated. Use /api/health for comprehensive health checks.'
  });
});

// 404 handler - must be before error handler
app.use(notFoundHandler);

// Security error logging - must be before general error handler
app.use(securityErrorLogger);

// Error handler - must be last
app.use(errorHandler);

// Start server
app.listen(PORT, () => {
  secureLogger.info(`Server running on port ${PORT}`);
  secureLogger.info(`Environment: ${serverConfig.nodeEnv}`);
  secureLogger.info(`API URL: ${serverConfig.api.url}`);
  secureLogger.info(`App URL: ${serverConfig.api.appUrl}`);
  secureLogger.info(`Paystack configured: ${!!configManager.getPaystackConfig().secretKey}`);
  secureLogger.info(`Supabase configured: ${!!configManager.getSupabaseConfig().url}`);
  
  // Start monitoring scheduler in production and staging
  if (serverConfig.nodeEnv !== 'test') {
    secureLogger.info('Starting monitoring scheduler...');
    monitoringScheduler.start();
  }
});

// Graceful shutdown handling
process.on('SIGTERM', () => {
  secureLogger.info('SIGTERM received, shutting down gracefully');
  monitoringScheduler.stop();
  process.exit(0);
});

process.on('SIGINT', () => {
  secureLogger.info('SIGINT received, shutting down gracefully');
  monitoringScheduler.stop();
  process.exit(0);
});
