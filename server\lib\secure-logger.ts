/**
 * Secure logging utility that sanitizes sensitive data before logging
 */

export interface LogLevel {
  ERROR: 'error';
  WARN: 'warn';
  INFO: 'info';
  DEBUG: 'debug';
}

export const LOG_LEVELS: LogLevel = {
  ERROR: 'error',
  WARN: 'warn',
  INFO: 'info',
  DEBUG: 'debug'
};

// Sensitive data patterns to sanitize
const SENSITIVE_PATTERNS = [
  /sk_[a-zA-Z0-9_]+/g, // Paystack secret keys
  /pk_[a-zA-Z0-9_]+/g, // Paystack public keys (in logs)
  /eyJ[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+/g, // JWT tokens
  /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g, // Email addresses (partial)
  /password['":\s]*['"]\w+['"]/gi, // Password fields
  /secret['":\s]*['"]\w+['"]/gi, // Secret fields
  /token['":\s]*['"]\w+['"]/gi, // Token fields
];

/**
 * Sanitizes sensitive data from log messages
 */
function sanitizeLogData(data: any): any {
  if (typeof data === 'string') {
    let sanitized = data;
    SENSITIVE_PATTERNS.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '[REDACTED]');
    });
    return sanitized;
  }

  if (typeof data === 'object' && data !== null) {
    if (Array.isArray(data)) {
      return data.map(item => sanitizeLogData(item));
    }

    const sanitized: any = {};
    for (const [key, value] of Object.entries(data)) {
      // Redact sensitive keys entirely
      if (/secret|password|token|key/i.test(key)) {
        sanitized[key] = '[REDACTED]';
      } else {
        sanitized[key] = sanitizeLogData(value);
      }
    }
    return sanitized;
  }

  return data;
}

/**
 * Secure logger class that sanitizes sensitive data
 */
class SecureLogger {
  private isDevelopment: boolean;

  constructor() {
    this.isDevelopment = process.env.NODE_ENV === 'development';
  }

  private formatMessage(level: string, message: string, data?: any): string {
    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] [${level.toUpperCase()}]`;
    
    if (data) {
      const sanitizedData = sanitizeLogData(data);
      return `${prefix} ${message} ${JSON.stringify(sanitizedData, null, 2)}`;
    }
    
    return `${prefix} ${message}`;
  }

  error(message: string, data?: any): void {
    const sanitizedMessage = sanitizeLogData(message);
    console.error(this.formatMessage('error', sanitizedMessage, data));
  }

  warn(message: string, data?: any): void {
    const sanitizedMessage = sanitizeLogData(message);
    console.warn(this.formatMessage('warn', sanitizedMessage, data));
  }

  info(message: string, data?: any): void {
    const sanitizedMessage = sanitizeLogData(message);
    console.info(this.formatMessage('info', sanitizedMessage, data));
  }

  debug(message: string, data?: any): void {
    if (this.isDevelopment) {
      const sanitizedMessage = sanitizeLogData(message);
      console.debug(this.formatMessage('debug', sanitizedMessage, data));
    }
  }

  /**
   * Log security events with additional context
   */
  security(event: string, details?: any): void {
    const securityLog = {
      event,
      timestamp: new Date().toISOString(),
      details: sanitizeLogData(details)
    };
    
    console.warn(this.formatMessage('security', `Security Event: ${event}`, securityLog));
  }

  /**
   * Log payment events with sanitized data
   */
  payment(event: string, reference?: string, details?: any): void {
    const paymentLog = {
      event,
      reference,
      timestamp: new Date().toISOString(),
      details: sanitizeLogData(details)
    };
    
    console.info(this.formatMessage('payment', `Payment Event: ${event}`, paymentLog));
  }
}

// Export singleton instance
export const secureLogger = new SecureLogger();

// Export individual log functions for convenience
export const { error, warn, info, debug, security, payment } = secureLogger;