-- Data Migration Script for Production
-- Run this script to migrate existing data and ensure consistency

-- Backup existing data before migration
-- CREATE TABLE subscriptions_backup AS SELECT * FROM subscriptions;
-- CREATE TABLE payment_transactions_backup AS SELECT * FROM payment_transactions;
-- CREATE TABLE user_profiles_backup AS SELECT * FROM user_profiles;

BEGIN;

-- Step 1: Clean up any inconsistent data
DO $$
DECLARE
    v_cleanup_count INTEGER;
BEGIN
    -- Remove duplicate subscriptions (keep the latest one)
    WITH duplicate_subscriptions AS (
        SELECT id, 
               ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn
        FROM subscriptions
    )
    DELETE FROM subscriptions 
    WHERE id IN (
        SELECT id FROM duplicate_subscriptions WHERE rn > 1
    );
    
    GET DIAGNOSTICS v_cleanup_count = ROW_COUNT;
    RAISE NOTICE 'Removed % duplicate subscriptions', v_cleanup_count;
END $$;

-- Step 2: Update user_profiles to match subscription status
DO $$
DECLARE
    v_updated_profiles INTEGER;
BEGIN
    -- Update user profiles based on active subscriptions
    UPDATE user_profiles up
    SET 
        is_subscribed = CASE 
            WHEN s.is_active AND s.end_date > NOW() THEN true 
            ELSE false 
        END,
        subscription_expires_at = CASE 
            WHEN s.is_active AND s.end_date > NOW() THEN s.end_date 
            ELSE NULL 
        END,
        updated_at = NOW()
    FROM subscriptions s
    WHERE up.user_id = s.user_id
    AND (
        up.is_subscribed != (s.is_active AND s.end_date > NOW()) OR
        up.subscription_expires_at != s.end_date OR
        up.subscription_expires_at IS NULL
    );
    
    GET DIAGNOSTICS v_updated_profiles = ROW_COUNT;
    RAISE NOTICE 'Updated % user profiles for subscription consistency', v_updated_profiles;
END $$;

-- Step 3: Mark expired subscriptions as inactive
DO $$
DECLARE
    v_expired_count INTEGER;
BEGIN
    UPDATE subscriptions 
    SET is_active = false, updated_at = NOW()
    WHERE end_date < NOW() AND is_active = true;
    
    GET DIAGNOSTICS v_expired_count = ROW_COUNT;
    RAISE NOTICE 'Marked % expired subscriptions as inactive', v_expired_count;
END $$;

-- Step 4: Update payment transaction statuses based on subscription data
DO $$
DECLARE
    v_updated_transactions INTEGER;
BEGIN
    -- Mark transactions as successful if they have corresponding active subscriptions
    UPDATE payment_transactions pt
    SET status = 'success', updated_at = NOW()
    FROM subscriptions s
    WHERE pt.reference = s.last_payment_reference
    AND pt.user_id = s.user_id
    AND s.is_active = true
    AND pt.status = 'pending';
    
    GET DIAGNOSTICS v_updated_transactions = ROW_COUNT;
    RAISE NOTICE 'Updated % payment transaction statuses', v_updated_transactions;
END $$;

-- Step 5: Create missing payment transactions for existing subscriptions
DO $$
DECLARE
    v_created_transactions INTEGER;
BEGIN
    INSERT INTO payment_transactions (user_id, reference, amount, plan_id, status, payment_method, created_at)
    SELECT 
        s.user_id,
        COALESCE(s.last_payment_reference, 'migration_' || s.id::text),
        s.amount_paid,
        s.plan_id,
        'success',
        'paystack',
        s.created_at
    FROM subscriptions s
    LEFT JOIN payment_transactions pt ON pt.reference = s.last_payment_reference
    WHERE pt.id IS NULL
    AND s.last_payment_reference IS NOT NULL;
    
    GET DIAGNOSTICS v_created_transactions = ROW_COUNT;
    RAISE NOTICE 'Created % missing payment transaction records', v_created_transactions;
END $$;

-- Step 6: Validate data consistency
DO $$
DECLARE
    v_inconsistent_count INTEGER;
    v_orphaned_transactions INTEGER;
    v_missing_profiles INTEGER;
BEGIN
    -- Check for inconsistencies between subscriptions and user_profiles
    SELECT COUNT(*) INTO v_inconsistent_count
    FROM subscriptions s
    JOIN user_profiles up ON s.user_id = up.user_id
    WHERE (s.is_active AND s.end_date > NOW()) != up.is_subscribed;
    
    IF v_inconsistent_count > 0 THEN
        RAISE WARNING 'Found % inconsistencies between subscriptions and user_profiles', v_inconsistent_count;
    END IF;
    
    -- Check for orphaned payment transactions
    SELECT COUNT(*) INTO v_orphaned_transactions
    FROM payment_transactions pt
    LEFT JOIN subscriptions s ON pt.reference = s.last_payment_reference
    WHERE s.id IS NULL AND pt.status = 'success';
    
    IF v_orphaned_transactions > 0 THEN
        RAISE WARNING 'Found % orphaned successful payment transactions', v_orphaned_transactions;
    END IF;
    
    -- Check for users with subscriptions but no user_profiles
    SELECT COUNT(*) INTO v_missing_profiles
    FROM subscriptions s
    LEFT JOIN user_profiles up ON s.user_id = up.user_id
    WHERE up.user_id IS NULL;
    
    IF v_missing_profiles > 0 THEN
        RAISE WARNING 'Found % subscriptions without corresponding user_profiles', v_missing_profiles;
    END IF;
END $$;

-- Step 7: Create summary report
DO $$
DECLARE
    v_total_subscriptions INTEGER;
    v_active_subscriptions INTEGER;
    v_expired_subscriptions INTEGER;
    v_total_transactions INTEGER;
    v_successful_transactions INTEGER;
    v_subscribed_users INTEGER;
BEGIN
    SELECT COUNT(*) INTO v_total_subscriptions FROM subscriptions;
    SELECT COUNT(*) INTO v_active_subscriptions FROM subscriptions WHERE is_active = true AND end_date > NOW();
    SELECT COUNT(*) INTO v_expired_subscriptions FROM subscriptions WHERE end_date <= NOW();
    SELECT COUNT(*) INTO v_total_transactions FROM payment_transactions;
    SELECT COUNT(*) INTO v_successful_transactions FROM payment_transactions WHERE status = 'success';
    SELECT COUNT(*) INTO v_subscribed_users FROM user_profiles WHERE is_subscribed = true;
    
    RAISE NOTICE '=== MIGRATION SUMMARY ===';
    RAISE NOTICE 'Total subscriptions: %', v_total_subscriptions;
    RAISE NOTICE 'Active subscriptions: %', v_active_subscriptions;
    RAISE NOTICE 'Expired subscriptions: %', v_expired_subscriptions;
    RAISE NOTICE 'Total payment transactions: %', v_total_transactions;
    RAISE NOTICE 'Successful transactions: %', v_successful_transactions;
    RAISE NOTICE 'Subscribed users: %', v_subscribed_users;
    RAISE NOTICE '========================';
END $$;

COMMIT;

-- Post-migration verification queries
-- Run these manually to verify the migration was successful

/*
-- Verify subscription consistency
SELECT 
    'Subscription Consistency' as check_type,
    COUNT(*) as total_records,
    SUM(CASE WHEN s.is_active AND s.end_date > NOW() AND up.is_subscribed THEN 1 ELSE 0 END) as consistent_active,
    SUM(CASE WHEN (NOT s.is_active OR s.end_date <= NOW()) AND NOT up.is_subscribed THEN 1 ELSE 0 END) as consistent_inactive,
    SUM(CASE WHEN (s.is_active AND s.end_date > NOW()) != up.is_subscribed THEN 1 ELSE 0 END) as inconsistent
FROM subscriptions s
JOIN user_profiles up ON s.user_id = up.user_id;

-- Verify payment transaction consistency
SELECT 
    'Payment Transaction Consistency' as check_type,
    COUNT(*) as total_transactions,
    SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as successful,
    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed
FROM payment_transactions;

-- Check for orphaned records
SELECT 
    'Orphaned Records Check' as check_type,
    (SELECT COUNT(*) FROM subscriptions s LEFT JOIN user_profiles up ON s.user_id = up.user_id WHERE up.user_id IS NULL) as subscriptions_without_profiles,
    (SELECT COUNT(*) FROM payment_transactions pt LEFT JOIN subscriptions s ON pt.reference = s.last_payment_reference WHERE s.id IS NULL AND pt.status = 'success') as orphaned_successful_transactions;

-- Active subscription summary by plan
SELECT 
    plan_id,
    COUNT(*) as active_subscriptions,
    SUM(amount_paid) as total_revenue,
    MIN(end_date) as earliest_expiry,
    MAX(end_date) as latest_expiry
FROM subscriptions 
WHERE is_active = true AND end_date > NOW()
GROUP BY plan_id
ORDER BY active_subscriptions DESC;
*/