/**
 * CORS configuration for different environments
 */

import { CorsOptions } from 'cors';
import { serverConfig } from '../lib/config.js';
import { secureLogger } from '../lib/secure-logger.js';

/**
 * Get CORS configuration based on environment
 */
export function getCorsConfig(): CorsOptions {
  const isDevelopment = serverConfig.nodeEnv === 'development';
  const isProduction = serverConfig.nodeEnv === 'production';

  // Development CORS - more permissive for local development
  if (isDevelopment) {
    return {
      origin: [
        'http://localhost:3000',
        'http://localhost:5173',
        'http://127.0.0.1:3000',
        'http://127.0.0.1:5173',
        serverConfig.api.appUrl
      ].filter(Boolean),
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-Requested-With',
        'Accept',
        'Origin'
      ],
      exposedHeaders: ['X-Total-Count'],
      maxAge: 86400 // 24 hours
    };
  }

  // Production CORS - strict configuration
  if (isProduction) {
    const allowedOrigins = [
      serverConfig.api.appUrl,
      'https://secquiz.vercel.app',
      'https://www.secquiz.com'
    ].filter(Boolean);

    return {
      origin: (origin, callback) => {
        // Allow requests with no origin (mobile apps, etc.)
        if (!origin) {
          return callback(null, true);
        }

        if (allowedOrigins.includes(origin)) {
          callback(null, true);
        } else {
          secureLogger.security('CORS violation attempt', {
            origin,
            allowedOrigins,
            userAgent: 'N/A' // Will be added by middleware
          });
          callback(new Error('Not allowed by CORS'));
        }
      },
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE'],
      allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-Requested-With'
      ],
      exposedHeaders: ['X-Total-Count'],
      maxAge: 3600, // 1 hour
      optionsSuccessStatus: 200
    };
  }

  // Default/staging configuration
  return {
    origin: [
      serverConfig.api.appUrl,
      'http://localhost:3000',
      'http://localhost:5173'
    ].filter(Boolean),
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'X-Requested-With',
      'Accept',
      'Origin'
    ],
    maxAge: 3600
  };
}

/**
 * Enhanced CORS middleware with logging
 */
export function corsWithLogging() {
  const corsConfig = getCorsConfig();
  
  return (req: any, res: any, next: any) => {
    // Log CORS requests in production for monitoring
    if (serverConfig.nodeEnv === 'production' && req.headers.origin) {
      secureLogger.debug('CORS request', {
        origin: req.headers.origin,
        method: req.method,
        path: req.path,
        userAgent: req.headers['user-agent']
      });
    }

    // Apply CORS configuration
    const cors = require('cors')(corsConfig);
    cors(req, res, next);
  };
}