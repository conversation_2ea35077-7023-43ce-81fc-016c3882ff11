/**
 * Tests for payment logging and monitoring system
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { paymentEventLogger, PaymentEventType, PaymentEventSeverity } from '../lib/payment-event-logger.js';
import { paymentAlertingSystem } from '../lib/payment-alerting.js';
import { monitoringScheduler } from '../lib/monitoring-scheduler.js';

describe('Payment Logging System', () => {
  beforeEach(() => {
    // Setup test environment
  });

  afterEach(() => {
    // Cleanup
  });

  describe('Payment Event Logger', () => {
    it('should log payment events without throwing errors', async () => {
      expect(async () => {
        await paymentEventLogger.logEvent({
          event_type: PaymentEventType.PAYMENT_VERIFICATION_STARTED,
          severity: PaymentEventSeverity.INFO,
          reference: 'test_ref_123',
          metadata: { test: true }
        });
      }).not.toThrow();
    });

    it('should log payment success events', async () => {
      expect(async () => {
        await paymentEventLogger.logPaymentSuccess({
          reference: 'test_success_ref',
          userId: 'test_user_id',
          planId: 'pro',
          amount: 5000,
          currency: 'NGN',
          processingTime: 1500,
          metadata: { test: true }
        });
      }).not.toThrow();
    });

    it('should log payment failure events', async () => {
      expect(async () => {
        await paymentEventLogger.logPaymentFailure({
          reference: 'test_failure_ref',
          userId: 'test_user_id',
          planId: 'pro',
          amount: 5000,
          errorCode: 'TEST_ERROR',
          errorMessage: 'Test error message',
          processingTime: 2000,
          metadata: { test: true }
        });
      }).not.toThrow();
    });

    it('should get payment metrics without errors', async () => {
      const metrics = await paymentEventLogger.getPaymentMetrics(1);
      
      expect(metrics).toBeDefined();
      expect(metrics).toHaveProperty('total_payments');
      expect(metrics).toHaveProperty('successful_payments');
      expect(metrics).toHaveProperty('failed_payments');
      expect(metrics).toHaveProperty('success_rate');
      expect(metrics).toHaveProperty('average_processing_time');
      expect(metrics).toHaveProperty('total_revenue');
      expect(metrics).toHaveProperty('webhook_events');
      expect(metrics).toHaveProperty('webhook_failures');
      expect(metrics).toHaveProperty('period_start');
      expect(metrics).toHaveProperty('period_end');
    });
  });

  describe('Payment Alerting System', () => {
    it('should check for critical issues without throwing errors', async () => {
      const result = await paymentAlertingSystem.checkAndTriggerAlerts();
      
      // checkAndTriggerAlerts doesn't return a value, just ensure it doesn't throw
      expect(true).toBe(true);
    });

    it('should get active alerts without errors', async () => {
      const alerts = await paymentAlertingSystem.getActiveAlerts(10);
      
      expect(Array.isArray(alerts)).toBe(true);
    });

    it('should run health checks without throwing errors', async () => {
      expect(async () => {
        await paymentAlertingSystem.checkAndTriggerAlerts();
      }).not.toThrow();
    });
  });

  describe('Monitoring Scheduler', () => {
    it('should provide status information', () => {
      const status = monitoringScheduler.getStatus();
      
      expect(status).toBeDefined();
      expect(status).toHaveProperty('isRunning');
      expect(status).toHaveProperty('activeTasks');
      expect(status).toHaveProperty('uptime');
      expect(Array.isArray(status.activeTasks)).toBe(true);
    });

    it('should be able to run individual tasks', async () => {
      expect(async () => {
        await monitoringScheduler.runTask('metrics_collection');
      }).not.toThrow();
    });

    it('should throw error for invalid task names', async () => {
      await expect(async () => {
        await monitoringScheduler.runTask('invalid_task');
      }).rejects.toThrow('Unknown task: invalid_task');
    });
  });

  describe('Event Sanitization', () => {
    it('should sanitize sensitive data in payment events', async () => {
      // This test would need to check that sensitive data is properly sanitized
      // For now, we just ensure the logging doesn't throw errors
      expect(async () => {
        await paymentEventLogger.logEvent({
          event_type: PaymentEventType.PAYMENT_VERIFICATION_SUCCESS,
          severity: PaymentEventSeverity.INFO,
          reference: 'test_ref_123',
          metadata: {
            secret_key: 'sk_test_secret_key',
            email: '<EMAIL>',
            password: 'secret_password',
            normal_field: 'normal_value'
          }
        });
      }).not.toThrow();
    });
  });
});

describe('Health Check Endpoints', () => {
  // These would be integration tests that would require a running server
  // For now, we just test that the functions exist and can be imported
  
  it('should have health check functions available', () => {
    expect(paymentEventLogger).toBeDefined();
    expect(paymentAlertingSystem).toBeDefined();
    expect(monitoringScheduler).toBeDefined();
  });
});