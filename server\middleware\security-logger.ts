/**
 * Security event logging middleware
 */

import { Request, Response, NextFunction } from 'express';
import { secureLogger } from '../lib/secure-logger.js';

interface SecurityEvent {
  type: 'request' | 'response' | 'error' | 'auth' | 'payment';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  metadata?: any;
}

/**
 * Logs security-relevant requests
 */
export function securityRequestLogger(req: Request, res: Response, next: NextFunction) {
  const startTime = Date.now();
  
  // Log sensitive endpoint access
  const sensitiveEndpoints = [
    '/api/payments',
    '/api/webhooks',
    '/api/subscriptions',
    '/api/debug',
    '/api/config'
  ];

  const isSensitive = sensitiveEndpoints.some(endpoint => req.path.startsWith(endpoint));
  
  if (isSensitive) {
    secureLogger.security('Sensitive endpoint access', {
      ip: req.ip,
      method: req.method,
      path: req.path,
      userAgent: req.headers['user-agent'],
      referer: req.headers.referer,
      timestamp: new Date().toISOString()
    });
  }

  // Override res.json to log responses
  const originalJson = res.json;
  res.json = function(body: any) {
    const duration = Date.now() - startTime;
    
    // Log failed responses
    if (res.statusCode >= 400) {
      const severity = res.statusCode >= 500 ? 'high' : 'medium';
      
      secureLogger.security('Failed request', {
        ip: req.ip,
        method: req.method,
        path: req.path,
        statusCode: res.statusCode,
        duration,
        userAgent: req.headers['user-agent'],
        severity
      });
    }

    // Log successful payment operations
    if (isSensitive && res.statusCode < 400) {
      secureLogger.security('Successful sensitive operation', {
        ip: req.ip,
        method: req.method,
        path: req.path,
        statusCode: res.statusCode,
        duration,
        severity: 'low'
      });
    }

    return originalJson.call(this, body);
  };

  next();
}

/**
 * Logs authentication events
 */
export function authEventLogger(event: 'login' | 'logout' | 'failed_login' | 'token_refresh', metadata?: any) {
  return (req: Request, res: Response, next: NextFunction) => {
    const severity = event === 'failed_login' ? 'medium' : 'low';
    
    secureLogger.security(`Authentication event: ${event}`, {
      ip: req.ip,
      userAgent: req.headers['user-agent'],
      timestamp: new Date().toISOString(),
      severity,
      ...metadata
    });

    next();
  };
}

/**
 * Logs payment events
 */
export function paymentEventLogger(event: string, metadata?: any) {
  return (req: Request, res: Response, next: NextFunction) => {
    secureLogger.payment(event, metadata?.reference, {
      ip: req.ip,
      userAgent: req.headers['user-agent'],
      timestamp: new Date().toISOString(),
      ...metadata
    });

    next();
  };
}

/**
 * Logs webhook events
 */
export function webhookEventLogger(req: Request, res: Response, next: NextFunction) {
  const webhookEvent = req.body?.event;
  const reference = req.body?.data?.reference;
  
  secureLogger.security('Webhook received', {
    ip: req.ip,
    event: webhookEvent,
    reference,
    userAgent: req.headers['user-agent'],
    timestamp: new Date().toISOString(),
    severity: 'low'
  });

  next();
}

/**
 * Logs suspicious activity
 */
export function suspiciousActivityLogger(activityType: string, details: any) {
  return (req: Request, res: Response, next: NextFunction) => {
    secureLogger.security(`Suspicious activity: ${activityType}`, {
      ip: req.ip,
      path: req.path,
      method: req.method,
      userAgent: req.headers['user-agent'],
      timestamp: new Date().toISOString(),
      severity: 'high',
      details
    });

    next();
  };
}

/**
 * Comprehensive security logging middleware
 */
export function comprehensiveSecurityLogger(req: Request, res: Response, next: NextFunction) {
  // Log all requests to payment-related endpoints
  if (req.path.startsWith('/api/')) {
    securityRequestLogger(req, res, next);
  } else {
    next();
  }
}

/**
 * Error logging middleware for security events
 */
export function securityErrorLogger(err: any, req: Request, res: Response, next: NextFunction) {
  // Log security-related errors
  const isSecurityError = err.status === 401 || err.status === 403 || err.status === 429;
  
  if (isSecurityError) {
    secureLogger.security('Security error', {
      ip: req.ip,
      path: req.path,
      method: req.method,
      error: err.message,
      statusCode: err.status,
      userAgent: req.headers['user-agent'],
      timestamp: new Date().toISOString(),
      severity: 'medium'
    });
  }

  next(err);
}