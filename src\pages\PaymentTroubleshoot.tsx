import { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlertCircle, CheckCircle, ChevronDown, ChevronUp, ExternalLink, Clock, RefreshCw } from "lucide-react";
import Navbar from "@/components/Navbar";
import BottomNavigation from "@/components/BottomNavigation";
import { Link } from "react-router-dom";

interface FAQItemProps {
  question: string;
  answer: React.ReactNode;
}

const FAQItem = ({ question, answer }: FAQItemProps) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="border-b border-gray-200 dark:border-gray-700 py-4">
      <button
        className="flex justify-between items-center w-full text-left font-medium text-gray-900 dark:text-white"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span>{question}</span>
        {isOpen ? (
          <ChevronUp className="h-5 w-5 text-gray-500 dark:text-gray-400" />
        ) : (
          <ChevronDown className="h-5 w-5 text-gray-500 dark:text-gray-400" />
        )}
      </button>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
          className="mt-2 text-gray-600 dark:text-gray-300"
        >
          {answer}
        </motion.div>
      )}
    </div>
  );
};

const PaymentTroubleshoot = () => {
  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />
      <div className="flex-1 container py-8 px-4">
        <div className="max-w-3xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl">Payment Troubleshooting</CardTitle>
                <CardDescription>
                  Having trouble with your payment? Here are some common issues and solutions.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 mb-6">
                  <div className="flex items-start gap-3 p-3 bg-amber-50 dark:bg-amber-950/50 rounded-md border border-amber-200 dark:border-amber-800">
                    <AlertCircle className="h-5 w-5 text-amber-600 dark:text-amber-500 flex-shrink-0 mt-0.5" />
                    <div>
                      <p className="text-sm text-amber-800 dark:text-amber-300">
                        If you're experiencing issues with payment, please try the solutions below. If your problem persists, contact us at <a href="mailto:<EMAIL>" className="underline font-medium"><EMAIL></a>.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Common Issues</h3>
                  
                  <FAQItem
                    question="Payment initialization failed"
                    answer={
                      <div className="space-y-2">
                        <p>This error occurs when the payment system cannot start properly. Common causes include:</p>
                        <ul className="list-disc pl-5 space-y-1">
                          <li><strong>Browser blocking popups:</strong> Enable popups for this site in your browser settings</li>
                          <li><strong>JavaScript disabled:</strong> Ensure JavaScript is enabled in your browser</li>
                          <li><strong>Ad blockers:</strong> Temporarily disable ad blockers or add this site to your whitelist</li>
                          <li><strong>Network connectivity:</strong> Check your internet connection and try again</li>
                          <li><strong>Browser compatibility:</strong> Use Chrome, Firefox, Safari, or Edge (latest versions)</li>
                          <li><strong>Configuration error:</strong> Our payment service may be temporarily unavailable</li>
                        </ul>
                        <div className="mt-3 p-3 bg-green-50 dark:bg-green-950/50 rounded-md border border-green-200 dark:border-green-800">
                          <div className="flex items-start gap-2">
                            <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-500 flex-shrink-0 mt-0.5" />
                            <div className="text-sm text-green-800 dark:text-green-300">
                              <p className="font-medium mb-1">Quick Fix:</p>
                              <ol className="list-decimal pl-4 space-y-1">
                                <li>Refresh the page (Ctrl+F5 or Cmd+Shift+R)</li>
                                <li>Clear browser cache and cookies</li>
                                <li>Try in incognito/private mode</li>
                                <li>Wait 5 minutes and try again</li>
                              </ol>
                            </div>
                          </div>
                        </div>
                      </div>
                    }
                  />
                  
                  <FAQItem
                    question="Card declined or payment failed"
                    answer={
                      <div className="space-y-2">
                        <p>Card declines can happen for various security and account reasons:</p>
                        <ul className="list-disc pl-5 space-y-1">
                          <li><strong>Insufficient funds:</strong> Ensure your account has enough balance plus any fees</li>
                          <li><strong>Card restrictions:</strong> Check if your card is enabled for online/international transactions</li>
                          <li><strong>Incorrect details:</strong> Verify card number, expiry date, and CVV are correct</li>
                          <li><strong>Bank security:</strong> Your bank may have blocked the transaction for security</li>
                          <li><strong>Daily/monthly limits:</strong> You may have exceeded your spending limits</li>
                          <li><strong>Expired card:</strong> Check if your card has expired</li>
                          <li><strong>3D Secure/OTP:</strong> Complete any additional verification steps from your bank</li>
                        </ul>
                        <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-950/50 rounded-md border border-blue-200 dark:border-blue-800">
                          <div className="flex items-start gap-2">
                            <AlertCircle className="h-5 w-5 text-blue-600 dark:text-blue-500 flex-shrink-0 mt-0.5" />
                            <div className="text-sm text-blue-800 dark:text-blue-300">
                              <p className="font-medium mb-1">What to do:</p>
                              <ol className="list-decimal pl-4 space-y-1">
                                <li>Contact your bank to authorize online payments</li>
                                <li>Try a different card (debit/credit)</li>
                                <li>Use a different browser or device</li>
                                <li>Wait 15 minutes before retrying</li>
                              </ol>
                            </div>
                          </div>
                        </div>
                      </div>
                    }
                  />
                  
                  <FAQItem
                    question="Payment successful but subscription not activated"
                    answer={
                      <div className="space-y-2">
                        <p>Sometimes there's a delay between payment and subscription activation:</p>
                        <ul className="list-disc pl-5 space-y-1">
                          <li><strong>Processing delay:</strong> Wait 2-5 minutes for automatic verification</li>
                          <li><strong>Verification timeout:</strong> Our system may be verifying your payment</li>
                          <li><strong>Database sync:</strong> Subscription status may take time to update</li>
                          <li><strong>Network issues:</strong> Temporary connectivity problems during verification</li>
                          <li><strong>Webhook failure:</strong> Payment notification may have failed to reach our servers</li>
                        </ul>
                        <div className="mt-3 p-3 bg-yellow-50 dark:bg-yellow-950/50 rounded-md border border-yellow-200 dark:border-yellow-800">
                          <div className="flex items-start gap-2">
                            <Clock className="h-5 w-5 text-yellow-600 dark:text-yellow-500 flex-shrink-0 mt-0.5" />
                            <div className="text-sm text-yellow-800 dark:text-yellow-300">
                              <p className="font-medium mb-1">Step-by-step resolution:</p>
                              <ol className="list-decimal pl-4 space-y-1">
                                <li>Wait 5 minutes, then refresh your profile page</li>
                                <li>Log out and log back in to refresh your session</li>
                                <li>Check your email for payment confirmation</li>
                                <li>Visit the payment success page if you have the reference</li>
                                <li>Contact support with your transaction reference</li>
                              </ol>
                            </div>
                          </div>
                        </div>
                        <div className="mt-3 p-3 bg-red-50 dark:bg-red-950/50 rounded-md border border-red-200 dark:border-red-800">
                          <div className="flex items-start gap-2">
                            <AlertCircle className="h-5 w-5 text-red-600 dark:text-red-500 flex-shrink-0 mt-0.5" />
                            <p className="text-sm text-red-800 dark:text-red-300">
                              <strong>Important:</strong> If your subscription isn't activated within 30 minutes and you were charged, 
                              contact support immediately with your transaction reference number.
                            </p>
                          </div>
                        </div>
                      </div>
                    }
                  />
                  
                  <FAQItem
                    question="Payment verification failed or timed out"
                    answer={
                      <div className="space-y-2">
                        <p>This happens when we cannot confirm your payment with Paystack:</p>
                        <ul className="list-disc pl-5 space-y-1">
                          <li><strong>Network timeout:</strong> Verification took longer than 30 seconds</li>
                          <li><strong>Server overload:</strong> High traffic may delay verification</li>
                          <li><strong>Paystack API issues:</strong> Temporary problems with payment provider</li>
                          <li><strong>Invalid reference:</strong> Payment reference may be corrupted</li>
                        </ul>
                        <div className="mt-3 p-3 bg-orange-50 dark:bg-orange-950/50 rounded-md border border-orange-200 dark:border-orange-800">
                          <div className="flex items-start gap-2">
                            <RefreshCw className="h-5 w-5 text-orange-600 dark:text-orange-500 flex-shrink-0 mt-0.5" />
                            <div className="text-sm text-orange-800 dark:text-orange-300">
                              <p className="font-medium mb-1">What to do:</p>
                              <ul className="list-disc pl-4 space-y-1">
                                <li>Use the "Retry Verification" button on the success page</li>
                                <li>Check your bank statement to confirm if you were charged</li>
                                <li>Contact support with your transaction reference</li>
                                <li>Do not attempt payment again until verification is complete</li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>
                    }
                  />

                  <FAQItem
                    question="Network or connection errors"
                    answer={
                      <div className="space-y-2">
                        <p>Connection issues can interrupt the payment process:</p>
                        <ul className="list-disc pl-5 space-y-1">
                          <li><strong>Slow internet:</strong> Payment requires stable connection</li>
                          <li><strong>Mobile data issues:</strong> Switch to WiFi if using mobile data</li>
                          <li><strong>Firewall/proxy:</strong> Corporate networks may block payment requests</li>
                          <li><strong>DNS issues:</strong> Try using different DNS servers (8.8.8.8, 1.1.1.1)</li>
                        </ul>
                        <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-950/50 rounded-md border border-blue-200 dark:border-blue-800">
                          <div className="flex items-start gap-2">
                            <CheckCircle className="h-5 w-5 text-blue-600 dark:text-blue-500 flex-shrink-0 mt-0.5" />
                            <div className="text-sm text-blue-800 dark:text-blue-300">
                              <p className="font-medium mb-1">Network troubleshooting:</p>
                              <ol className="list-decimal pl-4 space-y-1">
                                <li>Test your connection by visiting other websites</li>
                                <li>Try using a different network (mobile hotspot, different WiFi)</li>
                                <li>Restart your router/modem</li>
                                <li>Use a VPN if you suspect network restrictions</li>
                              </ol>
                            </div>
                          </div>
                        </div>
                      </div>
                    }
                  />

                  <FAQItem
                    question="Browser compatibility issues"
                    answer={
                      <div className="space-y-2">
                        <p>Our payment system requires modern browser features:</p>
                        <ul className="list-disc pl-5 space-y-1">
                          <li><strong>Supported browsers:</strong> Chrome 80+, Firefox 75+, Safari 13+, Edge 80+</li>
                          <li><strong>JavaScript required:</strong> Must be enabled for payment processing</li>
                          <li><strong>Cookies required:</strong> Essential for maintaining payment session</li>
                          <li><strong>Local storage:</strong> Used for temporary payment data</li>
                        </ul>
                        <div className="mt-3 p-3 bg-purple-50 dark:bg-purple-950/50 rounded-md border border-purple-200 dark:border-purple-800">
                          <div className="flex items-start gap-2">
                            <ExternalLink className="h-5 w-5 text-purple-600 dark:text-purple-500 flex-shrink-0 mt-0.5" />
                            <div className="text-sm text-purple-800 dark:text-purple-300">
                              <p className="font-medium mb-1">Browser optimization:</p>
                              <ul className="list-disc pl-4 space-y-1">
                                <li>Update to the latest browser version</li>
                                <li>Disable extensions (especially ad blockers)</li>
                                <li>Clear browser cache and cookies</li>
                                <li>Try incognito/private browsing mode</li>
                                <li>Enable third-party cookies temporarily</li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>
                    }
                  />
                  
                  <FAQItem
                    question="How to contact support"
                    answer={
                      <div className="space-y-2">
                        <p>If you're still experiencing issues after trying the solutions above:</p>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
                          <div className="p-3 bg-blue-50 dark:bg-blue-950/50 rounded-md border border-blue-200 dark:border-blue-800">
                            <h4 className="font-medium text-blue-800 dark:text-blue-300 mb-2">Email Support</h4>
                            <p className="text-sm text-blue-700 dark:text-blue-400 mb-2">
                              Best for detailed issues with screenshots
                            </p>
                            <a href="mailto:<EMAIL>" className="text-blue-600 dark:text-blue-400 underline">
                              <EMAIL>
                            </a>
                            <p className="text-xs text-blue-600 dark:text-blue-500 mt-1">
                              Response time: 2-4 hours
                            </p>
                          </div>
                          <div className="p-3 bg-green-50 dark:bg-green-950/50 rounded-md border border-green-200 dark:border-green-800">
                            <h4 className="font-medium text-green-800 dark:text-green-300 mb-2">WhatsApp Support</h4>
                            <p className="text-sm text-green-700 dark:text-green-400 mb-2">
                              Quick help for urgent payment issues
                            </p>
                            <a href="https://wa.me/2348123456789" className="text-green-600 dark:text-green-400 underline">
                              +234 812 345 6789
                            </a>
                            <p className="text-xs text-green-600 dark:text-green-500 mt-1">
                              Available: 9 AM - 6 PM WAT
                            </p>
                          </div>
                        </div>
                        
                        <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                          <h4 className="font-medium text-gray-800 dark:text-gray-300 mb-2">Information to include:</h4>
                          <ul className="text-sm text-gray-700 dark:text-gray-400 space-y-1">
                            <li>✓ Your registered email address</li>
                            <li>✓ Subscription plan you were purchasing</li>
                            <li>✓ Payment reference number (if available)</li>
                            <li>✓ Exact error message or description</li>
                            <li>✓ Screenshots of error screens</li>
                            <li>✓ Browser and device information</li>
                            <li>✓ Time when the issue occurred</li>
                          </ul>
                        </div>

                        <div className="mt-4 p-3 bg-amber-50 dark:bg-amber-950/50 rounded-md border border-amber-200 dark:border-amber-800">
                          <div className="flex items-start gap-2">
                            <AlertCircle className="h-5 w-5 text-amber-600 dark:text-amber-500 flex-shrink-0 mt-0.5" />
                            <div className="text-sm text-amber-800 dark:text-amber-300">
                              <p className="font-medium mb-1">Priority Support:</p>
                              <p>If you were charged but your subscription wasn't activated, mark your message as "URGENT - Payment Issue" for faster response.</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    }
                  />
                </div>

                <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild variant="outline">
                    <Link to="/profile">
                      Back to Profile
                    </Link>
                  </Button>
                  <Button asChild>
                    <Link to="/contact">
                      Contact Support
                    </Link>
                  </Button>
                  <Button asChild variant="outline">
                    <a href="https://paystack.com/docs/payments/accept-payments/#troubleshooting" target="_blank" rel="noopener noreferrer" className="flex items-center gap-1">
                      Paystack Help <ExternalLink className="h-4 w-4" />
                    </a>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
      <BottomNavigation />
    </div>
  );
};

export default PaymentTroubleshoot;
