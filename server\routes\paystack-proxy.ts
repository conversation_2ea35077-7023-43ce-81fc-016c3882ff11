import express, { Request, Response } from 'express';
import { configManager } from '../lib/config.js';

// Generate a unique reference for each transaction
const generateReference = (): string => {
  const timestamp = new Date().getTime();
  const randomString = Math.random().toString(36).substring(2, 15);
  return `secquiz-${timestamp}-${randomString}`;
};

const router = express.Router();

// Get Paystack configuration
const paystackConfig = configManager.getPaystackConfig();

// Log keys for debugging (remove in production)
if (!paystackConfig.publicKey || !paystackConfig.secretKey) {
  console.error('Missing Paystack API keys. Please check your environment variables.');
}

// Proxy endpoint for Paystack initialization
router.post('/initialize', async (req: Request, res: Response) => {
  try {
    const { email, amount, currency, metadata } = req.body;

    // Generate a reference server-side
    const reference = generateReference();

    // Create the request to Paystack
    // Use the standard transaction/initialize endpoint instead of checkout/request_inline
    const response = await fetch('https://api.paystack.co/transaction/initialize', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${paystackConfig.secretKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email,
        amount,
        currency,
        metadata,
        reference,
        callback_url: `${process.env.VITE_APP_URL || 'https://secquiz.vercel.app'}/payment/success`,
        channels: ['card', 'bank', 'ussd', 'qr', 'mobile_money', 'bank_transfer']
      }),
    });

    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error('Error initializing Paystack payment:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to initialize payment',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Proxy endpoint for Paystack verification
router.post('/verify', async (req: Request, res: Response) => {
  try {
    const { reference } = req.body;

    // Verify the transaction server-side
    const response = await fetch(`https://api.paystack.co/transaction/verify/${reference}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${paystackConfig.secretKey}`,
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error('Error verifying Paystack payment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to verify payment',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
