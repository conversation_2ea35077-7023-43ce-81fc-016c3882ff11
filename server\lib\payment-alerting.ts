/**
 * Payment alerting system for critical issues and monitoring
 */

import { secureLogger } from './secure-logger.js';
import { paymentEventLogger, PaymentEventType, PaymentEventSeverity } from './payment-event-logger.js';
import { supabase } from './supabase.js';

// Alert types
export enum AlertType {
  HIGH_FAILURE_RATE = 'high_failure_rate',
  PAYMENT_TIMEOUT = 'payment_timeout',
  WEBHOOK_FAILURES = 'webhook_failures',
  SYSTEM_ERROR = 'system_error',
  REVENUE_DROP = 'revenue_drop',
  PROCESSING_DELAY = 'processing_delay'
}

// Alert severity levels
export enum AlertSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// Alert interface
export interface PaymentAlert {
  id?: string;
  alert_type: AlertType;
  severity: AlertSeverity;
  title: string;
  message: string;
  metric_value?: number;
  threshold_value?: number;
  affected_count?: number;
  context?: Record<string, any>;
  is_resolved: boolean;
  created_at?: string;
  resolved_at?: string;
}

// Alert configuration
interface AlertConfig {
  enabled: boolean;
  threshold: number;
  timeWindowMinutes: number;
  cooldownMinutes: number;
}

class PaymentAlertingSystem {
  private alertConfigs: Map<AlertType, AlertConfig> = new Map([
    [AlertType.HIGH_FAILURE_RATE, { enabled: true, threshold: 0.2, timeWindowMinutes: 15, cooldownMinutes: 30 }],
    [AlertType.PAYMENT_TIMEOUT, { enabled: true, threshold: 5, timeWindowMinutes: 10, cooldownMinutes: 15 }],
    [AlertType.WEBHOOK_FAILURES, { enabled: true, threshold: 0.1, timeWindowMinutes: 15, cooldownMinutes: 30 }],
    [AlertType.SYSTEM_ERROR, { enabled: true, threshold: 10, timeWindowMinutes: 5, cooldownMinutes: 10 }],
    [AlertType.REVENUE_DROP, { enabled: true, threshold: 0.5, timeWindowMinutes: 60, cooldownMinutes: 120 }],
    [AlertType.PROCESSING_DELAY, { enabled: true, threshold: 15000, timeWindowMinutes: 10, cooldownMinutes: 20 }]
  ]);

  private activeAlerts: Map<AlertType, Date> = new Map();

  /**
   * Check for payment system issues and trigger alerts
   */
  async checkAndTriggerAlerts(): Promise<void> {
    try {
      secureLogger.debug('Starting payment system health check');

      // Check various metrics and trigger alerts if thresholds are exceeded
      await Promise.all([
        this.checkFailureRate(),
        this.checkPaymentTimeouts(),
        this.checkWebhookFailures(),
        this.checkSystemErrors(),
        this.checkProcessingDelays()
      ]);

      secureLogger.debug('Payment system health check completed');

    } catch (error) {
      secureLogger.error('Error during payment system health check', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      await this.triggerAlert({
        alert_type: AlertType.SYSTEM_ERROR,
        severity: AlertSeverity.HIGH,
        title: 'Payment Monitoring System Error',
        message: 'Failed to perform payment system health check',
        context: {
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      });
    }
  }

  /**
   * Check payment failure rate
   */
  private async checkFailureRate(): Promise<void> {
    const config = this.alertConfigs.get(AlertType.HIGH_FAILURE_RATE);
    if (!config?.enabled || this.isInCooldown(AlertType.HIGH_FAILURE_RATE, config.cooldownMinutes)) {
      return;
    }

    try {
      const metrics = await paymentEventLogger.getPaymentMetrics(config.timeWindowMinutes / 60);
      
      if (metrics.total_payments >= 5 && (1 - metrics.success_rate) > config.threshold) {
        await this.triggerAlert({
          alert_type: AlertType.HIGH_FAILURE_RATE,
          severity: AlertSeverity.CRITICAL,
          title: 'High Payment Failure Rate Detected',
          message: `Payment failure rate is ${((1 - metrics.success_rate) * 100).toFixed(1)}% over the last ${config.timeWindowMinutes} minutes`,
          metric_value: 1 - metrics.success_rate,
          threshold_value: config.threshold,
          affected_count: metrics.failed_payments,
          context: {
            total_payments: metrics.total_payments,
            successful_payments: metrics.successful_payments,
            failed_payments: metrics.failed_payments,
            time_window_minutes: config.timeWindowMinutes
          }
        });
      }
    } catch (error) {
      secureLogger.error('Error checking payment failure rate', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Check for payment timeouts
   */
  private async checkPaymentTimeouts(): Promise<void> {
    const config = this.alertConfigs.get(AlertType.PAYMENT_TIMEOUT);
    if (!config?.enabled || this.isInCooldown(AlertType.PAYMENT_TIMEOUT, config.cooldownMinutes)) {
      return;
    }

    try {
      const timeWindow = new Date(Date.now() - config.timeWindowMinutes * 60 * 1000);
      
      const { data, error } = await supabase
        .from('payment_events')
        .select('id')
        .eq('event_type', PaymentEventType.PAYMENT_TIMEOUT)
        .gte('created_at', timeWindow.toISOString());

      if (error) {
        secureLogger.error('Error querying payment timeout events', { error: error.message });
        return;
      }

      const timeoutCount = data?.length || 0;
      
      if (timeoutCount >= config.threshold) {
        await this.triggerAlert({
          alert_type: AlertType.PAYMENT_TIMEOUT,
          severity: AlertSeverity.HIGH,
          title: 'Multiple Payment Timeouts Detected',
          message: `${timeoutCount} payment timeouts occurred in the last ${config.timeWindowMinutes} minutes`,
          metric_value: timeoutCount,
          threshold_value: config.threshold,
          affected_count: timeoutCount,
          context: {
            time_window_minutes: config.timeWindowMinutes
          }
        });
      }
    } catch (error) {
      secureLogger.error('Error checking payment timeouts', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Check webhook failure rate
   */
  private async checkWebhookFailures(): Promise<void> {
    const config = this.alertConfigs.get(AlertType.WEBHOOK_FAILURES);
    if (!config?.enabled || this.isInCooldown(AlertType.WEBHOOK_FAILURES, config.cooldownMinutes)) {
      return;
    }

    try {
      const timeWindow = new Date(Date.now() - config.timeWindowMinutes * 60 * 1000);
      
      const { data, error } = await supabase
        .from('webhook_events')
        .select('status')
        .gte('created_at', timeWindow.toISOString());

      if (error) {
        secureLogger.error('Error querying webhook events', { error: error.message });
        return;
      }

      const webhookEvents = data || [];
      const totalWebhooks = webhookEvents.length;
      const failedWebhooks = webhookEvents.filter(event => event.status === 'failed').length;
      
      if (totalWebhooks >= 10 && failedWebhooks / totalWebhooks > config.threshold) {
        await this.triggerAlert({
          alert_type: AlertType.WEBHOOK_FAILURES,
          severity: AlertSeverity.HIGH,
          title: 'High Webhook Failure Rate',
          message: `Webhook failure rate is ${((failedWebhooks / totalWebhooks) * 100).toFixed(1)}% over the last ${config.timeWindowMinutes} minutes`,
          metric_value: failedWebhooks / totalWebhooks,
          threshold_value: config.threshold,
          affected_count: failedWebhooks,
          context: {
            total_webhooks: totalWebhooks,
            failed_webhooks: failedWebhooks,
            time_window_minutes: config.timeWindowMinutes
          }
        });
      }
    } catch (error) {
      secureLogger.error('Error checking webhook failures', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Check for system errors
   */
  private async checkSystemErrors(): Promise<void> {
    const config = this.alertConfigs.get(AlertType.SYSTEM_ERROR);
    if (!config?.enabled || this.isInCooldown(AlertType.SYSTEM_ERROR, config.cooldownMinutes)) {
      return;
    }

    try {
      const timeWindow = new Date(Date.now() - config.timeWindowMinutes * 60 * 1000);
      
      const { data, error } = await supabase
        .from('payment_events')
        .select('id, error_code')
        .in('severity', ['error', 'critical'])
        .gte('created_at', timeWindow.toISOString());

      if (error) {
        secureLogger.error('Error querying system errors', { error: error.message });
        return;
      }

      const errorCount = data?.length || 0;
      
      if (errorCount >= config.threshold) {
        // Group errors by error code for better context
        const errorGroups = data?.reduce((acc, event) => {
          const code = event.error_code || 'unknown';
          acc[code] = (acc[code] || 0) + 1;
          return acc;
        }, {} as Record<string, number>) || {};

        await this.triggerAlert({
          alert_type: AlertType.SYSTEM_ERROR,
          severity: AlertSeverity.HIGH,
          title: 'High System Error Rate',
          message: `${errorCount} system errors occurred in the last ${config.timeWindowMinutes} minutes`,
          metric_value: errorCount,
          threshold_value: config.threshold,
          affected_count: errorCount,
          context: {
            error_groups: errorGroups,
            time_window_minutes: config.timeWindowMinutes
          }
        });
      }
    } catch (error) {
      secureLogger.error('Error checking system errors', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Check for processing delays
   */
  private async checkProcessingDelays(): Promise<void> {
    const config = this.alertConfigs.get(AlertType.PROCESSING_DELAY);
    if (!config?.enabled || this.isInCooldown(AlertType.PROCESSING_DELAY, config.cooldownMinutes)) {
      return;
    }

    try {
      const metrics = await paymentEventLogger.getPaymentMetrics(config.timeWindowMinutes / 60);
      
      if (metrics.total_payments >= 5 && metrics.average_processing_time > config.threshold) {
        await this.triggerAlert({
          alert_type: AlertType.PROCESSING_DELAY,
          severity: AlertSeverity.MEDIUM,
          title: 'High Payment Processing Time',
          message: `Average payment processing time is ${(metrics.average_processing_time / 1000).toFixed(1)}s over the last ${config.timeWindowMinutes} minutes`,
          metric_value: metrics.average_processing_time,
          threshold_value: config.threshold,
          affected_count: metrics.total_payments,
          context: {
            time_window_minutes: config.timeWindowMinutes
          }
        });
      }
    } catch (error) {
      secureLogger.error('Error checking processing delays', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Trigger an alert
   */
  private async triggerAlert(alert: Omit<PaymentAlert, 'id' | 'is_resolved' | 'created_at'>): Promise<void> {
    try {
      // Mark alert as active to prevent spam
      this.activeAlerts.set(alert.alert_type, new Date());

      // Store alert in database
      const { data, error } = await supabase
        .from('payment_alerts')
        .insert({
          alert_type: alert.alert_type,
          severity: alert.severity,
          title: alert.title,
          message: alert.message,
          metric_value: alert.metric_value,
          threshold_value: alert.threshold_value,
          affected_count: alert.affected_count,
          context: alert.context,
          is_resolved: false,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        secureLogger.error('Failed to store payment alert', { error: error.message });
      } else {
        secureLogger.warn(`Payment Alert Triggered: ${alert.title}`, {
          alertType: alert.alert_type,
          severity: alert.severity,
          message: alert.message,
          metricValue: alert.metric_value,
          thresholdValue: alert.threshold_value,
          affectedCount: alert.affected_count
        });

        // Log as payment event for comprehensive tracking
        await paymentEventLogger.logEvent({
          event_type: PaymentEventType.PAYMENT_ERROR,
          severity: this.mapAlertSeverityToEventSeverity(alert.severity),
          error_code: alert.alert_type,
          error_message: alert.message,
          metadata: {
            alert_id: data?.id,
            alert_type: alert.alert_type,
            metric_value: alert.metric_value,
            threshold_value: alert.threshold_value,
            affected_count: alert.affected_count,
            context: alert.context
          }
        });

        // Send notifications (email, Slack, etc.) - implement based on requirements
        await this.sendAlertNotification(alert);
      }
    } catch (error) {
      secureLogger.error('Error triggering payment alert', {
        alertType: alert.alert_type,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Check if alert type is in cooldown period
   */
  private isInCooldown(alertType: AlertType, cooldownMinutes: number): boolean {
    const lastAlert = this.activeAlerts.get(alertType);
    if (!lastAlert) return false;

    const cooldownEnd = new Date(lastAlert.getTime() + cooldownMinutes * 60 * 1000);
    return new Date() < cooldownEnd;
  }

  /**
   * Map alert severity to payment event severity
   */
  private mapAlertSeverityToEventSeverity(alertSeverity: AlertSeverity): PaymentEventSeverity {
    switch (alertSeverity) {
      case AlertSeverity.CRITICAL:
        return PaymentEventSeverity.CRITICAL;
      case AlertSeverity.HIGH:
        return PaymentEventSeverity.ERROR;
      case AlertSeverity.MEDIUM:
        return PaymentEventSeverity.WARN;
      default:
        return PaymentEventSeverity.INFO;
    }
  }

  /**
   * Send alert notification (placeholder for integration with notification services)
   */
  private async sendAlertNotification(alert: Omit<PaymentAlert, 'id' | 'is_resolved' | 'created_at'>): Promise<void> {
    // This is a placeholder for notification integration
    // In a real implementation, you would integrate with:
    // - Email service (SendGrid, AWS SES, etc.)
    // - Slack/Discord webhooks
    // - SMS service (Twilio, etc.)
    // - PagerDuty or similar alerting service

    secureLogger.info('Alert notification would be sent', {
      alertType: alert.alert_type,
      severity: alert.severity,
      title: alert.title
    });

    // Example: Log to console for now
    console.log(`🚨 PAYMENT ALERT [${alert.severity.toUpperCase()}]: ${alert.title}`);
    console.log(`   Message: ${alert.message}`);
    if (alert.metric_value !== undefined) {
      console.log(`   Metric: ${alert.metric_value} (threshold: ${alert.threshold_value})`);
    }
    if (alert.affected_count !== undefined) {
      console.log(`   Affected: ${alert.affected_count}`);
    }
  }

  /**
   * Resolve an alert
   */
  async resolveAlert(alertId: string, resolution: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('payment_alerts')
        .update({
          is_resolved: true,
          resolved_at: new Date().toISOString(),
          context: supabase.raw(`context || '{"resolution": "${resolution}"}'::jsonb`)
        })
        .eq('id', alertId);

      if (error) {
        secureLogger.error('Failed to resolve payment alert', { 
          alertId, 
          error: error.message 
        });
      } else {
        secureLogger.info('Payment alert resolved', { alertId, resolution });
      }
    } catch (error) {
      secureLogger.error('Error resolving payment alert', {
        alertId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get active alerts
   */
  async getActiveAlerts(limit: number = 50): Promise<PaymentAlert[]> {
    try {
      const { data, error } = await supabase
        .from('payment_alerts')
        .select('*')
        .eq('is_resolved', false)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        secureLogger.error('Failed to fetch active alerts', { error: error.message });
        return [];
      }

      return data || [];
    } catch (error) {
      secureLogger.error('Error fetching active alerts', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return [];
    }
  }

  /**
   * Update alert configuration
   */
  updateAlertConfig(alertType: AlertType, config: Partial<AlertConfig>): void {
    const currentConfig = this.alertConfigs.get(alertType);
    if (currentConfig) {
      this.alertConfigs.set(alertType, { ...currentConfig, ...config });
      secureLogger.info('Alert configuration updated', { alertType, config });
    }
  }

  /**
   * Get alert configuration
   */
  getAlertConfig(alertType: AlertType): AlertConfig | undefined {
    return this.alertConfigs.get(alertType);
  }
}

// Export singleton instance
export const paymentAlertingSystem = new PaymentAlertingSystem();

// Export convenience functions
export const {
  checkAndTriggerAlerts,
  resolveAlert,
  getActiveAlerts,
  updateAlertConfig,
  getAlertConfig
} = paymentAlertingSystem;