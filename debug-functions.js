import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function debugFunctions() {
  console.log('Debugging function issues...');
  
  try {
    // Let's try to see what functions exist and their definitions
    console.log('1. Checking existing functions...');
    
    const checkFunctionsSQL = `
      SELECT 
        proname as function_name,
        pg_get_function_arguments(oid) as arguments,
        pg_get_function_result(oid) as return_type
      FROM pg_proc 
      WHERE proname LIKE '%user%' OR proname LIKE '%profile%'
      ORDER BY proname;
    `;
    
    const { data: functionsData, error: functionsError } = await supabase.rpc('execute_sql', { query: checkFunctionsSQL });
    
    if (functionsError) {
      console.error('❌ Failed to check functions:', functionsError);
    } else {
      console.log('✓ Functions check completed');
      console.log('Result:', functionsData);
    }
    
    // Let's try to create a function with a completely different name
    console.log('2. Creating function with different name...');
    
    const differentNameSQL = `
      DROP FUNCTION IF EXISTS public.admin_get_all_profiles() CASCADE;
      
      CREATE OR REPLACE FUNCTION public.admin_get_all_profiles()
      RETURNS TABLE (
        profile_id UUID,
        auth_user_id UUID,
        email_address TEXT
      ) AS $$
      BEGIN
        RETURN QUERY
        SELECT
          p.id,
          p.user_id,
          p.email
        FROM public.user_profiles p
        ORDER BY p.created_at DESC;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;
      
      GRANT EXECUTE ON FUNCTION public.admin_get_all_profiles() TO authenticated;
    `;
    
    const { data: diffNameData, error: diffNameError } = await supabase.rpc('execute_sql', { query: differentNameSQL });
    
    if (diffNameError) {
      console.error('❌ Different name function failed:', diffNameError);
    } else {
      console.log('✓ Different name function created');
      
      // Test the different name function
      const { data: testData, error: testError } = await supabase.rpc('admin_get_all_profiles');
      
      if (testError) {
        console.error('❌ Different name function test failed:', testError);
      } else {
        console.log('✅ Different name function works!');
        console.log('Data returned:', testData ? testData.length : 0, 'records');
        
        // Now let's try to recreate get_all_user_profiles using the working pattern
        console.log('3. Recreating get_all_user_profiles using working pattern...');
        
        const workingPatternSQL = `
          DROP FUNCTION IF EXISTS public.get_all_user_profiles() CASCADE;
          
          CREATE OR REPLACE FUNCTION public.get_all_user_profiles()
          RETURNS TABLE (
            id UUID,
            user_id UUID,
            email TEXT,
            full_name TEXT,
            is_subscribed BOOLEAN,
            is_admin BOOLEAN,
            subscription_expires_at TIMESTAMPTZ,
            subscription_status TEXT,
            subscription_plan TEXT,
            created_at TIMESTAMPTZ,
            updated_at TIMESTAMPTZ,
            last_sign_in_at TIMESTAMPTZ
          ) AS $$
          DECLARE
            current_user UUID;
            user_is_admin BOOLEAN;
          BEGIN
            current_user := auth.uid();
            
            SELECT p.is_admin INTO user_is_admin
            FROM public.user_profiles p
            WHERE p.user_id = current_user;
            
            IF NOT COALESCE(user_is_admin, false) THEN
              RAISE EXCEPTION 'Access denied. Admin privileges required.';
            END IF;
            
            RETURN QUERY
            SELECT
              p.id,
              p.user_id,
              p.email,
              p.full_name,
              p.is_subscribed,
              p.is_admin,
              p.subscription_expires_at,
              p.subscription_status,
              p.subscription_plan,
              p.created_at,
              p.updated_at,
              u.last_sign_in_at
            FROM public.user_profiles p
            LEFT JOIN auth.users u ON p.user_id = u.id
            ORDER BY p.created_at DESC;
          END;
          $$ LANGUAGE plpgsql SECURITY DEFINER;
          
          GRANT EXECUTE ON FUNCTION public.get_all_user_profiles() TO authenticated;
        `;
        
        const { data: workingData, error: workingError } = await supabase.rpc('execute_sql', { query: workingPatternSQL });
        
        if (workingError) {
          console.error('❌ Working pattern function failed:', workingError);
        } else {
          console.log('✓ Working pattern function created');
          
          // Test the working pattern function
          const { data: workingTestData, error: workingTestError } = await supabase.rpc('get_all_user_profiles');
          
          if (workingTestError) {
            if (workingTestError.message.includes('Access denied') || workingTestError.message.includes('Admin privileges')) {
              console.log('✅ Working pattern function is working correctly! (Requires admin authentication)');
              return true;
            } else {
              console.error('❌ Working pattern function test failed:', workingTestError);
            }
          } else {
            console.log('✅ Working pattern function test successful!');
            console.log('Data returned:', workingTestData ? workingTestData.length : 0, 'records');
            return true;
          }
        }
      }
    }
    
  } catch (error) {
    console.error('Debug failed with exception:', error);
    return false;
  }
  
  return false;
}

debugFunctions().then(success => {
  if (success) {
    console.log('\n🎉 SUCCESS: Found a working solution!');
  } else {
    console.log('\n❌ FAILED: Still investigating the issue.');
  }
});