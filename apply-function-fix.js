import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

// Use service role key for admin operations
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyFunctionFix() {
  console.log('Applying get_all_user_profiles function fix...');
  
  try {
    // Read the SQL fix file
    const sqlFix = fs.readFileSync('fix-get-all-user-profiles-function.sql', 'utf8');
    
    console.log('1. Attempting to execute SQL fix using execute_sql function...');
    
    // Try using the execute_sql RPC function first
    try {
      const { data, error } = await supabase.rpc('execute_sql', { query: sqlFix });
      
      if (error) {
        console.error('execute_sql RPC failed:', error);
        throw error;
      }
      
      console.log('✓ SQL fix applied successfully using execute_sql RPC');
      console.log('Result:', data);
      
    } catch (rpcError) {
      console.log('execute_sql RPC not available, trying direct SQL execution...');
      
      // Split the SQL into individual statements and execute them
      const statements = sqlFix
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
      
      for (const statement of statements) {
        if (statement.includes('SELECT')) {
          // For SELECT statements, use .from() or .rpc()
          continue;
        }
        
        console.log('Executing statement:', statement.substring(0, 50) + '...');
        
        const { error } = await supabase.from('_').select('*').limit(0); // This won't work for DDL
        
        if (error) {
          console.error('Statement failed:', error);
        }
      }
    }
    
    // Test the fixed function
    console.log('2. Testing the fixed function...');
    
    // First, let's try to authenticate as an admin user
    // We'll need to sign in with admin credentials for this test
    console.log('Note: Function requires admin authentication to test properly');
    
    const { data: testData, error: testError } = await supabase.rpc('get_all_user_profiles');
    
    if (testError) {
      console.error('❌ Function test failed:', testError);
      
      // Check if it's an authentication error vs a function error
      if (testError.message.includes('Access denied') || testError.message.includes('Admin privileges')) {
        console.log('✓ Function is working but requires admin authentication (expected behavior)');
      } else if (testError.code === '42702') {
        console.error('❌ Ambiguous column reference error still exists');
      } else {
        console.error('❌ Other function error:', testError);
      }
    } else {
      console.log('✓ Function test successful');
      console.log('Data returned:', testData ? testData.length : 0, 'records');
    }
    
  } catch (error) {
    console.error('Fix application failed:', error);
  }
}

applyFunctionFix();