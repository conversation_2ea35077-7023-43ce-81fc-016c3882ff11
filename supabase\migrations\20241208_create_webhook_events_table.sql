-- Create webhook_events table for tracking webhook processing
CREATE TABLE IF NOT EXISTS webhook_events (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    event_id TEXT NOT NULL UNIQUE,
    event_type TEXT NOT NULL,
    reference TEXT,
    status TEXT NOT NULL CHECK (status IN ('received', 'processing', 'completed', 'failed', 'duplicate')),
    payload_hash TEXT NOT NULL,
    processing_attempts INTEGER DEFAULT 1,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_webhook_events_event_id ON webhook_events(event_id);
CREATE INDEX IF NOT EXISTS idx_webhook_events_event_type ON webhook_events(event_type);
CREATE INDEX IF NOT EXISTS idx_webhook_events_status ON webhook_events(status);
CREATE INDEX IF NOT EXISTS idx_webhook_events_reference ON webhook_events(reference);
CREATE INDEX IF NOT EXISTS idx_webhook_events_created_at ON webhook_events(created_at);

-- Add RLS (Row Level Security) policies
ALTER TABLE webhook_events ENABLE ROW LEVEL SECURITY;

-- Create policy for service role access (webhooks run with service role)
CREATE POLICY "Service role can manage webhook events" ON webhook_events
    FOR ALL USING (auth.role() = 'service_role');

-- Create policy for authenticated users to read their own webhook events (if needed for debugging)
CREATE POLICY "Users can read webhook events for their references" ON webhook_events
    FOR SELECT USING (
        auth.role() = 'authenticated' AND 
        reference IN (
            SELECT last_payment_reference 
            FROM subscriptions 
            WHERE user_id = auth.uid()
        )
    );

-- Add comment to table
COMMENT ON TABLE webhook_events IS 'Tracks webhook events for idempotency and monitoring';
COMMENT ON COLUMN webhook_events.event_id IS 'Unique identifier for the webhook event';
COMMENT ON COLUMN webhook_events.event_type IS 'Type of webhook event (e.g., charge.success)';
COMMENT ON COLUMN webhook_events.reference IS 'Payment reference associated with the event';
COMMENT ON COLUMN webhook_events.status IS 'Processing status of the webhook event';
COMMENT ON COLUMN webhook_events.payload_hash IS 'SHA256 hash of the webhook payload for idempotency';
COMMENT ON COLUMN webhook_events.processing_attempts IS 'Number of times this event has been processed';
COMMENT ON COLUMN webhook_events.error_message IS 'Error message if processing failed';