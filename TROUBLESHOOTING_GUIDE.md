# SecQuiz Payment System Troubleshooting Guide

This guide provides solutions for common issues encountered with the SecQuiz payment integration system.

## Quick Diagnostic Steps

### 1. Check System Health
Visit the health check endpoint: `GET /api/health`

Expected response:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "services": {
    "database": "connected",
    "paystack": "configured",
    "environment": "valid"
  }
}
```

### 2. Verify Environment Variables
Run the environment validation check on server startup. Look for these log messages:
- ✅ "Environment validation passed"
- ❌ "Missing required environment variable: [variable_name]"

### 3. Check Server Logs
Look for these log patterns:
- Payment verification: `[PAYMENT] Verifying payment: [reference]`
- Webhook processing: `[WEBHOOK] Processing event: [event_type]`
- Database operations: `[DB] Subscription updated for user: [user_id]`
- Errors: `[ERROR] [component] [error_message]`

## Common Issues and Solutions

### Payment Issues

#### Issue: Payment popup doesn't appear
**Symptoms:**
- Subscribe button doesn't trigger Paystack popup
- Console shows "Paystack not loaded" error
- No network requests to Paystack

**Causes & Solutions:**
1. **Missing or invalid public key**
   - Check `VITE_PAYSTACK_PUBLIC_KEY` in `.env`
   - Ensure key starts with `pk_test_` or `pk_live_`
   - Verify no extra spaces or quotes

2. **Network connectivity issues**
   - Check if Paystack CDN is accessible
   - Verify no ad blockers are interfering
   - Test in incognito mode

3. **JavaScript errors**
   - Open browser console and check for errors
   - Ensure react-paystack library is properly installed
   - Verify component imports are correct

**Quick Fix:**
```bash
# Verify environment variable
echo $VITE_PAYSTACK_PUBLIC_KEY

# Reinstall paystack library
npm uninstall react-paystack
npm install react-paystack
```

#### Issue: Payment verification fails
**Symptoms:**
- Payment completes but subscription isn't activated
- "Payment verification failed" error message
- User redirected to error page

**Causes & Solutions:**
1. **Server not running or unreachable**
   - Verify server is running: `curl http://localhost:5000/api/health`
   - Check `VITE_API_URL` points to correct server
   - Ensure no firewall blocking requests

2. **Invalid Paystack secret key**
   - Verify `PAYSTACK_SECRET_KEY` in server `.env`
   - Ensure key starts with `sk_test_` or `sk_live_`
   - Test key with Paystack API directly

3. **Database connection issues**
   - Check database connectivity
   - Verify Supabase credentials
   - Check for table permission issues

**Debug Steps:**
```bash
# Test payment verification endpoint
curl -X POST http://localhost:5000/api/payments/verify \
  -H "Content-Type: application/json" \
  -d '{"reference":"test_reference","userId":"test_user"}'

# Check server logs
tail -f server/logs/app.log
```

#### Issue: Webhook events not processed
**Symptoms:**
- Payments succeed but subscriptions aren't updated
- No webhook logs in server
- Paystack dashboard shows failed webhook deliveries

**Causes & Solutions:**
1. **Webhook URL not accessible**
   - Verify webhook URL in Paystack dashboard
   - Ensure server is publicly accessible
   - Test webhook endpoint: `curl -X POST your-domain.com/api/webhooks/paystack`

2. **Signature verification failing**
   - Check `PAYSTACK_WEBHOOK_SECRET` matches dashboard
   - Verify webhook secret is correctly configured
   - Check for encoding issues

3. **Webhook processing errors**
   - Check server logs for webhook errors
   - Verify database permissions for webhook processing
   - Ensure idempotency handling works correctly

**Debug Steps:**
```bash
# Test webhook endpoint locally
curl -X POST http://localhost:5000/api/webhooks/paystack \
  -H "Content-Type: application/json" \
  -H "x-paystack-signature: test_signature" \
  -d '{"event":"charge.success","data":{"reference":"test"}}'
```

### Database Issues

#### Issue: Subscription data inconsistency
**Symptoms:**
- User shows as subscribed but can't access content
- Subscription table and user_profiles table don't match
- Expired subscriptions still showing as active

**Causes & Solutions:**
1. **Failed atomic operations**
   - Check database transaction logs
   - Verify database functions are working
   - Run data consistency check script

2. **Missing database constraints**
   - Verify foreign key constraints exist
   - Check for orphaned records
   - Run database integrity checks

**Fix Script:**
```sql
-- Check for inconsistencies
SELECT u.id, u.is_subscribed, s.is_active, s.end_date
FROM user_profiles u
LEFT JOIN subscriptions s ON u.user_id = s.user_id
WHERE u.is_subscribed != s.is_active
   OR (s.end_date < NOW() AND s.is_active = true);

-- Fix inconsistencies
UPDATE user_profiles 
SET is_subscribed = false, subscription_expires_at = NULL
WHERE user_id IN (
  SELECT user_id FROM subscriptions 
  WHERE end_date < NOW() AND is_active = true
);
```

#### Issue: Database connection failures
**Symptoms:**
- "Database connection failed" errors
- Timeouts on database operations
- Intermittent subscription updates

**Causes & Solutions:**
1. **Connection pool exhaustion**
   - Increase connection pool size
   - Check for connection leaks
   - Implement connection retry logic

2. **Database server issues**
   - Check Supabase dashboard for outages
   - Verify database server resources
   - Check for long-running queries

### Environment and Configuration Issues

#### Issue: Environment variables not loading
**Symptoms:**
- "Missing environment variable" errors
- Default values being used instead of configured values
- Different behavior between development and production

**Causes & Solutions:**
1. **File location or naming issues**
   - Verify `.env` file is in correct location
   - Check file permissions
   - Ensure no hidden characters in file

2. **Variable naming or formatting**
   - Check for typos in variable names
   - Verify no spaces around equals sign
   - Ensure no quotes unless needed

**Debug Steps:**
```bash
# Check if environment file exists
ls -la .env server/.env

# Verify variable loading
node -e "console.log(process.env.VITE_PAYSTACK_PUBLIC_KEY)"
```

#### Issue: CORS errors in browser
**Symptoms:**
- "CORS policy" errors in browser console
- API requests failing from client
- Network tab shows preflight request failures

**Causes & Solutions:**
1. **Incorrect CORS configuration**
   - Update `CORS_ORIGIN` in server environment
   - Verify client domain is allowed
   - Check for protocol mismatches (http vs https)

2. **Missing CORS headers**
   - Verify CORS middleware is configured
   - Check for proper preflight handling
   - Ensure credentials are handled correctly

### TypeScript and Build Issues

#### Issue: TypeScript compilation errors
**Symptoms:**
- Build fails with type errors
- IDE shows red squiggly lines
- "Property does not exist" errors

**Causes & Solutions:**
1. **Missing type definitions**
   - Install missing @types packages
   - Add custom type definitions
   - Update tsconfig.json configuration

2. **Import/export issues**
   - Verify ES6 import syntax
   - Check for circular dependencies
   - Ensure proper module resolution

**Fix Commands:**
```bash
# Install missing types
npm install --save-dev @types/node @types/react

# Check TypeScript configuration
npx tsc --noEmit

# Fix import issues
npm run lint --fix
```

## Performance Issues

### Issue: Slow payment processing
**Symptoms:**
- Payment verification takes too long
- Users experience timeouts
- High server response times

**Causes & Solutions:**
1. **Database query optimization**
   - Add indexes to frequently queried columns
   - Optimize subscription lookup queries
   - Use database connection pooling

2. **API timeout configuration**
   - Increase timeout values for Paystack API calls
   - Implement retry logic with exponential backoff
   - Add circuit breaker pattern

### Issue: High memory usage
**Symptoms:**
- Server crashes with out-of-memory errors
- Slow response times
- High CPU usage

**Causes & Solutions:**
1. **Memory leaks**
   - Check for unclosed database connections
   - Verify event listeners are properly removed
   - Monitor memory usage patterns

2. **Large payload processing**
   - Implement request size limits
   - Add streaming for large responses
   - Optimize data serialization

## Security Issues

### Issue: Webhook signature verification fails
**Symptoms:**
- All webhook requests are rejected
- "Invalid signature" errors in logs
- Paystack dashboard shows failed deliveries

**Causes & Solutions:**
1. **Incorrect secret key**
   - Verify webhook secret matches Paystack dashboard
   - Check for encoding issues
   - Ensure secret is properly stored

2. **Signature calculation errors**
   - Verify HMAC SHA512 implementation
   - Check request body handling
   - Ensure timing-safe comparison

**Debug Code:**
```javascript
// Test signature verification
const crypto = require('crypto');
const secret = process.env.PAYSTACK_WEBHOOK_SECRET;
const payload = JSON.stringify(requestBody);
const signature = crypto
  .createHmac('sha512', secret)
  .update(payload)
  .digest('hex');
console.log('Expected signature:', signature);
console.log('Received signature:', receivedSignature);
```

## Monitoring and Alerting

### Set up monitoring for:
- Payment success/failure rates
- API response times
- Database connection health
- Webhook processing success
- Error rates and patterns

### Key metrics to track:
- Payment conversion rate
- Average payment processing time
- Database query performance
- Server resource utilization
- User subscription activation rate

## Getting Help

### Internal Resources
1. Check server logs: `tail -f server/logs/app.log`
2. Review database logs in Supabase dashboard
3. Check Paystack dashboard for transaction details
4. Use browser developer tools for client-side issues

### External Support
- **Paystack Support**: <EMAIL>
- **Supabase Support**: <EMAIL>
- **Community Forums**: Stack Overflow, GitHub Issues

### Emergency Procedures
1. If payments are completely broken, disable payment buttons
2. If database is corrupted, restore from latest backup
3. If security breach suspected, rotate all API keys immediately
4. Document all issues and resolutions for future reference

## Prevention Tips

1. **Regular Testing**
   - Test payment flows weekly
   - Monitor webhook delivery success
   - Verify database consistency regularly

2. **Code Quality**
   - Use TypeScript for type safety
   - Implement comprehensive error handling
   - Add unit and integration tests

3. **Monitoring**
   - Set up alerts for payment failures
   - Monitor API response times
   - Track subscription activation rates

4. **Security**
   - Rotate API keys regularly
   - Keep dependencies updated
   - Regular security audits