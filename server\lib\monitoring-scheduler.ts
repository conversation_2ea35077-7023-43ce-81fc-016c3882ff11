/**
 * Monitoring scheduler for automated health checks and alerting
 */

import { secureLogger } from './secure-logger.js';
import { paymentAlertingSystem } from './payment-alerting.js';
import { cleanupExpiredSubscriptionsWithRetry } from '../services/subscription.js';
import { supabase } from './supabase.js';

class MonitoringScheduler {
  private intervals: Map<string, NodeJS.Timeout> = new Map();
  private isRunning: boolean = false;

  /**
   * Start all monitoring tasks
   */
  start(): void {
    if (this.isRunning) {
      secureLogger.warn('Monitoring scheduler is already running');
      return;
    }

    secureLogger.info('Starting monitoring scheduler');
    this.isRunning = true;

    // Health check and alerting - every 5 minutes
    this.scheduleTask('health_check', () => this.runHealthCheck(), 5 * 60 * 1000);

    // Subscription cleanup - every hour
    this.scheduleTask('subscription_cleanup', () => this.runSubscriptionCleanup(), 60 * 60 * 1000);

    // Database maintenance - every 6 hours
    this.scheduleTask('database_maintenance', () => this.runDatabaseMaintenance(), 6 * 60 * 60 * 1000);

    // Metrics collection - every 15 minutes
    this.scheduleTask('metrics_collection', () => this.collectMetrics(), 15 * 60 * 1000);

    secureLogger.info('Monitoring scheduler started with all tasks');
  }

  /**
   * Stop all monitoring tasks
   */
  stop(): void {
    if (!this.isRunning) {
      return;
    }

    secureLogger.info('Stopping monitoring scheduler');
    
    for (const [taskName, interval] of this.intervals) {
      clearInterval(interval);
      secureLogger.debug(`Stopped monitoring task: ${taskName}`);
    }

    this.intervals.clear();
    this.isRunning = false;
    
    secureLogger.info('Monitoring scheduler stopped');
  }

  /**
   * Get status of monitoring scheduler
   */
  getStatus(): {
    isRunning: boolean;
    activeTasks: string[];
    uptime: number;
  } {
    return {
      isRunning: this.isRunning,
      activeTasks: Array.from(this.intervals.keys()),
      uptime: process.uptime()
    };
  }

  /**
   * Schedule a monitoring task
   */
  private scheduleTask(name: string, task: () => Promise<void>, intervalMs: number): void {
    // Run immediately
    this.runTaskSafely(name, task);

    // Schedule recurring execution
    const interval = setInterval(() => {
      this.runTaskSafely(name, task);
    }, intervalMs);

    this.intervals.set(name, interval);
    
    secureLogger.debug(`Scheduled monitoring task: ${name} (interval: ${intervalMs}ms)`);
  }

  /**
   * Run a task with error handling
   */
  private async runTaskSafely(name: string, task: () => Promise<void>): Promise<void> {
    try {
      secureLogger.debug(`Running monitoring task: ${name}`);
      const startTime = Date.now();
      
      await task();
      
      const duration = Date.now() - startTime;
      secureLogger.debug(`Completed monitoring task: ${name} (${duration}ms)`);
      
    } catch (error) {
      secureLogger.error(`Monitoring task failed: ${name}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
    }
  }

  /**
   * Run health check and alerting
   */
  private async runHealthCheck(): Promise<void> {
    secureLogger.debug('Running scheduled health check and alerting');
    
    try {
      await paymentAlertingSystem.checkAndTriggerAlerts();
      secureLogger.debug('Health check and alerting completed successfully');
    } catch (error) {
      secureLogger.error('Health check and alerting failed', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Run subscription cleanup
   */
  private async runSubscriptionCleanup(): Promise<void> {
    secureLogger.debug('Running scheduled subscription cleanup');
    
    try {
      const result = await cleanupExpiredSubscriptionsWithRetry(3);
      
      if (result.success) {
        secureLogger.info('Subscription cleanup completed', {
          expiredCount: result.data?.expiredCount || 0,
          cleanedUsers: result.data?.cleanedUsers?.length || 0
        });
      } else {
        secureLogger.error('Subscription cleanup failed', {
          error: result.error
        });
      }
    } catch (error) {
      secureLogger.error('Subscription cleanup task failed', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Run database maintenance tasks
   */
  private async runDatabaseMaintenance(): Promise<void> {
    secureLogger.debug('Running scheduled database maintenance');
    
    try {
      // Clean up old payment events (keep 90 days)
      const { data: cleanupResult, error: cleanupError } = await supabase
        .rpc('cleanup_old_payment_events', { retention_days: 90 });

      if (cleanupError) {
        secureLogger.error('Payment events cleanup failed', { error: cleanupError.message });
      } else {
        secureLogger.info('Payment events cleanup completed', { 
          deletedRecords: cleanupResult || 0 
        });
      }

      // Clean up old webhook events (keep 30 days)
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const { error: webhookCleanupError } = await supabase
        .from('webhook_events')
        .delete()
        .lt('created_at', thirtyDaysAgo.toISOString());

      if (webhookCleanupError) {
        secureLogger.error('Webhook events cleanup failed', { error: webhookCleanupError.message });
      } else {
        secureLogger.info('Webhook events cleanup completed');
      }

      // Resolve old alerts (keep active alerts for 7 days max)
      const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      const { error: alertCleanupError } = await supabase
        .from('payment_alerts')
        .update({ 
          is_resolved: true, 
          resolved_at: new Date().toISOString() 
        })
        .eq('is_resolved', false)
        .lt('created_at', sevenDaysAgo.toISOString());

      if (alertCleanupError) {
        secureLogger.error('Alert cleanup failed', { error: alertCleanupError.message });
      } else {
        secureLogger.info('Alert cleanup completed');
      }

    } catch (error) {
      secureLogger.error('Database maintenance task failed', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Collect and log system metrics
   */
  private async collectMetrics(): Promise<void> {
    secureLogger.debug('Collecting system metrics');
    
    try {
      const metrics = {
        timestamp: new Date().toISOString(),
        system: {
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          cpu_usage: process.cpuUsage()
        },
        monitoring: {
          active_tasks: this.intervals.size,
          scheduler_uptime: this.isRunning ? process.uptime() : 0
        }
      };

      // Log metrics for monitoring systems to pick up
      secureLogger.info('System metrics collected', metrics);

      // Store metrics in database for historical analysis
      const { error } = await supabase
        .from('system_metrics')
        .insert({
          metric_type: 'system_health',
          metric_data: metrics,
          created_at: new Date().toISOString()
        });

      if (error && !error.message.includes('relation "system_metrics" does not exist')) {
        secureLogger.warn('Failed to store system metrics', { error: error.message });
      }

    } catch (error) {
      secureLogger.error('Metrics collection task failed', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Force run a specific task (for testing/debugging)
   */
  async runTask(taskName: string): Promise<void> {
    switch (taskName) {
      case 'health_check':
        await this.runHealthCheck();
        break;
      case 'subscription_cleanup':
        await this.runSubscriptionCleanup();
        break;
      case 'database_maintenance':
        await this.runDatabaseMaintenance();
        break;
      case 'metrics_collection':
        await this.collectMetrics();
        break;
      default:
        throw new Error(`Unknown task: ${taskName}`);
    }
  }
}

// Export singleton instance
export const monitoringScheduler = new MonitoringScheduler();

// Export convenience functions
export const {
  start: startMonitoring,
  stop: stopMonitoring,
  getStatus: getMonitoringStatus,
  runTask: runMonitoringTask
} = monitoringScheduler;