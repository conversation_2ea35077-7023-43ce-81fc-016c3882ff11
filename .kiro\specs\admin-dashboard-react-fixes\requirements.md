# Requirements Document

## Introduction

The AdminDashboard component is experiencing critical React errors that prevent it from rendering properly. The main error indicates that `useState` is being called on null, which suggests issues with React imports, hook usage, or component context. This feature will systematically identify and fix all React-related errors in the AdminDashboard component and its dependencies.

## Requirements

### Requirement 1

**User Story:** As a developer, I want the AdminDashboard component to render without React errors, so that administrators can access the dashboard functionality.

#### Acceptance Criteria

1. WHEN the AdminDashboard component is loaded THEN it SHALL render without throwing "Cannot read properties of null (reading 'useState')" errors
2. WHEN React hooks are used in the useAdminUsers hook THEN they SHALL be properly imported and called within a valid React component context
3. WHEN the AdminDashboard component mounts THEN all child components SHALL render without React errors

### Requirement 2

**User Story:** As a developer, I want all React imports to be properly structured, so that hooks and components function correctly.

#### Acceptance Criteria

1. WHEN React hooks are imported THEN they SHALL be imported from the correct React package
2. WHEN custom hooks are used THEN they SHALL only be called from within React functional components or other hooks
3. WHEN React components are defined THEN they SHALL follow proper React component patterns

### Requirement 3

**User Story:** As a developer, I want proper error boundaries and error handling, so that React errors are caught and displayed gracefully.

#### Acceptance Criteria

1. WHEN a React error occurs THEN it SHALL be caught by an error boundary if one exists
2. WHEN hooks fail to initialize THEN appropriate error messages SHALL be displayed to the user
3. WHEN the component encounters an error THEN it SHALL not crash the entire application

### Requirement 4

**User Story:** As an administrator, I want the AdminDashboard to load successfully, so that I can manage users, topics, and questions.

#### Acceptance Criteria

1. WHEN an admin user navigates to the AdminDashboard THEN the component SHALL load without JavaScript errors
2. WHEN the AdminDashboard loads THEN all tabs (topics, questions, users) SHALL be accessible
3. WHEN the useAdminUsers hook is called THEN it SHALL successfully fetch and display user data

### Requirement 5

**User Story:** As a developer, I want the Supabase database functions to work correctly, so that the AdminDashboard can fetch user data without 400 errors.

#### Acceptance Criteria

1. WHEN the get_all_user_profiles RPC function is called THEN it SHALL return user data without a 400 status error
2. WHEN database functions are missing or broken THEN they SHALL be created or fixed to support the admin functionality
3. WHEN the useAdminUsers hook makes database calls THEN they SHALL complete successfully or gracefully handle errors