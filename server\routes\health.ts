/**
 * Comprehensive health check and debugging endpoints
 */

import express, { Request, Response } from 'express';
import { configManager } from '../lib/config.js';
import { environmentValidator } from '../lib/env-validator.js';
import { supabase } from '../lib/supabase.js';
import { secureLogger } from '../lib/secure-logger.js';
import { paymentEventLogger } from '../lib/payment-event-logger.js';
import { paymentAlertingSystem } from '../lib/payment-alerting.js';
import { subscriptionServiceHealthCheck } from '../services/subscription.js';
import { monitoringScheduler } from '../lib/monitoring-scheduler.js';
import axios from 'axios';

const router = express.Router();

// Health check status interface
interface HealthCheckResult {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  uptime: number;
  version?: string;
  environment: string;
  checks: {
    database: HealthCheck;
    paystack: HealthCheck;
    configuration: HealthCheck;
    subscription_service: HealthCheck;
    payment_system: HealthCheck;
  };
  metrics?: {
    memory: NodeJS.MemoryUsage;
    cpu_usage?: number;
    active_connections?: number;
  };
}

interface HealthCheck {
  status: 'pass' | 'fail' | 'warn';
  message: string;
  response_time_ms?: number;
  last_checked: string;
  details?: Record<string, any>;
}

// Payment system status interface
interface PaymentSystemStatus {
  status: 'operational' | 'degraded' | 'down';
  timestamp: string;
  metrics: {
    success_rate_24h: number;
    average_processing_time: number;
    total_payments_24h: number;
    failed_payments_24h: number;
    webhook_success_rate: number;
  };
  recent_errors: Array<{
    error_code: string;
    count: number;
    last_occurred: string;
  }>;
  active_alerts: number;
  critical_issues: Array<{
    type: string;
    severity: string;
    message: string;
  }>;
}

// Subscription debugging interface
interface SubscriptionDebugInfo {
  user_id: string;
  subscription_status: {
    is_subscribed: boolean;
    subscription_status: string;
    expires_at?: string;
    plan_id?: string;
    is_expired?: boolean;
  };
  recent_payments: Array<{
    reference: string;
    amount: number;
    status: string;
    created_at: string;
  }>;
  subscription_history: Array<{
    plan_id: string;
    start_date: string;
    end_date: string;
    is_active: boolean;
  }>;
}

/**
 * Enhanced health check endpoint with comprehensive system status
 */
router.get('/health', async (req: Request, res: Response) => {
  const startTime = Date.now();
  
  try {
    secureLogger.debug('Starting comprehensive health check');

    // Run all health checks in parallel
    const [
      databaseCheck,
      paystackCheck,
      configCheck,
      subscriptionCheck,
      paymentSystemCheck
    ] = await Promise.allSettled([
      checkDatabaseHealth(),
      checkPaystackHealth(),
      checkConfigurationHealth(),
      checkSubscriptionServiceHealth(),
      checkPaymentSystemHealth()
    ]);

    // Process results
    const checks = {
      database: getCheckResult(databaseCheck),
      paystack: getCheckResult(paystackCheck),
      configuration: getCheckResult(configCheck),
      subscription_service: getCheckResult(subscriptionCheck),
      payment_system: getCheckResult(paymentSystemCheck)
    };

    // Determine overall status
    const hasFailures = Object.values(checks).some(check => check.status === 'fail');
    const hasWarnings = Object.values(checks).some(check => check.status === 'warn');
    
    let overallStatus: 'healthy' | 'degraded' | 'unhealthy';
    if (hasFailures) {
      overallStatus = 'unhealthy';
    } else if (hasWarnings) {
      overallStatus = 'degraded';
    } else {
      overallStatus = 'healthy';
    }

    // Get system metrics
    const metrics = {
      memory: process.memoryUsage(),
      cpu_usage: process.cpuUsage ? getCpuUsage() : undefined
    };

    const healthResult: HealthCheckResult = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      checks,
      metrics
    };

    const statusCode = overallStatus === 'healthy' ? 200 : 
                      overallStatus === 'degraded' ? 200 : 503;

    secureLogger.info('Health check completed', {
      status: overallStatus,
      duration_ms: Date.now() - startTime,
      failed_checks: Object.entries(checks)
        .filter(([_, check]) => check.status === 'fail')
        .map(([name, _]) => name)
    });

    res.status(statusCode).json(healthResult);

  } catch (error) {
    secureLogger.error('Health check failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
      duration_ms: Date.now() - startTime
    });

    res.status(500).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Health check system failure',
      uptime: process.uptime()
    });
  }
});

/**
 * Payment system status endpoint with detailed metrics
 */
router.get('/payment-status', async (req: Request, res: Response) => {
  try {
    secureLogger.debug('Checking payment system status');

    // Get payment metrics for the last 24 hours
    const metrics = await paymentEventLogger.getPaymentMetrics(24);
    
    // Get recent errors
    const recentErrors = await paymentEventLogger.getRecentErrors(10);
    const errorSummary = recentErrors.reduce((acc, error) => {
      const existing = acc.find(e => e.error_code === error.error_code);
      if (existing) {
        existing.count += error.occurrence_count;
        if (error.last_occurred > existing.last_occurred) {
          existing.last_occurred = error.last_occurred;
        }
      } else {
        acc.push({
          error_code: error.error_code,
          count: error.occurrence_count,
          last_occurred: error.last_occurred
        });
      }
      return acc;
    }, [] as Array<{ error_code: string; count: number; last_occurred: string }>);

    // Get active alerts
    const activeAlerts = await paymentAlertingSystem.getActiveAlerts(20);
    
    // Check for critical issues
    const criticalIssues = await paymentAlertingSystem.checkCriticalIssues();

    // Determine overall payment system status
    let status: 'operational' | 'degraded' | 'down';
    if (criticalIssues.hasIssues) {
      const hasCritical = criticalIssues.issues.some(issue => issue.severity === 'critical');
      status = hasCritical ? 'down' : 'degraded';
    } else {
      status = 'operational';
    }

    const paymentStatus: PaymentSystemStatus = {
      status,
      timestamp: new Date().toISOString(),
      metrics: {
        success_rate_24h: metrics.success_rate,
        average_processing_time: metrics.average_processing_time,
        total_payments_24h: metrics.total_payments,
        failed_payments_24h: metrics.failed_payments,
        webhook_success_rate: metrics.webhook_events > 0 ? 
          1 - (metrics.webhook_failures / metrics.webhook_events) : 1
      },
      recent_errors: errorSummary,
      active_alerts: activeAlerts.length,
      critical_issues: criticalIssues.issues
    };

    const statusCode = status === 'operational' ? 200 : 
                      status === 'degraded' ? 200 : 503;

    res.status(statusCode).json(paymentStatus);

  } catch (error) {
    secureLogger.error('Payment status check failed', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      status: 'down',
      timestamp: new Date().toISOString(),
      error: 'Payment status check failed'
    });
  }
});

/**
 * Subscription debugging endpoint
 */
router.get('/debug/subscription/:userId', async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;

    if (!userId) {
      return res.status(400).json({
        error: 'User ID is required'
      });
    }

    secureLogger.debug('Getting subscription debug info', { userId });

    // Get subscription status
    const { getSubscriptionStatus } = await import('../services/subscription.js');
    const statusResult = await getSubscriptionStatus(userId);

    if (!statusResult.success) {
      return res.status(404).json({
        error: 'Failed to get subscription status',
        details: statusResult.error
      });
    }

    // Get recent payment transactions
    const { data: recentPayments, error: paymentsError } = await supabase
      .from('payment_transactions')
      .select('reference, amount, status, created_at')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(10);

    if (paymentsError) {
      secureLogger.warn('Failed to fetch recent payments', { 
        userId, 
        error: paymentsError.message 
      });
    }

    // Get subscription history
    const { data: subscriptionHistory, error: historyError } = await supabase
      .from('subscriptions')
      .select('plan_id, start_date, end_date, is_active')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(5);

    if (historyError) {
      secureLogger.warn('Failed to fetch subscription history', { 
        userId, 
        error: historyError.message 
      });
    }

    const debugInfo: SubscriptionDebugInfo = {
      user_id: userId,
      subscription_status: statusResult.data || {
        is_subscribed: false,
        subscription_status: 'free'
      },
      recent_payments: recentPayments || [],
      subscription_history: subscriptionHistory || []
    };

    res.json(debugInfo);

  } catch (error) {
    secureLogger.error('Subscription debug failed', {
      userId: req.params.userId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      error: 'Subscription debug failed'
    });
  }
});

/**
 * Configuration validation endpoint (enhanced)
 */
router.get('/config/validate', (req: Request, res: Response) => {
  try {
    const validationResult = environmentValidator.validateEnvironment();
    const paystackConfig = configManager.getPaystackConfig();
    const supabaseConfig = configManager.getSupabaseConfig();

    // Additional configuration checks
    const additionalChecks = {
      paystack_keys_present: !!(paystackConfig.publicKey && paystackConfig.secretKey),
      webhook_secret_configured: !!paystackConfig.webhookSecret,
      supabase_configured: !!(supabaseConfig.url && supabaseConfig.serviceRoleKey),
      cors_configured: !!process.env.CORS_ORIGIN,
      rate_limiting_configured: !!process.env.RATE_LIMIT_WINDOW_MS
    };

    const allValid = validationResult.isValid && Object.values(additionalChecks).every(Boolean);

    res.status(allValid ? 200 : 400).json({
      isValid: allValid,
      basic_validation: {
        isValid: validationResult.isValid,
        errors: validationResult.errors,
        warnings: validationResult.warnings
      },
      configuration_checks: additionalChecks,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    secureLogger.error('Configuration validation failed', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      isValid: false,
      error: 'Configuration validation failed',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * System metrics endpoint
 */
router.get('/metrics', async (req: Request, res: Response) => {
  try {
    const metrics = {
      system: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        cpu_usage: getCpuUsage(),
        node_version: process.version,
        platform: process.platform,
        arch: process.arch
      },
      payment: await paymentEventLogger.getPaymentMetrics(24),
      alerts: {
        active_count: (await paymentAlertingSystem.getActiveAlerts(100)).length,
        critical_issues: await paymentAlertingSystem.checkCriticalIssues()
      },
      monitoring: monitoringScheduler.getStatus(),
      timestamp: new Date().toISOString()
    };

    res.json(metrics);

  } catch (error) {
    secureLogger.error('Metrics collection failed', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      error: 'Metrics collection failed',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Monitoring status and control endpoint
 */
router.get('/monitoring/status', (req: Request, res: Response) => {
  try {
    const status = monitoringScheduler.getStatus();
    res.json(status);
  } catch (error) {
    secureLogger.error('Failed to get monitoring status', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    res.status(500).json({
      error: 'Failed to get monitoring status'
    });
  }
});

/**
 * Manual monitoring task execution endpoint (for debugging)
 */
router.post('/monitoring/run/:taskName', async (req: Request, res: Response) => {
  try {
    const { taskName } = req.params;
    const validTasks = ['health_check', 'subscription_cleanup', 'database_maintenance', 'metrics_collection'];
    
    if (!validTasks.includes(taskName)) {
      return res.status(400).json({
        error: 'Invalid task name',
        valid_tasks: validTasks
      });
    }

    secureLogger.info(`Manual execution of monitoring task: ${taskName}`);
    await monitoringScheduler.runTask(taskName);
    
    res.json({
      success: true,
      message: `Task ${taskName} executed successfully`,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    secureLogger.error('Manual task execution failed', {
      taskName: req.params.taskName,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      error: 'Task execution failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Helper functions

/**
 * Check database connectivity and performance
 */
async function checkDatabaseHealth(): Promise<HealthCheck> {
  const startTime = Date.now();
  
  try {
    // Test basic connectivity
    const { data, error } = await supabase
      .from('user_profiles')
      .select('count')
      .limit(1);

    if (error) {
      return {
        status: 'fail',
        message: `Database connection failed: ${error.message}`,
        response_time_ms: Date.now() - startTime,
        last_checked: new Date().toISOString()
      };
    }

    const responseTime = Date.now() - startTime;
    
    return {
      status: responseTime > 1000 ? 'warn' : 'pass',
      message: responseTime > 1000 ? 'Database responding slowly' : 'Database connection healthy',
      response_time_ms: responseTime,
      last_checked: new Date().toISOString(),
      details: {
        connection_pool: 'active'
      }
    };

  } catch (error) {
    return {
      status: 'fail',
      message: `Database health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      response_time_ms: Date.now() - startTime,
      last_checked: new Date().toISOString()
    };
  }
}

/**
 * Check Paystack API connectivity
 */
async function checkPaystackHealth(): Promise<HealthCheck> {
  const startTime = Date.now();
  
  try {
    const paystackConfig = configManager.getPaystackConfig();
    
    if (!paystackConfig.secretKey) {
      return {
        status: 'fail',
        message: 'Paystack secret key not configured',
        response_time_ms: Date.now() - startTime,
        last_checked: new Date().toISOString()
      };
    }

    // Test Paystack API connectivity with a simple request
    const response = await axios.get('https://api.paystack.co/bank', {
      headers: {
        Authorization: `Bearer ${paystackConfig.secretKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 5000
    });

    const responseTime = Date.now() - startTime;

    if (response.status === 200) {
      return {
        status: responseTime > 2000 ? 'warn' : 'pass',
        message: responseTime > 2000 ? 'Paystack API responding slowly' : 'Paystack API connection healthy',
        response_time_ms: responseTime,
        last_checked: new Date().toISOString(),
        details: {
          api_status: response.status,
          has_webhook_secret: !!paystackConfig.webhookSecret
        }
      };
    } else {
      return {
        status: 'warn',
        message: `Paystack API returned status ${response.status}`,
        response_time_ms: responseTime,
        last_checked: new Date().toISOString()
      };
    }

  } catch (error) {
    return {
      status: 'fail',
      message: `Paystack API health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      response_time_ms: Date.now() - startTime,
      last_checked: new Date().toISOString()
    };
  }
}

/**
 * Check configuration health
 */
async function checkConfigurationHealth(): Promise<HealthCheck> {
  const startTime = Date.now();
  
  try {
    const validationResult = environmentValidator.validateEnvironment();
    
    return {
      status: validationResult.isValid ? 'pass' : 'fail',
      message: validationResult.isValid ? 'Configuration is valid' : 'Configuration has errors',
      response_time_ms: Date.now() - startTime,
      last_checked: new Date().toISOString(),
      details: {
        errors: validationResult.errors,
        warnings: validationResult.warnings
      }
    };

  } catch (error) {
    return {
      status: 'fail',
      message: `Configuration check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      response_time_ms: Date.now() - startTime,
      last_checked: new Date().toISOString()
    };
  }
}

/**
 * Check subscription service health
 */
async function checkSubscriptionServiceHealth(): Promise<HealthCheck> {
  const startTime = Date.now();
  
  try {
    const healthResult = await subscriptionServiceHealthCheck();
    
    return {
      status: healthResult.healthy ? 'pass' : 'fail',
      message: healthResult.healthy ? 'Subscription service is healthy' : 'Subscription service has issues',
      response_time_ms: Date.now() - startTime,
      last_checked: new Date().toISOString(),
      details: healthResult.checks
    };

  } catch (error) {
    return {
      status: 'fail',
      message: `Subscription service check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      response_time_ms: Date.now() - startTime,
      last_checked: new Date().toISOString()
    };
  }
}

/**
 * Check payment system health
 */
async function checkPaymentSystemHealth(): Promise<HealthCheck> {
  const startTime = Date.now();
  
  try {
    const criticalIssues = await paymentAlertingSystem.checkCriticalIssues();
    const metrics = await paymentEventLogger.getPaymentMetrics(1); // Last hour
    
    let status: 'pass' | 'warn' | 'fail' = 'pass';
    let message = 'Payment system is healthy';
    
    if (criticalIssues.hasIssues) {
      const hasCritical = criticalIssues.issues.some(issue => issue.severity === 'critical');
      status = hasCritical ? 'fail' : 'warn';
      message = hasCritical ? 'Payment system has critical issues' : 'Payment system has warnings';
    }
    
    return {
      status,
      message,
      response_time_ms: Date.now() - startTime,
      last_checked: new Date().toISOString(),
      details: {
        success_rate: metrics.success_rate,
        total_payments_1h: metrics.total_payments,
        critical_issues: criticalIssues.issues.length,
        issues: criticalIssues.issues
      }
    };

  } catch (error) {
    return {
      status: 'fail',
      message: `Payment system check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      response_time_ms: Date.now() - startTime,
      last_checked: new Date().toISOString()
    };
  }
}

/**
 * Extract health check result from Promise.allSettled result
 */
function getCheckResult(result: PromiseSettledResult<HealthCheck>): HealthCheck {
  if (result.status === 'fulfilled') {
    return result.value;
  } else {
    return {
      status: 'fail',
      message: `Health check failed: ${result.reason}`,
      last_checked: new Date().toISOString()
    };
  }
}

/**
 * Get CPU usage percentage (simplified)
 */
function getCpuUsage(): number {
  const usage = process.cpuUsage();
  return Math.round((usage.user + usage.system) / 1000000); // Convert to milliseconds
}

export default router;