import { useEffect, useState } from 'react';
import { useLocation, Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import Navbar from '@/components/Navbar';
import BottomNavigation from '@/components/BottomNavigation';
import { Check, AlertCircle, Clock, RefreshCw, ExternalLink, User, BookOpen } from 'lucide-react';
import { toast } from 'sonner';

// Payment verification states
type VerificationState = 'pending' | 'verifying' | 'verified' | 'failed' | 'timeout';

interface PaymentVerificationResult {
  success: boolean;
  message: string;
  subscriptionActive?: boolean;
  expiresAt?: string;
}

const PaymentSuccess = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [reference, setReference] = useState<string>('');
  const [planName, setPlanName] = useState<string>('');
  const [verificationState, setVerificationState] = useState<VerificationState>('pending');
  const [verificationResult, setVerificationResult] = useState<PaymentVerificationResult | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [timeRemaining, setTimeRemaining] = useState(30);

  // Verify payment with backend
  const verifyPayment = async (paymentReference: string, retryAttempt = 0) => {
    if (retryAttempt === 0) {
      setVerificationState('verifying');
    }

    try {
      const response = await fetch(`/api/payments/verify/${paymentReference}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      if (response.ok && result.success) {
        setVerificationResult({
          success: true,
          message: result.message || 'Payment verified successfully!',
          subscriptionActive: result.subscriptionActive,
          expiresAt: result.expiresAt
        });
        setVerificationState('verified');
        toast.success('Payment verified and subscription activated!');
      } else {
        throw new Error(result.message || 'Payment verification failed');
      }
    } catch (error) {
      console.error('Payment verification error:', error);
      
      // Retry logic for failed verifications
      if (retryAttempt < 3) {
        setTimeout(() => {
          setRetryCount(retryAttempt + 1);
          verifyPayment(paymentReference, retryAttempt + 1);
        }, 2000 * (retryAttempt + 1)); // Exponential backoff
      } else {
        setVerificationResult({
          success: false,
          message: error instanceof Error ? error.message : 'Payment verification failed'
        });
        setVerificationState('failed');
        toast.error('Unable to verify payment. Please contact support if you were charged.');
      }
    }
  };

  // Manual retry verification
  const retryVerification = () => {
    if (reference) {
      setRetryCount(0);
      verifyPayment(reference);
    }
  };

  useEffect(() => {
    // Get query parameters from URL
    const params = new URLSearchParams(location.search);
    const ref = params.get('reference');
    const plan = params.get('plan');

    if (ref) {
      setReference(ref);
      // Start payment verification
      verifyPayment(ref);
    } else {
      // No reference provided, redirect to profile
      toast.error('No payment reference found');
      setTimeout(() => navigate('/profile'), 2000);
    }
    
    if (plan) setPlanName(plan);
  }, [location, navigate]);

  // Countdown timer for verification timeout
  useEffect(() => {
    if (verificationState === 'verifying' && timeRemaining > 0) {
      const timer = setTimeout(() => {
        setTimeRemaining(prev => prev - 1);
      }, 1000);

      return () => clearTimeout(timer);
    } else if (verificationState === 'verifying' && timeRemaining === 0) {
      setVerificationState('timeout');
      setVerificationResult({
        success: false,
        message: 'Payment verification timed out. Please contact support if you were charged.'
      });
    }
  }, [verificationState, timeRemaining]);

  // Render verification status icon and color
  const getVerificationDisplay = () => {
    switch (verificationState) {
      case 'verifying':
        return {
          icon: <RefreshCw className="h-8 w-8 text-blue-500 animate-spin" />,
          bgColor: 'bg-blue-100',
          title: 'Verifying Payment...',
          subtitle: `Please wait while we verify your payment (${timeRemaining}s remaining)`
        };
      case 'verified':
        return {
          icon: <Check className="h-8 w-8 text-green-500" />,
          bgColor: 'bg-green-100',
          title: 'Payment Verified!',
          subtitle: verificationResult?.message || 'Your subscription has been activated successfully.'
        };
      case 'failed':
      case 'timeout':
        return {
          icon: <AlertCircle className="h-8 w-8 text-red-500" />,
          bgColor: 'bg-red-100',
          title: 'Verification Issue',
          subtitle: verificationResult?.message || 'Unable to verify payment automatically.'
        };
      default:
        return {
          icon: <Clock className="h-8 w-8 text-gray-500" />,
          bgColor: 'bg-gray-100',
          title: 'Processing Payment...',
          subtitle: 'Initializing payment verification'
        };
    }
  };

  const display = getVerificationDisplay();

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <main className="flex-1 container mx-auto px-4 py-12">
        <motion.div
          className="max-w-lg mx-auto bg-white rounded-xl shadow-lg overflow-hidden"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="p-8">
            {/* Status Icon */}
            <div className="flex justify-center mb-6">
              <div className={`w-16 h-16 ${display.bgColor} rounded-full flex items-center justify-center`}>
                {display.icon}
              </div>
            </div>

            {/* Title and Subtitle */}
            <h1 className="text-2xl font-bold text-center text-gray-800 mb-4">
              {display.title}
            </h1>

            <p className="text-center text-gray-600 mb-6">
              {display.subtitle}
            </p>

            {/* Plan Information */}
            {planName && (
              <div className="bg-indigo-50 p-4 rounded-lg mb-6 border border-indigo-200">
                <div className="flex items-center justify-center space-x-2">
                  <BookOpen className="h-5 w-5 text-indigo-600" />
                  <p className="text-indigo-800 font-medium">
                    Subscribed to: {planName}
                  </p>
                </div>
                {verificationResult?.expiresAt && (
                  <p className="text-sm text-indigo-600 text-center mt-2">
                    Valid until: {new Date(verificationResult.expiresAt).toLocaleDateString()}
                  </p>
                )}
              </div>
            )}

            {/* Transaction Reference */}
            {reference && (
              <div className="bg-gray-50 p-4 rounded-lg mb-6">
                <p className="text-sm text-gray-500 mb-1">Transaction Reference:</p>
                <p className="font-mono text-gray-700 text-sm break-all">{reference}</p>
                {retryCount > 0 && (
                  <p className="text-xs text-gray-500 mt-2">
                    Verification attempts: {retryCount + 1}
                  </p>
                )}
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col space-y-3">
              {verificationState === 'verified' && (
                <>
                  <Link
                    to="/quizzes"
                    className="py-3 px-4 bg-indigo-600 hover:bg-indigo-700 text-white text-center font-medium rounded-lg transition-colors flex items-center justify-center space-x-2"
                  >
                    <BookOpen className="h-5 w-5" />
                    <span>Start Exploring Quizzes</span>
                  </Link>

                  <Link
                    to="/profile"
                    className="py-3 px-4 bg-gray-100 hover:bg-gray-200 text-gray-800 text-center font-medium rounded-lg transition-colors flex items-center justify-center space-x-2"
                  >
                    <User className="h-5 w-5" />
                    <span>View My Profile</span>
                  </Link>
                </>
              )}

              {(verificationState === 'failed' || verificationState === 'timeout') && (
                <>
                  <button
                    onClick={retryVerification}
                    className="py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white text-center font-medium rounded-lg transition-colors flex items-center justify-center space-x-2"
                  >
                    <RefreshCw className="h-5 w-5" />
                    <span>Retry Verification</span>
                  </button>

                  <Link
                    to="/payment/troubleshoot"
                    className="py-3 px-4 bg-amber-100 hover:bg-amber-200 text-amber-800 text-center font-medium rounded-lg transition-colors flex items-center justify-center space-x-2"
                  >
                    <AlertCircle className="h-5 w-5" />
                    <span>Get Help</span>
                  </Link>
                </>
              )}

              {verificationState === 'verifying' && (
                <div className="py-3 px-4 bg-blue-50 text-blue-800 text-center font-medium rounded-lg border border-blue-200">
                  <div className="flex items-center justify-center space-x-2">
                    <RefreshCw className="h-5 w-5 animate-spin" />
                    <span>Verifying payment...</span>
                  </div>
                  <p className="text-sm mt-1">Please do not close this page</p>
                </div>
              )}
            </div>

            {/* Help Section */}
            <div className="mt-6 p-4 bg-amber-50 rounded-lg border border-amber-200 flex items-start space-x-3">
              <AlertCircle className="h-5 w-5 text-amber-500 flex-shrink-0 mt-0.5" />
              <div className="text-sm text-amber-800">
                <p className="font-medium mb-1">Need assistance?</p>
                <div className="space-y-1">
                  <Link to="/payment/troubleshoot" className="block underline hover:no-underline">
                    View troubleshooting guide
                  </Link>
                  <a 
                    href="mailto:<EMAIL>" 
                    className="block underline hover:no-underline flex items-center space-x-1"
                  >
                    <span>Contact support</span>
                    <ExternalLink className="h-3 w-3" />
                  </a>
                </div>
              </div>
            </div>

            {/* Success Tips */}
            {verificationState === 'verified' && (
              <div className="mt-6 p-4 bg-green-50 rounded-lg border border-green-200">
                <h3 className="font-medium text-green-800 mb-2">What's next?</h3>
                <ul className="text-sm text-green-700 space-y-1">
                  <li>• Access all premium quiz content</li>
                  <li>• Track your progress and performance</li>
                  <li>• Get detailed explanations for answers</li>
                  <li>• Download certificates upon completion</li>
                </ul>
              </div>
            )}
          </div>
        </motion.div>
      </main>

      <BottomNavigation />
    </div>
  );
};

export default PaymentSuccess;
