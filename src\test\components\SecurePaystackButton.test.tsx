import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import SecurePaystackButton from '@/components/SecurePaystackButton';
import * as useAuthModule from '@/hooks/use-auth';
import * as paystackUtils from '@/utils/paystack';
import { usePaystackPayment } from 'react-paystack';

// Mock the hooks and modules
vi.mock('@/hooks/use-auth', async () => {
  const actual = await vi.importActual('@/hooks/use-auth');
  return {
    ...actual,
    useAuth: vi.fn(),
  };
});

vi.mock('react-paystack', () => ({
  usePaystackPayment: vi.fn(),
}));

vi.mock('framer-motion', () => ({
  motion: {
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}));

vi.mock('sonner', () => ({
  toast: {
    error: vi.fn(),
    success: vi.fn(),
    info: vi.fn(),
  },
}));

vi.mock('@/utils/paystack', async () => {
  const actual = await vi.importActual('@/utils/paystack');
  return {
    ...actual,
    generateReference: vi.fn().mockReturnValue('secure_ref_123'),
    toKobo: vi.fn().mockImplementation((amount) => amount * 100),
    handlePaymentSuccess: vi.fn(),
  };
});

// Mock environment variables
Object.defineProperty(import.meta, 'env', {
  value: {
    VITE_PAYSTACK_PUBLIC_KEY: 'pk_test_secure_123456789',
  },
  writable: true,
});

describe('SecurePaystackButton component', () => {
  const mockPlan = {
    id: 'pro',
    name: 'Pro',
    amount: 1979,
    interval: 'weekly' as const,
    features: ['All features', 'Priority support'],
  };

  const mockUser = {
    id: '456',
    email: '<EMAIL>',
    name: 'Secure User'
  };

  const mockInitializePayment = vi.fn();
  const mockHandlePaymentSuccess = vi.fn();
  const mockOnPaymentStart = vi.fn();
  const mockOnPaymentSuccess = vi.fn();
  const mockOnPaymentError = vi.fn();

  // Mock window.location.href
  const mockLocationHref = vi.fn();
  Object.defineProperty(window, 'location', {
    value: {
      href: '',
      assign: mockLocationHref,
    },
    writable: true,
  });

  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks();

    // Default mock implementations
    vi.mocked(useAuthModule.useAuth).mockReturnValue({
      user: mockUser as any,
      session: {} as any,
      isLoading: false,
      signIn: vi.fn(),
      signUp: vi.fn(),
      signOut: vi.fn(),
    });

    vi.mocked(usePaystackPayment).mockReturnValue(mockInitializePayment);
    vi.mocked(paystackUtils.handlePaymentSuccess).mockImplementation(mockHandlePaymentSuccess);

    // Clear any existing timers
    vi.clearAllTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('Rendering and Basic Functionality', () => {
    it('renders with default button text', () => {
      render(<SecurePaystackButton plan={mockPlan} />);
      expect(screen.getByText('Select')).toBeInTheDocument();
    });

    it('renders with custom button text', () => {
      render(<SecurePaystackButton plan={mockPlan} buttonText="Subscribe Now" />);
      expect(screen.getByText('Subscribe Now')).toBeInTheDocument();
    });

    it('applies custom className when provided', () => {
      render(<SecurePaystackButton plan={mockPlan} className="secure-class" />);
      const button = screen.getByRole('button');
      expect(button).toHaveClass('secure-class');
    });

    it('shows authentication message when user is not logged in', () => {
      vi.mocked(useAuthModule.useAuth).mockReturnValue({
        user: null,
        session: null,
        isLoading: false,
        signIn: vi.fn(),
        signUp: vi.fn(),
        signOut: vi.fn(),
      });

      render(<SecurePaystackButton plan={mockPlan} />);
      expect(screen.getByText(/Please log in to subscribe/)).toBeInTheDocument();
    });

    it('disables button when user is not authenticated', () => {
      vi.mocked(useAuthModule.useAuth).mockReturnValue({
        user: null,
        session: null,
        isLoading: false,
        signIn: vi.fn(),
        signUp: vi.fn(),
        signOut: vi.fn(),
      });

      render(<SecurePaystackButton plan={mockPlan} />);
      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
    });
  });

  describe('Configuration Validation', () => {
    it('validates Paystack public key configuration', () => {
      // Mock missing public key
      Object.defineProperty(import.meta, 'env', {
        value: {
          VITE_PAYSTACK_PUBLIC_KEY: undefined,
        },
        writable: true,
      });

      render(
        <SecurePaystackButton 
          plan={mockPlan} 
          onPaymentError={mockOnPaymentError}
        />
      );
      
      const button = screen.getByText('Select');
      fireEvent.click(button);

      expect(mockOnPaymentError).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'CONFIGURATION_ERROR',
          message: 'Payment service not configured',
          retryable: true
        })
      );

      // Restore for other tests
      Object.defineProperty(import.meta, 'env', {
        value: {
          VITE_PAYSTACK_PUBLIC_KEY: 'pk_test_secure_123456789',
        },
        writable: true,
      });
    });

    it('configures Paystack with validated parameters', () => {
      render(<SecurePaystackButton plan={mockPlan} />);
      
      const button = screen.getByText('Select');
      fireEvent.click(button);

      expect(usePaystackPayment).toHaveBeenCalledWith({
        reference: 'secure_ref_123',
        email: '<EMAIL>',
        amount: 197900, // 1979 * 100 (toKobo)
        publicKey: 'pk_test_secure_123456789',
        currency: 'NGN',
        metadata: {
          custom_fields: [
            {
              display_name: 'Plan',
              variable_name: 'plan',
              value: 'pro',
            },
          ],
        },
      });
    });
  });

  describe('Payment Verification Options', () => {
    it('performs payment verification by default', async () => {
      mockHandlePaymentSuccess.mockResolvedValue({
        success: true,
        message: 'Payment verified successfully',
        reference: 'secure_ref_123'
      });

      render(<SecurePaystackButton plan={mockPlan} />);
      
      const button = screen.getByText('Select');
      fireEvent.click(button);

      // Simulate successful payment callback
      const paymentResponse = { reference: 'secure_ref_123' };
      const successCallback = mockInitializePayment.mock.calls[0][0];
      
      await act(async () => {
        await successCallback(paymentResponse);
      });

      expect(mockHandlePaymentSuccess).toHaveBeenCalledWith(
        'secure_ref_123',
        'pro',
        '<EMAIL>'
      );
    });

    it('skips verification when enableVerification is false', async () => {
      vi.useFakeTimers();

      render(
        <SecurePaystackButton 
          plan={mockPlan} 
          enableVerification={false}
          onPaymentSuccess={mockOnPaymentSuccess}
        />
      );
      
      const button = screen.getByText('Select');
      fireEvent.click(button);

      // Simulate successful payment callback
      const paymentResponse = { reference: 'secure_ref_123' };
      const successCallback = mockInitializePayment.mock.calls[0][0];
      
      await act(async () => {
        await successCallback(paymentResponse);
      });

      // Should not call verification
      expect(mockHandlePaymentSuccess).not.toHaveBeenCalled();
      
      // Should call success callback directly
      expect(mockOnPaymentSuccess).toHaveBeenCalledWith('secure_ref_123');

      // Should show success state
      await waitFor(() => {
        expect(screen.getByText('Payment Successful!')).toBeInTheDocument();
      });

      // Should redirect after delay
      act(() => {
        vi.advanceTimersByTime(1500);
      });

      expect(window.location.href).toBe('/payment/success?reference=secure_ref_123&plan=Pro');
    });
  });

  describe('Enhanced Error Handling', () => {
    it('handles configuration errors with detailed messages', async () => {
      // Mock missing public key
      Object.defineProperty(import.meta, 'env', {
        value: {
          VITE_PAYSTACK_PUBLIC_KEY: '',
        },
        writable: true,
      });

      render(<SecurePaystackButton plan={mockPlan} />);
      
      const button = screen.getByText('Select');
      fireEvent.click(button);

      await waitFor(() => {
        expect(screen.getByText(/Payment service not configured/)).toBeInTheDocument();
      });

      // Restore for other tests
      Object.defineProperty(import.meta, 'env', {
        value: {
          VITE_PAYSTACK_PUBLIC_KEY: 'pk_test_secure_123456789',
        },
        writable: true,
      });
    });

    it('provides detailed error information in UI', async () => {
      const mockError = new Error('Network connection failed');
      vi.mocked(usePaystackPayment).mockImplementation(() => {
        throw mockError;
      });

      render(<SecurePaystackButton plan={mockPlan} />);
      
      const button = screen.getByText('Select');
      fireEvent.click(button);

      await waitFor(() => {
        expect(screen.getByText(/Failed to initialize payment/)).toBeInTheDocument();
        expect(screen.getByText(/Network connection failed/)).toBeInTheDocument();
      });
    });

    it('shows support contact for non-retryable errors', async () => {
      const mockError = { type: 'AUTHENTICATION_REQUIRED', retryable: false };
      vi.mocked(usePaystackPayment).mockImplementation(() => {
        throw mockError;
      });

      render(<SecurePaystackButton plan={mockPlan} />);
      
      const button = screen.getByText('Select');
      fireEvent.click(button);

      await waitFor(() => {
        expect(screen.getByText(/Please contact support/)).toBeInTheDocument();
      });
    });
  });

  describe('Payment States and UI Feedback', () => {
    it('shows comprehensive processing states', async () => {
      render(<SecurePaystackButton plan={mockPlan} />);
      
      const button = screen.getByText('Select');
      fireEvent.click(button);

      // Should show initializing state
      await waitFor(() => {
        expect(screen.getByText('Initializing...')).toBeInTheDocument();
      });

      // Should progress to processing state
      await waitFor(() => {
        expect(screen.getByText('Processing Payment...')).toBeInTheDocument();
      });
    });

    it('shows verification state with user guidance', async () => {
      vi.useFakeTimers();
      
      // Mock a slow verification
      mockHandlePaymentSuccess.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({
          success: true,
          message: 'Verified'
        }), 5000))
      );

      render(<SecurePaystackButton plan={mockPlan} />);
      
      const button = screen.getByText('Select');
      fireEvent.click(button);

      // Simulate successful payment callback
      const paymentResponse = { reference: 'secure_ref_123' };
      const successCallback = mockInitializePayment.mock.calls[0][0];
      
      act(() => {
        successCallback(paymentResponse);
      });

      await waitFor(() => {
        expect(screen.getByText('Verifying Payment...')).toBeInTheDocument();
        expect(screen.getByText(/Verifying your payment with our secure servers/)).toBeInTheDocument();
        expect(screen.getByText(/Please do not close this page/)).toBeInTheDocument();
      });
    });

    it('handles verification timeout with appropriate messaging', async () => {
      vi.useFakeTimers();
      
      // Mock a hanging verification
      mockHandlePaymentSuccess.mockImplementation(() => 
        new Promise(() => {}) // Never resolves
      );

      render(<SecurePaystackButton plan={mockPlan} />);
      
      const button = screen.getByText('Select');
      fireEvent.click(button);

      // Simulate successful payment callback
      const paymentResponse = { reference: 'secure_ref_123' };
      const successCallback = mockInitializePayment.mock.calls[0][0];
      
      act(() => {
        successCallback(paymentResponse);
      });

      // Fast-forward time to trigger timeout
      act(() => {
        vi.advanceTimersByTime(30000);
      });

      await waitFor(() => {
        expect(screen.getByText(/Payment verification timed out/)).toBeInTheDocument();
      });
    });
  });

  describe('Success Flow and Redirects', () => {
    it('redirects to success page after successful verification', async () => {
      vi.useFakeTimers();
      
      mockHandlePaymentSuccess.mockResolvedValue({
        success: true,
        message: 'Payment successful',
        reference: 'secure_ref_123'
      });

      render(<SecurePaystackButton plan={mockPlan} />);
      
      const button = screen.getByText('Select');
      fireEvent.click(button);

      // Simulate successful payment callback
      const paymentResponse = { reference: 'secure_ref_123' };
      const successCallback = mockInitializePayment.mock.calls[0][0];
      
      await act(async () => {
        await successCallback(paymentResponse);
      });

      await waitFor(() => {
        expect(screen.getByText('Payment Successful!')).toBeInTheDocument();
      });

      // Should redirect after delay
      act(() => {
        vi.advanceTimersByTime(1500);
      });

      expect(window.location.href).toBe('/payment/success?reference=secure_ref_123&plan=Pro');
    });

    it('includes plan name in redirect URL', async () => {
      vi.useFakeTimers();
      
      const elitePlan = {
        id: 'elite',
        name: 'Elite Premium',
        amount: 5000,
        interval: 'one-time' as const,
        features: ['All features']
      };

      mockHandlePaymentSuccess.mockResolvedValue({
        success: true,
        message: 'Payment successful',
        reference: 'secure_ref_123'
      });

      render(<SecurePaystackButton plan={elitePlan} />);
      
      const button = screen.getByText('Select');
      fireEvent.click(button);

      // Simulate successful payment callback
      const paymentResponse = { reference: 'secure_ref_123' };
      const successCallback = mockInitializePayment.mock.calls[0][0];
      
      await act(async () => {
        await successCallback(paymentResponse);
      });

      // Should redirect with correct plan name
      act(() => {
        vi.advanceTimersByTime(1500);
      });

      expect(window.location.href).toBe('/payment/success?reference=secure_ref_123&plan=Elite Premium');
    });
  });

  describe('Retry Mechanism', () => {
    it('implements retry logic with exponential backoff', async () => {
      const mockError = new Error('Network error');
      vi.mocked(usePaystackPayment).mockImplementation(() => {
        throw mockError;
      });

      render(<SecurePaystackButton plan={mockPlan} maxRetries={2} />);
      
      const button = screen.getByText('Select');
      
      // First attempt
      fireEvent.click(button);
      await waitFor(() => screen.getByText('Retry Payment'));
      
      // Should show retry count
      expect(screen.getByText(/Retry attempt 1 of 2/)).toBeInTheDocument();
      
      // Retry
      fireEvent.click(screen.getByText('Retry Payment'));
      await waitFor(() => screen.getByText('Retry Payment'));
      
      expect(screen.getByText(/Retry attempt 2 of 2/)).toBeInTheDocument();
    });

    it('stops retrying after max attempts', async () => {
      const mockError = new Error('Persistent error');
      vi.mocked(usePaystackPayment).mockImplementation(() => {
        throw mockError;
      });

      render(<SecurePaystackButton plan={mockPlan} maxRetries={1} />);
      
      const button = screen.getByText('Select');
      
      // First attempt
      fireEvent.click(button);
      await waitFor(() => screen.getByText('Retry Payment'));
      
      // Retry (should be last attempt)
      fireEvent.click(screen.getByText('Retry Payment'));
      await waitFor(() => screen.getByText('Payment Failed'));
      
      // Should not show retry option anymore
      expect(screen.queryByText('Retry Payment')).not.toBeInTheDocument();
    });
  });

  describe('Accessibility and UX Enhancements', () => {
    it('provides loading indicators during all processing states', async () => {
      render(<SecurePaystackButton plan={mockPlan} />);
      
      const button = screen.getByText('Select');
      fireEvent.click(button);

      await waitFor(() => {
        const spinner = screen.getByRole('button').querySelector('.animate-spin');
        expect(spinner).toBeInTheDocument();
      });
    });

    it('maintains button accessibility during state changes', async () => {
      render(<SecurePaystackButton plan={mockPlan} />);
      
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('type', 'button');
      
      fireEvent.click(button);
      
      await waitFor(() => {
        expect(button).toBeDisabled();
        expect(button).toHaveAttribute('type', 'button');
      });
    });

    it('provides clear visual feedback for different states', async () => {
      render(<SecurePaystackButton plan={mockPlan} />);
      
      const button = screen.getByText('Select');
      fireEvent.click(button);

      // Should have visual loading state
      await waitFor(() => {
        const buttonElement = screen.getByRole('button');
        expect(buttonElement).toHaveStyle({ opacity: '0.6' });
      });
    });
  });

  describe('Edge Cases and Error Recovery', () => {
    it('handles component unmount during payment processing', () => {
      const { unmount } = render(<SecurePaystackButton plan={mockPlan} />);
      
      const button = screen.getByText('Select');
      fireEvent.click(button);
      
      // Unmount during processing
      unmount();
      
      // Should not cause errors or memory leaks
      expect(() => {
        vi.runAllTimers();
      }).not.toThrow();
    });

    it('handles rapid successive clicks gracefully', async () => {
      render(<SecurePaystackButton plan={mockPlan} />);
      
      const button = screen.getByText('Select');
      
      // Click multiple times rapidly
      fireEvent.click(button);
      fireEvent.click(button);
      fireEvent.click(button);

      // Should only initialize payment once
      expect(mockInitializePayment).toHaveBeenCalledTimes(1);
    });

    it('recovers from temporary network issues', async () => {
      let callCount = 0;
      mockHandlePaymentSuccess.mockImplementation(() => {
        callCount++;
        if (callCount === 1) {
          throw new Error('Network error');
        }
        return Promise.resolve({
          success: true,
          message: 'Payment successful'
        });
      });

      render(<SecurePaystackButton plan={mockPlan} />);
      
      const button = screen.getByText('Select');
      fireEvent.click(button);

      // Should show retry option
      await waitFor(() => screen.getByText('Retry Payment'));
      
      // Retry should succeed
      fireEvent.click(screen.getByText('Retry Payment'));
      
      await waitFor(() => {
        expect(screen.getByText('Payment Successful!')).toBeInTheDocument();
      });
    });
  });
});