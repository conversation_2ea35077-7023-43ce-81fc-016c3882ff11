-- Production Database Setup Script
-- Run this script to set up the production database with all necessary tables, functions, and constraints

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create subscriptions table if not exists
CREATE TABLE IF NOT EXISTS subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    plan_id VARCHAR(50) NOT NULL,
    amount_paid INTEGER NOT NULL,
    start_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    end_date TIMESTAMP WITH TIME ZONE NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    last_payment_reference VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create payment_transactions table if not exists
CREATE TABLE IF NOT EXISTS payment_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    reference VARCHAR(255) UNIQUE NOT NULL,
    amount INTEGER NOT NULL,
    plan_id VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    payment_method VARCHAR(50) NOT NULL DEFAULT 'paystack',
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_profiles table if not exists (assuming it exists, add subscription fields)
DO $$
BEGIN
    -- Add subscription fields to user_profiles if they don't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'is_subscribed') THEN
        ALTER TABLE user_profiles ADD COLUMN is_subscribed BOOLEAN DEFAULT false;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'subscription_expires_at') THEN
        ALTER TABLE user_profiles ADD COLUMN subscription_expires_at TIMESTAMP WITH TIME ZONE;
    END IF;
END $$;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_active ON subscriptions(is_active);
CREATE INDEX IF NOT EXISTS idx_subscriptions_end_date ON subscriptions(end_date);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_user_id ON payment_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_reference ON payment_transactions(reference);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_status ON payment_transactions(status);
CREATE INDEX IF NOT EXISTS idx_user_profiles_subscribed ON user_profiles(is_subscribed);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
DROP TRIGGER IF EXISTS update_subscriptions_updated_at ON subscriptions;
CREATE TRIGGER update_subscriptions_updated_at
    BEFORE UPDATE ON subscriptions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_payment_transactions_updated_at ON payment_transactions;
CREATE TRIGGER update_payment_transactions_updated_at
    BEFORE UPDATE ON payment_transactions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to handle payment success atomically
CREATE OR REPLACE FUNCTION handle_payment_success(
    p_user_id UUID,
    p_plan_id VARCHAR(50),
    p_amount INTEGER,
    p_reference VARCHAR(255),
    p_duration_days INTEGER DEFAULT 7
)
RETURNS JSONB AS $$
DECLARE
    v_subscription_id UUID;
    v_end_date TIMESTAMP WITH TIME ZONE;
    v_result JSONB;
BEGIN
    -- Calculate end date
    v_end_date := NOW() + (p_duration_days || ' days')::INTERVAL;
    
    -- Start transaction
    BEGIN
        -- Update payment transaction status
        UPDATE payment_transactions 
        SET status = 'success', updated_at = NOW()
        WHERE reference = p_reference AND user_id = p_user_id;
        
        -- Insert or update subscription
        INSERT INTO subscriptions (user_id, plan_id, amount_paid, start_date, end_date, is_active, last_payment_reference)
        VALUES (p_user_id, p_plan_id, p_amount, NOW(), v_end_date, true, p_reference)
        ON CONFLICT (user_id) 
        DO UPDATE SET 
            plan_id = EXCLUDED.plan_id,
            amount_paid = EXCLUDED.amount_paid,
            start_date = EXCLUDED.start_date,
            end_date = EXCLUDED.end_date,
            is_active = true,
            last_payment_reference = EXCLUDED.last_payment_reference,
            updated_at = NOW()
        RETURNING id INTO v_subscription_id;
        
        -- Update user profile
        UPDATE user_profiles 
        SET 
            is_subscribed = true,
            subscription_expires_at = v_end_date,
            updated_at = NOW()
        WHERE user_id = p_user_id;
        
        -- Return success result
        v_result := jsonb_build_object(
            'success', true,
            'subscription_id', v_subscription_id,
            'end_date', v_end_date,
            'message', 'Payment processed successfully'
        );
        
        RETURN v_result;
        
    EXCEPTION WHEN OTHERS THEN
        -- Return error result
        v_result := jsonb_build_object(
            'success', false,
            'error', SQLERRM,
            'message', 'Payment processing failed'
        );
        
        RETURN v_result;
    END;
END;
$$ LANGUAGE plpgsql;

-- Create function to check subscription status
CREATE OR REPLACE FUNCTION check_subscription_status(p_user_id UUID)
RETURNS JSONB AS $$
DECLARE
    v_subscription RECORD;
    v_result JSONB;
BEGIN
    SELECT 
        s.id,
        s.plan_id,
        s.is_active,
        s.end_date,
        s.start_date,
        up.is_subscribed
    INTO v_subscription
    FROM subscriptions s
    JOIN user_profiles up ON s.user_id = up.user_id
    WHERE s.user_id = p_user_id
    ORDER BY s.created_at DESC
    LIMIT 1;
    
    IF v_subscription IS NULL THEN
        v_result := jsonb_build_object(
            'is_subscribed', false,
            'status', 'no_subscription',
            'message', 'No subscription found'
        );
    ELSIF v_subscription.end_date < NOW() THEN
        v_result := jsonb_build_object(
            'is_subscribed', false,
            'status', 'expired',
            'expired_at', v_subscription.end_date,
            'plan_id', v_subscription.plan_id,
            'message', 'Subscription has expired'
        );
    ELSIF v_subscription.is_active THEN
        v_result := jsonb_build_object(
            'is_subscribed', true,
            'status', 'active',
            'expires_at', v_subscription.end_date,
            'plan_id', v_subscription.plan_id,
            'days_remaining', EXTRACT(DAY FROM (v_subscription.end_date - NOW())),
            'message', 'Subscription is active'
        );
    ELSE
        v_result := jsonb_build_object(
            'is_subscribed', false,
            'status', 'inactive',
            'plan_id', v_subscription.plan_id,
            'message', 'Subscription is inactive'
        );
    END IF;
    
    RETURN v_result;
END;
$$ LANGUAGE plpgsql;

-- Create function to cleanup expired subscriptions
CREATE OR REPLACE FUNCTION cleanup_expired_subscriptions()
RETURNS INTEGER AS $$
DECLARE
    v_updated_count INTEGER;
BEGIN
    -- Update expired subscriptions
    UPDATE subscriptions 
    SET is_active = false, updated_at = NOW()
    WHERE end_date < NOW() AND is_active = true;
    
    GET DIAGNOSTICS v_updated_count = ROW_COUNT;
    
    -- Update corresponding user profiles
    UPDATE user_profiles 
    SET 
        is_subscribed = false,
        subscription_expires_at = NULL,
        updated_at = NOW()
    WHERE user_id IN (
        SELECT user_id 
        FROM subscriptions 
        WHERE end_date < NOW() AND is_active = false
    ) AND is_subscribed = true;
    
    RETURN v_updated_count;
END;
$$ LANGUAGE plpgsql;

-- Create constraints for data integrity
ALTER TABLE subscriptions 
ADD CONSTRAINT chk_subscriptions_amount_positive 
CHECK (amount_paid > 0);

ALTER TABLE subscriptions 
ADD CONSTRAINT chk_subscriptions_end_after_start 
CHECK (end_date > start_date);

ALTER TABLE payment_transactions 
ADD CONSTRAINT chk_payment_amount_positive 
CHECK (amount > 0);

ALTER TABLE payment_transactions 
ADD CONSTRAINT chk_payment_status_valid 
CHECK (status IN ('pending', 'success', 'failed', 'cancelled'));

-- Create RLS (Row Level Security) policies if needed
-- Note: Uncomment and modify these if using RLS
/*
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_transactions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own subscriptions" ON subscriptions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can view own payment transactions" ON payment_transactions
    FOR SELECT USING (auth.uid() = user_id);
*/

-- Grant necessary permissions (adjust role names as needed)
-- GRANT SELECT, INSERT, UPDATE ON subscriptions TO authenticated;
-- GRANT SELECT, INSERT, UPDATE ON payment_transactions TO authenticated;
-- GRANT EXECUTE ON FUNCTION handle_payment_success TO authenticated;
-- GRANT EXECUTE ON FUNCTION check_subscription_status TO authenticated;

COMMIT;