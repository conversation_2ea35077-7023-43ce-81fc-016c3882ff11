import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

export interface ServerConfig {
  port: number;
  nodeEnv: string;
  supabase: {
    url: string;
    serviceRoleKey: string;
  };
  paystack: {
    secretKey: string;
    webhookSecret?: string;
  };
  api: {
    url: string;
    appUrl: string;
    timeout: number;
  };
}

export interface ClientConfig {
  supabase: {
    url: string;
    anonKey: string;
  };
  paystack: {
    publicKey: string;
  };
  app: {
    name: string;
    description: string;
    url: string;
  };
  api: {
    url: string;
    timeout: number;
    maxUploadSize: number;
  };
  features: {
    adminFeatures: boolean;
    analytics: boolean;
    debugMode: boolean;
  };
  auth: {
    redirectUrl: string;
  };
}

class ConfigManager {
  private static instance: ConfigManager;
  private serverConfig: ServerConfig;
  private clientConfig: ClientConfig;

  private constructor() {
    this.serverConfig = this.loadServerConfig();
    this.clientConfig = this.loadClientConfig();
  }

  public static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  private loadServerConfig(): ServerConfig {
    return {
      port: parseInt(process.env.PORT || '5000', 10),
      nodeEnv: process.env.NODE_ENV || 'development',
      supabase: {
        url: process.env.NEXT_PUBLIC_SUPABASE_URL || '',
        serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY || '',
      },
      paystack: {
        secretKey: process.env.PAYSTACK_SECRET_KEY || '',
        webhookSecret: process.env.PAYSTACK_WEBHOOK_SECRET,
      },
      api: {
        url: process.env.VITE_API_URL || 'http://localhost:5000',
        appUrl: process.env.VITE_APP_URL || 'http://localhost:5173',
        timeout: parseInt(process.env.VITE_API_TIMEOUT || '30000', 10),
      },
    };
  }

  private loadClientConfig(): ClientConfig {
    return {
      supabase: {
        url: process.env.VITE_SUPABASE_URL || '',
        anonKey: process.env.VITE_SUPABASE_ANON_KEY || '',
      },
      paystack: {
        publicKey: process.env.VITE_PAYSTACK_PUBLIC_KEY || '',
      },
      app: {
        name: process.env.VITE_APP_NAME || 'SecQuiz',
        description: process.env.VITE_APP_DESCRIPTION || 'A cybersecurity education platform',
        url: process.env.VITE_APP_URL || 'http://localhost:5173',
      },
      api: {
        url: process.env.VITE_API_URL || 'http://localhost:5000',
        timeout: parseInt(process.env.VITE_API_TIMEOUT || '30000', 10),
        maxUploadSize: parseInt(process.env.VITE_MAX_UPLOAD_SIZE || '5242880', 10),
      },
      features: {
        adminFeatures: process.env.VITE_ENABLE_ADMIN_FEATURES === 'true',
        analytics: process.env.VITE_ENABLE_ANALYTICS === 'true',
        debugMode: process.env.VITE_ENABLE_DEBUG_MODE === 'true',
      },
      auth: {
        redirectUrl: process.env.VITE_AUTH_REDIRECT_URL || 'http://localhost:5173/auth/verify',
      },
    };
  }

  public getServerConfig(): ServerConfig {
    return this.serverConfig;
  }

  public getClientConfig(): ClientConfig {
    return this.clientConfig;
  }

  public getPaystackConfig() {
    return {
      secretKey: this.serverConfig.paystack.secretKey,
      publicKey: this.clientConfig.paystack.publicKey,
      webhookSecret: this.serverConfig.paystack.webhookSecret,
    };
  }

  public getSupabaseConfig() {
    return {
      url: this.serverConfig.supabase.url,
      serviceRoleKey: this.serverConfig.supabase.serviceRoleKey,
      anonKey: this.clientConfig.supabase.anonKey,
    };
  }
}

export const configManager = ConfigManager.getInstance();
export const serverConfig = configManager.getServerConfig();
export const clientConfig = configManager.getClientConfig();