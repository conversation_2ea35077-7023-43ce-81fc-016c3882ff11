-- Fix the get_all_user_profiles function to resolve ambiguous column reference
-- Drop the existing function first
DROP FUNCTION IF EXISTS public.get_all_user_profiles() CASCADE;

-- Create the corrected function
CREATE OR REPLACE FUNCTION public.get_all_user_profiles()
RETURNS TABLE (
  id UUID,
  user_id UUID,
  email TEXT,
  full_name TEXT,
  is_subscribed BOOLEAN,
  is_admin BOOLEAN,
  subscription_expires_at TIMESTAMPTZ,
  subscription_status TEXT,
  subscription_plan TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  last_sign_in_at TIMESTAMPTZ
) AS $function$
BEGIN
  -- Check if current user is admin
  IF NOT EXISTS (
    SELECT 1 FROM public.user_profiles up_check
    WHERE up_check.user_id = auth.uid() AND up_check.is_admin = true
  ) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;

  RETURN QUERY
  SELECT
    up.id,
    up.user_id,
    up.email,
    up.full_name,
    up.is_subscribed,
    up.is_admin,
    up.subscription_expires_at,
    up.subscription_status,
    up.subscription_plan,
    up.created_at,
    up.updated_at,
    au.last_sign_in_at
  FROM public.user_profiles up
  LEFT JOIN auth.users au ON up.user_id = au.id
  ORDER BY up.created_at DESC;
END;
$function$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.get_all_user_profiles() TO authenticated;

-- Test the function by selecting its definition
SELECT 
  proname as function_name,
  prosrc as function_body
FROM pg_proc 
WHERE proname = 'get_all_user_profiles';