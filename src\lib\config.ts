export interface ClientConfig {
  supabase: {
    url: string;
    anonKey: string;
  };
  paystack: {
    publicKey: string;
  };
  app: {
    name: string;
    description: string;
    url: string;
  };
  api: {
    url: string;
    timeout: number;
    maxUploadSize: number;
  };
  features: {
    adminFeatures: boolean;
    analytics: boolean;
    debugMode: boolean;
  };
  auth: {
    redirectUrl: string;
  };
}

class ClientConfigManager {
  private static instance: ClientConfigManager;
  private config: ClientConfig;

  private constructor() {
    this.config = this.loadConfig();
  }

  public static getInstance(): ClientConfigManager {
    if (!ClientConfigManager.instance) {
      ClientConfigManager.instance = new ClientConfigManager();
    }
    return ClientConfigManager.instance;
  }

  private loadConfig(): ClientConfig {
    return {
      supabase: {
        url: import.meta.env.VITE_SUPABASE_URL || '',
        anonKey: import.meta.env.VITE_SUPABASE_ANON_KEY || '',
      },
      paystack: {
        publicKey: import.meta.env.VITE_PAYSTACK_PUBLIC_KEY || '',
      },
      app: {
        name: import.meta.env.VITE_APP_NAME || 'SecQuiz',
        description: import.meta.env.VITE_APP_DESCRIPTION || 'A cybersecurity education platform',
        url: import.meta.env.VITE_APP_URL || 'http://localhost:5173',
      },
      api: {
        url: import.meta.env.VITE_API_URL || 'http://localhost:5000',
        timeout: parseInt(import.meta.env.VITE_API_TIMEOUT || '30000', 10),
        maxUploadSize: parseInt(import.meta.env.VITE_MAX_UPLOAD_SIZE || '5242880', 10),
      },
      features: {
        adminFeatures: import.meta.env.VITE_ENABLE_ADMIN_FEATURES === 'true',
        analytics: import.meta.env.VITE_ENABLE_ANALYTICS === 'true',
        debugMode: import.meta.env.VITE_ENABLE_DEBUG_MODE === 'true',
      },
      auth: {
        redirectUrl: import.meta.env.VITE_AUTH_REDIRECT_URL || 'http://localhost:5173/auth/verify',
      },
    };
  }

  public getConfig(): ClientConfig {
    return this.config;
  }

  public getPaystackConfig() {
    return this.config.paystack;
  }

  public getSupabaseConfig() {
    return this.config.supabase;
  }

  public getApiConfig() {
    return this.config.api;
  }

  public getAppConfig() {
    return this.config.app;
  }

  public getFeatureFlags() {
    return this.config.features;
  }

  public getAuthConfig() {
    return this.config.auth;
  }

  public validateConfig(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check required Supabase configuration
    if (!this.config.supabase.url) {
      errors.push('VITE_SUPABASE_URL is required');
    }
    if (!this.config.supabase.anonKey) {
      errors.push('VITE_SUPABASE_ANON_KEY is required');
    }

    // Check required Paystack configuration
    if (!this.config.paystack.publicKey) {
      errors.push('VITE_PAYSTACK_PUBLIC_KEY is required');
    }

    // Check API configuration
    if (!this.config.api.url) {
      errors.push('VITE_API_URL is required');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

export const clientConfigManager = ClientConfigManager.getInstance();
export const clientConfig = clientConfigManager.getConfig();

// Validate configuration on load in development mode
if (import.meta.env.DEV) {
  const validation = clientConfigManager.validateConfig();
  if (!validation.isValid) {
    console.warn('⚠️ Client configuration validation warnings:');
    validation.errors.forEach(error => console.warn(`  - ${error}`));
  }
}