import { useState, useEffect, useCallback } from "react";
import { User } from "@supabase/supabase-js";
import { isUserSubscribed, getUserSubscriptionStatus, getUserSubscriptionDetails } from "@/utils/auth-helpers";

// Enhanced subscription status types
type SubscriptionStatus = 'Free' | 'Premium' | 'Expired' | 'Expiring Soon';

interface SubscriptionDetails {
  plan_id?: string;
  amount_paid?: number;
  start_date?: string;
  end_date?: string;
  is_active?: boolean;
  payment_reference?: string;
}

interface UseSubscriptionStatusReturn {
  isSubscribed: boolean;
  status: SubscriptionStatus;
  details: SubscriptionDetails | null;
  isLoading: boolean;
  daysUntilExpiry: number | null;
  refresh: () => Promise<void>;
  lastUpdated: Date | null;
}

/**
 * Enhanced hook to check user subscription status with detailed information
 * Returns comprehensive subscription data and real-time updates
 */
export function useSubscriptionStatus(
  user: User | null, 
  enableRealTimeUpdates = false,
  updateInterval = 60000 // 1 minute
): UseSubscriptionStatusReturn {
  const [isSubscribed, setIsSubscribed] = useState<boolean>(false);
  const [status, setStatus] = useState<SubscriptionStatus>('Free');
  const [details, setDetails] = useState<SubscriptionDetails | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [daysUntilExpiry, setDaysUntilExpiry] = useState<number | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const checkSubscription = useCallback(async () => {
    if (!user) {
      setIsSubscribed(false);
      setStatus('Free');
      setDetails(null);
      setDaysUntilExpiry(null);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);

      // Get basic subscription status
      const subscribed = await isUserSubscribed(user);
      setIsSubscribed(subscribed);

      // Get detailed subscription status
      const subscriptionStatus = await getUserSubscriptionStatus(user);
      
      // Get detailed subscription information
      let subscriptionDetails: SubscriptionDetails | null = null;
      if (subscriptionStatus === 'Premium') {
        subscriptionDetails = await getUserSubscriptionDetails(user);
      }

      // Calculate days until expiry and determine if expiring soon
      let finalStatus: SubscriptionStatus = subscriptionStatus as SubscriptionStatus;
      let daysLeft: number | null = null;

      if (subscriptionDetails?.end_date) {
        const endDate = new Date(subscriptionDetails.end_date);
        const now = new Date();
        const timeDiff = endDate.getTime() - now.getTime();
        daysLeft = Math.ceil(timeDiff / (1000 * 3600 * 24));
        
        if (daysLeft <= 0) {
          finalStatus = 'Expired';
          setIsSubscribed(false);
        } else if (daysLeft <= 7 && subscriptionStatus === 'Premium') {
          finalStatus = 'Expiring Soon';
        }
      }

      setStatus(finalStatus);
      setDetails(subscriptionDetails);
      setDaysUntilExpiry(daysLeft);
      setLastUpdated(new Date());

    } catch (error) {
      console.error("Error checking subscription status:", error);
      setIsSubscribed(false);
      setStatus('Free');
      setDetails(null);
      setDaysUntilExpiry(null);
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  // Manual refresh function
  const refresh = useCallback(async () => {
    await checkSubscription();
  }, [checkSubscription]);

  // Initial load
  useEffect(() => {
    checkSubscription();
  }, [checkSubscription]);

  // Real-time updates
  useEffect(() => {
    if (!enableRealTimeUpdates || !user) return;

    const interval = setInterval(() => {
      checkSubscription();
    }, updateInterval);

    return () => clearInterval(interval);
  }, [enableRealTimeUpdates, updateInterval, checkSubscription, user]);

  return { 
    isSubscribed, 
    status, 
    details, 
    isLoading, 
    daysUntilExpiry, 
    refresh, 
    lastUpdated 
  };
}
