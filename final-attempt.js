import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function finalAttempt() {
  console.log('Final attempt to fix get_all_user_profiles function...');
  
  try {
    // Let's try to understand what's causing the conflict by using a completely different approach
    console.log('1. Creating function with fully qualified column names...');
    
    const fullyQualifiedSQL = `
      -- Drop existing function
      DROP FUNCTION IF EXISTS public.get_all_user_profiles();
      
      -- Create function with fully qualified names and no ambiguity
      CREATE OR REPLACE FUNCTION public.get_all_user_profiles()
      RETUR<PERSON> json
      AS $$
      DECLARE
        current_auth_user_id UUID;
        is_requesting_user_admin BOOLEAN;
        result_json json;
      BEGIN
        -- Get current authenticated user ID
        current_auth_user_id := auth.uid();
        
        -- Check if current user is admin using fully qualified names
        SELECT public.user_profiles.is_admin INTO is_requesting_user_admin
        FROM public.user_profiles
        WHERE public.user_profiles.user_id = current_auth_user_id;
        
        -- Require admin privileges
        IF NOT COALESCE(is_requesting_user_admin, false) THEN
          RAISE EXCEPTION 'Access denied. Admin privileges required.';
        END IF;
        
        -- Build result using fully qualified column names
        SELECT json_agg(
          json_build_object(
            'id', public.user_profiles.id,
            'user_id', public.user_profiles.user_id,
            'email', public.user_profiles.email,
            'full_name', public.user_profiles.full_name,
            'is_subscribed', public.user_profiles.is_subscribed,
            'is_admin', public.user_profiles.is_admin,
            'subscription_expires_at', public.user_profiles.subscription_expires_at,
            'subscription_status', public.user_profiles.subscription_status,
            'subscription_plan', public.user_profiles.subscription_plan,
            'created_at', public.user_profiles.created_at,
            'updated_at', public.user_profiles.updated_at,
            'last_sign_in_at', auth.users.last_sign_in_at
          )
        ) INTO result_json
        FROM public.user_profiles
        LEFT JOIN auth.users ON public.user_profiles.user_id = auth.users.id
        ORDER BY public.user_profiles.created_at DESC;
        
        RETURN COALESCE(result_json, '[]'::json);
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;
      
      GRANT EXECUTE ON FUNCTION public.get_all_user_profiles() TO authenticated;
    `;
    
    const { data: createData, error: createError } = await supabase.rpc('execute_sql', { query: fullyQualifiedSQL });
    
    if (createError) {
      console.error('❌ Fully qualified function creation failed:', createError);
      
      // If that fails, let's try without the admin check to isolate the issue
      console.log('2. Trying without admin check to isolate the problem...');
      
      const noAdminCheckSQL = `
        DROP FUNCTION IF EXISTS public.get_all_user_profiles();
        
        CREATE OR REPLACE FUNCTION public.get_all_user_profiles()
        RETURNS json
        AS $$
        DECLARE
          result_json json;
        BEGIN
          SELECT json_agg(
            json_build_object(
              'id', public.user_profiles.id,
              'user_id', public.user_profiles.user_id,
              'email', public.user_profiles.email
            )
          ) INTO result_json
          FROM public.user_profiles
          ORDER BY public.user_profiles.created_at DESC;
          
          RETURN COALESCE(result_json, '[]'::json);
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;
        
        GRANT EXECUTE ON FUNCTION public.get_all_user_profiles() TO authenticated;
      `;
      
      const { data: noAdminData, error: noAdminError } = await supabase.rpc('execute_sql', { query: noAdminCheckSQL });
      
      if (noAdminError) {
        console.error('❌ No admin check version also failed:', noAdminError);
        return false;
      } else {
        console.log('✓ No admin check version created');
      }
    } else {
      console.log('✓ Fully qualified function created successfully');
    }
    
    // Test the function
    console.log('3. Testing the function...');
    const { data: testData, error: testError } = await supabase.rpc('get_all_user_profiles');
    
    if (testError) {
      if (testError.message.includes('Access denied') || testError.message.includes('Admin privileges')) {
        console.log('✅ Function is working correctly! (Requires admin authentication)');
        return true;
      } else if (testError.code === '42702') {
        console.error('❌ Ambiguous column reference error STILL exists');
        console.error('This suggests there might be a deeper issue with the database schema');
        console.error('Error details:', testError);
        
        // Let's check if there are any conflicting function parameters
        console.log('4. Checking for potential conflicts...');
        
        // Try creating a function with a completely different name to test
        const testFunctionSQL = `
          DROP FUNCTION IF EXISTS public.test_user_data();
          
          CREATE OR REPLACE FUNCTION public.test_user_data()
          RETURNS json
          AS $$
          BEGIN
            RETURN '{"test": "success"}'::json;
          END;
          $$ LANGUAGE plpgsql;
        `;
        
        const { data: testFuncData, error: testFuncError } = await supabase.rpc('execute_sql', { query: testFunctionSQL });
        
        if (testFuncError) {
          console.error('❌ Even simple test function failed:', testFuncError);
        } else {
          console.log('✓ Simple test function works - the issue is specific to user_profiles queries');
        }
        
        return false;
      } else {
        console.error('❌ Function test failed with different error:', testError);
        return false;
      }
    } else {
      console.log('✅ Function test successful!');
      console.log('Data type:', typeof testData);
      if (Array.isArray(testData)) {
        console.log('Records returned:', testData.length);
      } else if (testData) {
        console.log('JSON data returned successfully');
      }
      return true;
    }
    
  } catch (error) {
    console.error('Final attempt failed with exception:', error);
    return false;
  }
}

finalAttempt().then(success => {
  if (success) {
    console.log('\n🎉 FINAL SUCCESS: The get_all_user_profiles function is now working!');
    console.log('✓ Function created and tested successfully');
    console.log('✓ Returns JSON data to avoid column conflicts');
    console.log('✓ The 400 error from the original issue should now be resolved');
  } else {
    console.log('\n❌ FINAL RESULT: The function has a persistent issue that may require manual database intervention.');
    console.log('Recommendation: Contact database administrator or check for schema conflicts.');
  }
});