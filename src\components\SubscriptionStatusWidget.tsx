import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Crown, 
  Clock, 
  AlertTriangle, 
  Lock, 
  ChevronDown,
  Calendar,
  CreditCard,
  ExternalLink
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { useAuth } from '@/hooks/use-auth';
import { useSubscriptionStatus } from '@/hooks/use-subscription-status';

interface SubscriptionStatusWidgetProps {
  className?: string;
  showDropdown?: boolean;
  variant?: 'badge' | 'button' | 'minimal';
}

const SubscriptionStatusWidget = ({ 
  className = '', 
  showDropdown = true,
  variant = 'badge'
}: SubscriptionStatusWidgetProps) => {
  const { user } = useAuth();
  const { status, details, daysUntilExpiry, isLoading } = useSubscriptionStatus(user, true);
  const [isOpen, setIsOpen] = useState(false);

  if (!user || isLoading) {
    return null;
  }

  // Get status configuration
  const getStatusConfig = () => {
    switch (status) {
      case 'Premium':
        return {
          color: 'bg-green-500/20 text-green-600 border-green-500/30',
          icon: <Crown className="h-3 w-3" />,
          label: 'Premium',
          description: 'All features unlocked'
        };
      case 'Expiring Soon':
        return {
          color: 'bg-amber-500/20 text-amber-600 border-amber-500/30',
          icon: <Clock className="h-3 w-3" />,
          label: 'Expiring Soon',
          description: `${daysUntilExpiry} day${daysUntilExpiry !== 1 ? 's' : ''} left`
        };
      case 'Expired':
        return {
          color: 'bg-red-500/20 text-red-600 border-red-500/30',
          icon: <AlertTriangle className="h-3 w-3" />,
          label: 'Expired',
          description: 'Subscription expired'
        };
      default:
        return {
          color: 'bg-gray-500/20 text-gray-600 border-gray-500/30',
          icon: <Lock className="h-3 w-3" />,
          label: 'Free',
          description: 'Limited access'
        };
    }
  };

  const statusConfig = getStatusConfig();

  // Minimal variant - just an icon
  if (variant === 'minimal') {
    return (
      <div className={`flex items-center ${className}`}>
        <div className={`p-1 rounded-full ${statusConfig.color.split(' ')[0]}`}>
          {statusConfig.icon}
        </div>
      </div>
    );
  }

  // Badge variant
  if (variant === 'badge') {
    return (
      <div className={`relative ${className}`}>
        <Badge 
          className={`${statusConfig.color} cursor-pointer flex items-center space-x-1`}
          onClick={() => showDropdown && setIsOpen(!isOpen)}
        >
          {statusConfig.icon}
          <span>{statusConfig.label}</span>
          {showDropdown && <ChevronDown className="h-3 w-3 ml-1" />}
        </Badge>

        {/* Dropdown */}
        <AnimatePresence>
          {showDropdown && isOpen && (
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className="absolute top-full right-0 mt-2 w-64 bg-white border rounded-lg shadow-lg z-50 p-4"
              onMouseLeave={() => setIsOpen(false)}
            >
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="font-medium">{statusConfig.label}</span>
                  <Badge className={statusConfig.color}>
                    {statusConfig.icon}
                  </Badge>
                </div>

                <p className="text-sm text-muted-foreground">
                  {statusConfig.description}
                </p>

                {details && (
                  <div className="space-y-2 text-sm">
                    {details.plan_id && (
                      <div className="flex items-center space-x-2">
                        <CreditCard className="h-4 w-4 text-blue-500" />
                        <span>
                          {details.plan_id.charAt(0).toUpperCase() + details.plan_id.slice(1)} Plan
                        </span>
                      </div>
                    )}
                    
                    {details.end_date && (
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4 text-gray-500" />
                        <span>
                          Until {new Date(details.end_date).toLocaleDateString()}
                        </span>
                      </div>
                    )}
                  </div>
                )}

                <div className="pt-2 border-t space-y-2">
                  {status === 'Free' && (
                    <Link to="/#pricing" onClick={() => setIsOpen(false)}>
                      <Button size="sm" className="w-full">
                        <Crown className="h-4 w-4 mr-2" />
                        Upgrade
                      </Button>
                    </Link>
                  )}
                  
                  {(status === 'Expired' || status === 'Expiring Soon') && (
                    <Link to="/#pricing" onClick={() => setIsOpen(false)}>
                      <Button size="sm" className="w-full bg-amber-500 hover:bg-amber-600">
                        Renew Now
                      </Button>
                    </Link>
                  )}

                  <Link to="/profile" onClick={() => setIsOpen(false)}>
                    <Button variant="outline" size="sm" className="w-full">
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Manage
                    </Button>
                  </Link>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  }

  // Button variant
  return (
    <div className={`relative ${className}`}>
      <Button
        variant="outline"
        size="sm"
        className={`${statusConfig.color} border-current`}
        onClick={() => showDropdown && setIsOpen(!isOpen)}
      >
        {statusConfig.icon}
        <span className="ml-2">{statusConfig.label}</span>
        {showDropdown && <ChevronDown className="h-3 w-3 ml-1" />}
      </Button>

      {/* Dropdown for button variant */}
      <AnimatePresence>
        {showDropdown && isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full right-0 mt-2 w-64 bg-white border rounded-lg shadow-lg z-50 p-4"
            onMouseLeave={() => setIsOpen(false)}
          >
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="font-medium">Subscription Status</span>
                <Badge className={statusConfig.color}>
                  {statusConfig.icon}
                  <span className="ml-1">{statusConfig.label}</span>
                </Badge>
              </div>

              <p className="text-sm text-muted-foreground">
                {statusConfig.description}
              </p>

              {details && (
                <div className="space-y-2 text-sm">
                  {details.plan_id && (
                    <div className="flex items-center space-x-2">
                      <CreditCard className="h-4 w-4 text-blue-500" />
                      <span>
                        {details.plan_id.charAt(0).toUpperCase() + details.plan_id.slice(1)} Plan
                      </span>
                    </div>
                  )}
                  
                  {details.end_date && (
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <span>
                        Valid until {new Date(details.end_date).toLocaleDateString()}
                      </span>
                    </div>
                  )}
                </div>
              )}

              <div className="pt-2 border-t space-y-2">
                {status === 'Free' && (
                  <Link to="/#pricing" onClick={() => setIsOpen(false)}>
                    <Button size="sm" className="w-full">
                      <Crown className="h-4 w-4 mr-2" />
                      Upgrade to Premium
                    </Button>
                  </Link>
                )}
                
                {(status === 'Expired' || status === 'Expiring Soon') && (
                  <Link to="/#pricing" onClick={() => setIsOpen(false)}>
                    <Button size="sm" className="w-full bg-amber-500 hover:bg-amber-600">
                      Renew Subscription
                    </Button>
                  </Link>
                )}

                <Link to="/profile" onClick={() => setIsOpen(false)}>
                  <Button variant="outline" size="sm" className="w-full">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Manage Subscription
                  </Button>
                </Link>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SubscriptionStatusWidget;