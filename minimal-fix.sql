-- MINIMAL FIX - Just fix the immediate function conflict error
-- Run this first to resolve the delete_user function conflict

-- Step 1: Drop the conflicting function
DROP FUNCTION IF EXISTS public.delete_user(UUID) CASCADE;

-- Step 2: Drop other potentially conflicting functions
DROP FUNCTION IF EXISTS public.get_all_user_profiles() CASCADE;
DROP FUNCTION IF EXISTS public.is_admin() CASCADE;

-- Step 3: Create user_profiles table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.user_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
  email TEXT,
  full_name TEXT,
  is_subscribed BOOLEAN DEFAULT false,
  is_admin BOOLEAN DEFAULT false,
  subscription_expires_at TIMESTAMPTZ,
  subscription_status TEXT DEFAULT 'free',
  subscription_plan TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Step 4: Enable RLS if not already enabled
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

-- Step 5: Backfill users
INSERT INTO public.user_profiles (user_id, email, full_name, is_subscribed, is_admin)
SELECT 
  au.id as user_id,
  au.email,
  au.raw_user_meta_data->>'full_name' as full_name,
  false as is_subscribed,
  false as is_admin
FROM auth.users au
WHERE au.id NOT IN (SELECT user_id FROM public.user_profiles)
ON CONFLICT (user_id) DO UPDATE SET
  email = EXCLUDED.email,
  full_name = EXCLUDED.full_name,
  updated_at = now();

-- Step 6: Grant admin privileges
UPDATE public.user_profiles
SET is_admin = true, updated_at = now()
WHERE user_id IN (
  SELECT id FROM auth.users
  WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')
);

-- Step 7: Create basic RLS policies
DROP POLICY IF EXISTS "Users can view their own profile" ON public.user_profiles;
CREATE POLICY "Users can view their own profile"
  ON public.user_profiles
  FOR SELECT
  USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Admins can view all profiles" ON public.user_profiles;
CREATE POLICY "Admins can view all profiles"
  ON public.user_profiles
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles
      WHERE user_id = auth.uid() AND is_admin = true
    )
  );

-- Step 8: Create is_admin function
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.user_profiles 
    WHERE user_id = auth.uid() AND is_admin = true
  );
END;
$$ LANGUAGE plpgsql;

-- Step 9: Create get_all_user_profiles function
CREATE OR REPLACE FUNCTION public.get_all_user_profiles()
RETURNS TABLE (
  id UUID,
  user_id UUID,
  email TEXT,
  full_name TEXT,
  is_subscribed BOOLEAN,
  is_admin BOOLEAN,
  subscription_expires_at TIMESTAMPTZ,
  subscription_status TEXT,
  subscription_plan TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  last_sign_in_at TIMESTAMPTZ
) AS $$
BEGIN
  -- Check if current user is admin
  IF NOT EXISTS (
    SELECT 1 FROM public.user_profiles
    WHERE user_id = auth.uid() AND is_admin = true
  ) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;

  -- Return all user profiles with auth data
  RETURN QUERY
  SELECT
    up.id,
    up.user_id,
    up.email,
    up.full_name,
    up.is_subscribed,
    up.is_admin,
    up.subscription_expires_at,
    up.subscription_status,
    up.subscription_plan,
    up.created_at,
    up.updated_at,
    au.last_sign_in_at
  FROM public.user_profiles up
  LEFT JOIN auth.users au ON up.user_id = au.id
  ORDER BY up.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 10: Grant permissions
GRANT EXECUTE ON FUNCTION public.get_all_user_profiles() TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_admin() TO authenticated;

-- Verification
SELECT 'Table created successfully' as status, count(*) as users FROM public.user_profiles;
SELECT 'Admin users' as status, email FROM public.user_profiles up JOIN auth.users au ON up.user_id = au.id WHERE up.is_admin = true;
