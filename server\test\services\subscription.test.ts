import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  updateUserSubscription,
  isUserSubscribed,
  getSubscriptionStatus,
  updateSubscriptionStatusAtomic,
  getUserSubscriptionDetails,
  checkAndUpdateExpiredSubscriptions,
  cleanupExpiredSubscriptionsWithRetry,
  validateSubscriptionData,
  getSubscriptionErrorMessage
} from '../services/subscription.js';
import { mockSupabaseClient } from '../test/setup.js';

describe('Subscription Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('updateUserSubscription', () => {
    const validEmail = '<EMAIL>';
    const validPlanId = 'basic';
    const validAmount = 99800; // 998 * 100 (in kobo)
    const validReference = 'test_ref_123456';

    const mockUser = {
      id: 'user_123',
      email: validEmail,
      name: 'Test User'
    };

    beforeEach(() => {
      // Mock successful user lookup
      mockSupabaseClient.auth.admin.listUsers.mockResolvedValue({
        data: {
          users: [mockUser]
        },
        error: null
      });
    });

    describe('Input Validation', () => {
      it('validates required parameters', async () => {
        const result = await updateUserSubscription('', validPlanId, validAmount, validReference);
        
        expect(result.success).toBe(false);
        expect(result.error).toContain('Missing required parameters');
        expect(result.errorCode).toBe('VALIDATION_ERROR');
      });

      it('validates email format', async () => {
        const result = await updateUserSubscription('invalid-email', validPlanId, validAmount, validReference);
        
        expect(result.success).toBe(false);
        expect(result.error).toBe('Invalid email format');
        expect(result.errorCode).toBe('VALIDATION_ERROR');
      });

      it('validates plan ID', async () => {
        const result = await updateUserSubscription(validEmail, 'invalid_plan', validAmount, validReference);
        
        expect(result.success).toBe(false);
        expect(result.error).toContain('Invalid plan ID');
        expect(result.errorCode).toBe('INVALID_PLAN');
      });

      it('validates amount', async () => {
        const result = await updateUserSubscription(validEmail, validPlanId, -100, validReference);
        
        expect(result.success).toBe(false);
        expect(result.error).toBe('Amount must be greater than 0');
        expect(result.errorCode).toBe('VALIDATION_ERROR');
      });

      it('accepts valid parameters', async () => {
        // Mock successful atomic function call
        mockSupabaseClient.rpc.mockResolvedValue({
          data: {
            success: true,
            user_id: 'user_123',
            plan_id: validPlanId,
            end_date: '2024-01-08T00:00:00Z',
            payment_id: 'payment_123',
            subscription_id: 'sub_123',
            reference: validReference
          },
          error: null
        });

        const result = await updateUserSubscription(validEmail, validPlanId, validAmount, validReference);
        
        expect(result.success).toBe(true);
        expect(result.data).toEqual({
          userId: 'user_123',
          planId: validPlanId,
          expiresAt: '2024-01-08T00:00:00Z',
          paymentId: 'payment_123',
          subscriptionId: 'sub_123',
          reference: validReference
        });
      });
    });

    describe('User Lookup', () => {
      it('handles user not found', async () => {
        mockSupabaseClient.auth.admin.listUsers.mockResolvedValue({
          data: {
            users: []
          },
          error: null
        });

        const result = await updateUserSubscription(validEmail, validPlanId, validAmount, validReference);
        
        expect(result.success).toBe(false);
        expect(result.error).toBe('User not found');
        expect(result.errorCode).toBe('USER_NOT_FOUND');
      });

      it('handles user lookup error', async () => {
        mockSupabaseClient.auth.admin.listUsers.mockResolvedValue({
          data: null,
          error: { message: 'Database connection failed' }
        });

        const result = await updateUserSubscription(validEmail, validPlanId, validAmount, validReference);
        
        expect(result.success).toBe(false);
        expect(result.error).toBe('Failed to fetch user data');
        expect(result.errorCode).toBe('DATABASE_ERROR');
      });
    });

    describe('Atomic Payment Processing', () => {
      it('calls atomic function with correct parameters', async () => {
        mockSupabaseClient.rpc.mockResolvedValue({
          data: {
            success: true,
            user_id: 'user_123',
            plan_id: validPlanId,
            reference: validReference
          },
          error: null
        });

        await updateUserSubscription(validEmail, validPlanId, validAmount, validReference);
        
        expect(mockSupabaseClient.rpc).toHaveBeenCalledWith('handle_payment_success_atomic', {
          p_user_id: 'user_123',
          p_plan_id: validPlanId,
          p_amount: 998, // Converted from kobo to naira
          p_reference: validReference,
          p_currency: 'NGN'
        });
      });

      it('handles atomic function not available', async () => {
        mockSupabaseClient.rpc.mockResolvedValue({
          data: null,
          error: { message: 'function handle_payment_success_atomic does not exist' }
        });

        // Mock fallback method dependencies
        const mockSubscriptionQuery = {
          select: vi.fn().mockReturnThis(),
          eq: vi.fn().mockReturnThis(),
          maybeSingle: vi.fn().mockResolvedValue({ data: null, error: null })
        };

        const mockInsertQuery = {
          insert: vi.fn().mockResolvedValue({ data: {}, error: null })
        };

        const mockTransactionQuery = {
          insert: vi.fn().mockResolvedValue({ data: {}, error: null })
        };

        const mockProfileQuery = {
          select: vi.fn().mockReturnThis(),
          eq: vi.fn().mockReturnThis(),
          maybeSingle: vi.fn().mockResolvedValue({ data: null, error: null })
        };

        const mockProfileInsert = {
          insert: vi.fn().mockResolvedValue({ data: {}, error: null })
        };

        mockSupabaseClient.from
          .mockReturnValueOnce(mockSubscriptionQuery)
          .mockReturnValueOnce(mockInsertQuery)
          .mockReturnValueOnce(mockTransactionQuery)
          .mockReturnValueOnce(mockProfileQuery)
          .mockReturnValueOnce(mockProfileInsert);

        const result = await updateUserSubscription(validEmail, validPlanId, validAmount, validReference);
        
        expect(result.success).toBe(true);
        expect(mockSupabaseClient.from).toHaveBeenCalledWith('subscriptions');
      });

      it('handles atomic function failure', async () => {
        mockSupabaseClient.rpc.mockResolvedValue({
          data: {
            success: false,
            error: 'Payment already processed'
          },
          error: null
        });

        const result = await updateUserSubscription(validEmail, validPlanId, validAmount, validReference);
        
        expect(result.success).toBe(false);
        expect(result.error).toBe('Payment already processed');
        expect(result.errorCode).toBe('DUPLICATE_PAYMENT');
      });

      it('handles database error', async () => {
        mockSupabaseClient.rpc.mockResolvedValue({
          data: null,
          error: { message: 'Connection timeout' }
        });

        const result = await updateUserSubscription(validEmail, validPlanId, validAmount, validReference);
        
        expect(result.success).toBe(false);
        expect(result.error).toContain('Database operation failed');
        expect(result.errorCode).toBe('DATABASE_ERROR');
      });
    });

    describe('Error Handling', () => {
      it('handles network errors', async () => {
        mockSupabaseClient.auth.admin.listUsers.mockRejectedValue(
          new Error('network connection failed')
        );

        const result = await updateUserSubscription(validEmail, validPlanId, validAmount, validReference);
        
        expect(result.success).toBe(false);
        expect(result.errorCode).toBe('NETWORK_ERROR');
      });

      it('handles unknown errors', async () => {
        mockSupabaseClient.auth.admin.listUsers.mockRejectedValue(
          new Error('Unexpected error')
        );

        const result = await updateUserSubscription(validEmail, validPlanId, validAmount, validReference);
        
        expect(result.success).toBe(false);
        expect(result.errorCode).toBe('UNKNOWN_ERROR');
      });
    });
  });

  describe('isUserSubscribed', () => {
    it('returns false for empty userId', async () => {
      const result = await isUserSubscribed('');
      expect(result).toBe(false);
    });

    it('returns subscription status from getSubscriptionStatus', async () => {
      // Mock getSubscriptionStatus to return subscribed
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          profile: {
            is_subscribed: true,
            subscription_status: 'active'
          }
        },
        error: null
      });

      const result = await isUserSubscribed('user_123');
      expect(result).toBe(true);
    });

    it('handles getSubscriptionStatus failure', async () => {
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: false,
          error: 'Database error'
        },
        error: null
      });

      const result = await isUserSubscribed('user_123');
      expect(result).toBe(false);
    });
  });

  describe('getSubscriptionStatus', () => {
    const userId = 'user_123';

    it('validates user ID', async () => {
      const result = await getSubscriptionStatus('');
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('User ID is required');
      expect(result.errorCode).toBe('VALIDATION_ERROR');
    });

    it('returns comprehensive status from atomic function', async () => {
      const mockStatusData = {
        success: true,
        profile: {
          is_subscribed: true,
          subscription_status: 'active',
          subscription_expires_at: '2024-01-08T00:00:00Z',
          subscription_plan: 'basic'
        },
        subscription: {
          is_expired: false
        }
      };

      mockSupabaseClient.rpc.mockResolvedValue({
        data: mockStatusData,
        error: null
      });

      const result = await getSubscriptionStatus(userId);
      
      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        userId,
        isSubscribed: true,
        subscriptionStatus: 'active',
        expiresAt: '2024-01-08T00:00:00Z',
        planId: 'basic',
        isExpired: false
      });
    });

    it('handles expired subscription during status check', async () => {
      const mockStatusData = {
        success: true,
        profile: {
          is_subscribed: true,
          subscription_status: 'active',
          subscription_expires_at: '2024-01-01T00:00:00Z',
          subscription_plan: 'basic'
        },
        subscription: {
          is_expired: true
        }
      };

      mockSupabaseClient.rpc
        .mockResolvedValueOnce({
          data: mockStatusData,
          error: null
        })
        .mockResolvedValueOnce({
          data: {
            success: true,
            user_id: userId
          },
          error: null
        });

      const result = await getSubscriptionStatus(userId);
      
      expect(result.success).toBe(true);
      expect(result.data?.isSubscribed).toBe(false);
      expect(result.data?.subscriptionStatus).toBe('expired');
      
      // Should call update function
      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith('update_subscription_status_atomic', {
        p_user_id: userId,
        p_status: 'expired',
        p_reason: 'Subscription expired during status check'
      });
    });

    it('falls back to manual checking when function not available', async () => {
      mockSupabaseClient.rpc.mockResolvedValue({
        data: null,
        error: { message: 'function get_subscription_status does not exist' }
      });

      const mockProfileQuery = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        maybeSingle: vi.fn().mockResolvedValue({
          data: {
            is_subscribed: true,
            subscription_status: 'active',
            subscription_expires_at: '2024-12-31T00:00:00Z',
            subscription_plan: 'pro'
          },
          error: null
        })
      };

      mockSupabaseClient.from.mockReturnValue(mockProfileQuery);

      const result = await getSubscriptionStatus(userId);
      
      expect(result.success).toBe(true);
      expect(result.data?.isSubscribed).toBe(true);
      expect(result.data?.subscriptionStatus).toBe('active');
    });
  });

  describe('updateSubscriptionStatusAtomic', () => {
    const userId = 'user_123';
    const status = 'expired';
    const reason = 'Subscription expired';

    it('validates input parameters', async () => {
      const result = await updateSubscriptionStatusAtomic('', status, reason);
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('User ID and status are required');
      expect(result.errorCode).toBe('VALIDATION_ERROR');
    });

    it('validates status values', async () => {
      const result = await updateSubscriptionStatusAtomic(userId, 'invalid_status', reason);
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid status');
      expect(result.errorCode).toBe('VALIDATION_ERROR');
    });

    it('calls atomic function with correct parameters', async () => {
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          user_id: userId
        },
        error: null
      });

      const result = await updateSubscriptionStatusAtomic(userId, status, reason);
      
      expect(result.success).toBe(true);
      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith('update_subscription_status_atomic', {
        p_user_id: userId,
        p_status: status,
        p_reason: reason
      });
    });

    it('handles database errors', async () => {
      mockSupabaseClient.rpc.mockResolvedValue({
        data: null,
        error: { message: 'Connection failed' }
      });

      const result = await updateSubscriptionStatusAtomic(userId, status, reason);
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('Database error');
      expect(result.errorCode).toBe('DATABASE_ERROR');
    });
  });

  describe('getUserSubscriptionDetails', () => {
    const userId = 'user_123';

    it('returns null for empty userId', async () => {
      const result = await getUserSubscriptionDetails('');
      expect(result).toBeNull();
    });

    it('returns basic status for free users', async () => {
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          profile: {
            is_subscribed: false,
            subscription_status: 'free'
          }
        },
        error: null
      });

      const result = await getUserSubscriptionDetails(userId);
      
      expect(result).toEqual({
        status: 'free',
        isExpired: false
      });
    });

    it('returns detailed subscription information for subscribed users', async () => {
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          profile: {
            is_subscribed: true,
            subscription_status: 'active',
            subscription_expires_at: '2024-12-31T00:00:00Z',
            subscription_plan: 'pro'
          }
        },
        error: null
      });

      const mockSubscriptionQuery = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        order: vi.fn().mockReturnThis(),
        limit: vi.fn().mockReturnThis(),
        maybeSingle: vi.fn().mockResolvedValue({
          data: {
            id: 'sub_123',
            plan_id: 'pro',
            amount_paid: 1979,
            start_date: '2024-01-01T00:00:00Z',
            end_date: '2024-01-08T00:00:00Z',
            is_active: true
          },
          error: null
        })
      };

      mockSupabaseClient.from.mockReturnValue(mockSubscriptionQuery);

      const result = await getUserSubscriptionDetails(userId);
      
      expect(result).toEqual({
        id: 'sub_123',
        plan_id: 'pro',
        amount_paid: 1979,
        start_date: '2024-01-01T00:00:00Z',
        end_date: '2024-01-08T00:00:00Z',
        is_active: true,
        status: 'active',
        isExpired: false,
        expiresAt: '2024-12-31T00:00:00Z'
      });
    });

    it('handles subscription query errors gracefully', async () => {
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          profile: {
            is_subscribed: true,
            subscription_status: 'active'
          }
        },
        error: null
      });

      const mockSubscriptionQuery = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        order: vi.fn().mockReturnThis(),
        limit: vi.fn().mockReturnThis(),
        maybeSingle: vi.fn().mockResolvedValue({
          data: null,
          error: { message: 'Query failed' }
        })
      };

      mockSupabaseClient.from.mockReturnValue(mockSubscriptionQuery);

      const result = await getUserSubscriptionDetails(userId);
      
      expect(result).toEqual({
        status: 'active',
        isExpired: false,
        expiresAt: undefined,
        planId: undefined
      });
    });
  });

  describe('checkAndUpdateExpiredSubscriptions', () => {
    it('calls atomic expiration check function', async () => {
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          expired_count: 5,
          expired_users: ['user1', 'user2', 'user3', 'user4', 'user5']
        },
        error: null
      });

      const result = await checkAndUpdateExpiredSubscriptions();
      
      expect(result.success).toBe(true);
      expect(result.data?.expiredCount).toBe(5);
      expect(result.data?.cleanedUsers).toHaveLength(5);
      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith('check_subscription_expiration');
    });

    it('falls back to manual checking when function not available', async () => {
      mockSupabaseClient.rpc
        .mockResolvedValueOnce({
          data: null,
          error: { message: 'function check_subscription_expiration does not exist' }
        })
        .mockResolvedValueOnce({
          data: [
            { user_id: 'user1', id: 'sub1', end_date: '2023-12-31T00:00:00Z' },
            { user_id: 'user2', id: 'sub2', end_date: '2023-12-30T00:00:00Z' }
          ],
          error: null
        })
        .mockResolvedValue({
          data: { success: true, user_id: 'user1' },
          error: null
        });

      const mockSubscriptionQuery = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        lt: vi.fn().mockReturnThis()
      };

      mockSupabaseClient.from.mockReturnValue(mockSubscriptionQuery);

      const result = await checkAndUpdateExpiredSubscriptions();
      
      expect(result.success).toBe(true);
      expect(result.data?.expiredCount).toBeGreaterThan(0);
    });

    it('handles database errors', async () => {
      mockSupabaseClient.rpc.mockResolvedValue({
        data: null,
        error: { message: 'Database connection failed' }
      });

      const result = await checkAndUpdateExpiredSubscriptions();
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('Database error');
      expect(result.errorCode).toBe('DATABASE_ERROR');
    });
  });

  describe('cleanupExpiredSubscriptionsWithRetry', () => {
    it('succeeds on first attempt', async () => {
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          expired_count: 3,
          expired_users: ['user1', 'user2', 'user3']
        },
        error: null
      });

      const result = await cleanupExpiredSubscriptionsWithRetry(3);
      
      expect(result.success).toBe(true);
      expect(result.data?.expiredCount).toBe(3);
      expect(mockSupabaseClient.rpc).toHaveBeenCalledTimes(1);
    });

    it('retries on failure with exponential backoff', async () => {
      vi.useFakeTimers();

      mockSupabaseClient.rpc
        .mockResolvedValueOnce({
          data: { success: false, error: 'Temporary failure' },
          error: null
        })
        .mockResolvedValueOnce({
          data: {
            success: true,
            expired_count: 2,
            expired_users: ['user1', 'user2']
          },
          error: null
        });

      const resultPromise = cleanupExpiredSubscriptionsWithRetry(2);

      // Fast-forward through retry delay
      vi.advanceTimersByTime(1000);
      await vi.runAllTimersAsync();

      const result = await resultPromise;
      
      expect(result.success).toBe(true);
      expect(mockSupabaseClient.rpc).toHaveBeenCalledTimes(2);

      vi.useRealTimers();
    });

    it('fails after max retries', async () => {
      vi.useFakeTimers();

      mockSupabaseClient.rpc.mockResolvedValue({
        data: { success: false, error: 'Persistent failure' },
        error: null
      });

      const resultPromise = cleanupExpiredSubscriptionsWithRetry(2);

      // Fast-forward through all retry delays
      vi.advanceTimersByTime(1000); // First retry
      await vi.runAllTimersAsync();
      vi.advanceTimersByTime(2000); // Second retry
      await vi.runAllTimersAsync();

      const result = await resultPromise;
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('Cleanup failed after 2 attempts');
      expect(mockSupabaseClient.rpc).toHaveBeenCalledTimes(2);

      vi.useRealTimers();
    });
  });

  describe('validateSubscriptionData', () => {
    it('validates complete valid data', () => {
      const validData = {
        email: '<EMAIL>',
        planId: 'basic',
        amount: 99800,
        reference: 'test_ref_123456'
      };

      const result = validateSubscriptionData(validData);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('validates email format', () => {
      const invalidData = {
        email: 'invalid-email',
        planId: 'basic',
        amount: 99800,
        reference: 'test_ref_123456'
      };

      const result = validateSubscriptionData(invalidData);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid email format');
    });

    it('validates plan ID', () => {
      const invalidData = {
        email: '<EMAIL>',
        planId: 'invalid_plan',
        amount: 99800,
        reference: 'test_ref_123456'
      };

      const result = validateSubscriptionData(invalidData);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid plan ID');
    });

    it('validates amount', () => {
      const invalidData = {
        email: '<EMAIL>',
        planId: 'basic',
        amount: -100,
        reference: 'test_ref_123456'
      };

      const result = validateSubscriptionData(invalidData);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Amount must be greater than 0');
    });

    it('validates reference length', () => {
      const invalidData = {
        email: '<EMAIL>',
        planId: 'basic',
        amount: 99800,
        reference: 'short'
      };

      const result = validateSubscriptionData(invalidData);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Payment reference must be at least 10 characters');
    });

    it('collects multiple validation errors', () => {
      const invalidData = {
        email: '',
        planId: '',
        amount: 0,
        reference: ''
      };

      const result = validateSubscriptionData(invalidData);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(1);
    });
  });

  describe('getSubscriptionErrorMessage', () => {
    it('returns appropriate message for each error code', () => {
      expect(getSubscriptionErrorMessage('USER_NOT_FOUND'))
        .toContain('User account not found');
      
      expect(getSubscriptionErrorMessage('INVALID_PLAN'))
        .toContain('Invalid subscription plan');
      
      expect(getSubscriptionErrorMessage('DUPLICATE_PAYMENT'))
        .toContain('already processed'); // Note: function is truncated in the file
    });

    it('handles unknown error codes', () => {
      const message = getSubscriptionErrorMessage('UNKNOWN_ERROR_CODE');
      expect(typeof message).toBe('string');
      expect(message.length).toBeGreaterThan(0);
    });
  });
});