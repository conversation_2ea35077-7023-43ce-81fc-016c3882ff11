/**
 * Rate limiting middleware for API endpoints
 */

import { Request, Response, NextFunction } from 'express';
import { secureLogger } from '../lib/secure-logger.js';

interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  message?: string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

interface RateLimitStore {
  [key: string]: {
    count: number;
    resetTime: number;
  };
}

class InMemoryRateLimitStore {
  private store: RateLimitStore = {};
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // Clean up expired entries every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  private cleanup(): void {
    const now = Date.now();
    for (const key in this.store) {
      if (this.store[key].resetTime < now) {
        delete this.store[key];
      }
    }
  }

  get(key: string): { count: number; resetTime: number } | null {
    const entry = this.store[key];
    if (!entry || entry.resetTime < Date.now()) {
      return null;
    }
    return entry;
  }

  set(key: string, count: number, resetTime: number): void {
    this.store[key] = { count, resetTime };
  }

  increment(key: string, windowMs: number): { count: number; resetTime: number } {
    const now = Date.now();
    const existing = this.get(key);
    
    if (!existing) {
      const resetTime = now + windowMs;
      this.set(key, 1, resetTime);
      return { count: 1, resetTime };
    }

    existing.count++;
    this.set(key, existing.count, existing.resetTime);
    return existing;
  }

  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.store = {};
  }
}

// Global store instance
const rateLimitStore = new InMemoryRateLimitStore();

/**
 * Creates a rate limiting middleware
 */
export function createRateLimiter(config: RateLimitConfig) {
  return (req: Request, res: Response, next: NextFunction) => {
    // Generate key based on IP address and user agent for better uniqueness
    const clientId = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.headers['user-agent'] || 'unknown';
    const key = `${clientId}:${userAgent.substring(0, 50)}`;

    try {
      const result = rateLimitStore.increment(key, config.windowMs);
      
      // Set rate limit headers
      res.setHeader('X-RateLimit-Limit', config.maxRequests);
      res.setHeader('X-RateLimit-Remaining', Math.max(0, config.maxRequests - result.count));
      res.setHeader('X-RateLimit-Reset', Math.ceil(result.resetTime / 1000));

      if (result.count > config.maxRequests) {
        // Log rate limit violation
        secureLogger.security('Rate limit exceeded', {
          ip: clientId,
          userAgent: userAgent.substring(0, 100),
          path: req.path,
          method: req.method,
          count: result.count,
          limit: config.maxRequests
        });

        return res.status(429).json({
          status: 'error',
          message: config.message || 'Too many requests, please try again later.',
          retryAfter: Math.ceil((result.resetTime - Date.now()) / 1000)
        });
      }

      next();
    } catch (error) {
      secureLogger.error('Rate limiter error', { error: error instanceof Error ? error.message : String(error) });
      // If rate limiter fails, allow the request to continue
      next();
    }
  };
}

/**
 * Rate limiter for payment endpoints - more restrictive
 */
export const paymentRateLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 10, // 10 requests per 15 minutes
  message: 'Too many payment requests. Please wait before trying again.'
});

/**
 * Rate limiter for webhook endpoints - moderate restrictions
 */
export const webhookRateLimiter = createRateLimiter({
  windowMs: 1 * 60 * 1000, // 1 minute
  maxRequests: 100, // 100 requests per minute (Paystack can send many webhooks)
  message: 'Webhook rate limit exceeded.'
});

/**
 * Rate limiter for general API endpoints
 */
export const generalRateLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 100, // 100 requests per 15 minutes
  message: 'Too many requests. Please try again later.'
});

/**
 * Rate limiter for authentication endpoints - very restrictive
 */
export const authRateLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 5, // 5 attempts per 15 minutes
  message: 'Too many authentication attempts. Please wait before trying again.'
});

// Cleanup on process exit
process.on('SIGTERM', () => {
  rateLimitStore.destroy();
});

process.on('SIGINT', () => {
  rateLimitStore.destroy();
});