import Navbar from "@/components/Navbar";
import BottomNavigation from "@/components/BottomNavigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { motion } from "framer-motion";
import { Mail, Phone, Send, Loader2 } from "lucide-react";
import { MessageSquare } from "lucide-react";
import { useState } from "react";
import { useToast } from "@/components/ui/use-toast";
import { submitFeedback } from "@/services/feedback-service";
import { useAuth } from "@/hooks/use-auth";
import { sanitizeInput, isValidEmail } from "@/utils/security";

const ContactPage = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [formData, setFormData] = useState({
    name: user?.user_metadata?.full_name || "",
    email: user?.email || "",
    subject: "",
    message: ""
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Sanitize and validate inputs
      const sanitizedName = sanitizeInput(formData.name.trim());
      const sanitizedEmail = sanitizeInput(formData.email.trim());
      const sanitizedSubject = sanitizeInput(formData.subject.trim());
      const sanitizedMessage = sanitizeInput(formData.message.trim());

      // Validate inputs
      if (!sanitizedName) {
        toast({
          title: "Invalid Name",
          description: "Please enter your name.",
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }

      if (!isValidEmail(sanitizedEmail)) {
        toast({
          title: "Invalid Email",
          description: "Please enter a valid email address.",
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }

      if (!sanitizedSubject) {
        toast({
          title: "Invalid Subject",
          description: "Please enter a subject for your message.",
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }

      if (!sanitizedMessage) {
        toast({
          title: "Invalid Message",
          description: "Please enter your message.",
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }

      // Submit sanitized data
      const sanitizedFormData = {
        name: sanitizedName,
        email: sanitizedEmail,
        subject: sanitizedSubject,
        message: sanitizedMessage
      };

      // Submit feedback to database
      const result = await submitFeedback(sanitizedFormData, user);

      if (result.success) {
        toast({
          title: "Message sent",
          description: "Thank you for your feedback! We'll get back to you soon.",
        });

        setFormData({
          name: user?.user_metadata?.full_name || "",
          email: user?.email || "",
          subject: "",
          message: ""
        });
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to send message. Please try again.",
          variant: "destructive",
        });
      }
    } catch {
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />

      <main className="flex-1 container py-12">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="max-w-4xl mx-auto"
        >
          <motion.div variants={itemVariants} className="mb-8 text-center">
            <div className="inline-flex mb-4 p-3 rounded-full bg-cyber-primary/10">
              <img src="/secquiz-logo.svg" alt="SecQuiz Logo" className="h-14 w-14" />
            </div>
            <h1 className="text-3xl md:text-4xl font-bold mb-4">Get in Touch</h1>
            <p className="text-muted-foreground">
              Have questions about SecQuiz? We're here to help!
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
            <motion.div
              variants={itemVariants}
              className="bg-background/60 backdrop-blur-sm border rounded-lg p-6 text-center hover:border-cyber-primary/50 transition-all hover:shadow-md"
              whileHover={{ y: -5, transition: { duration: 0.2 } }}
            >
              <div className="mb-3 inline-flex items-center justify-center w-12 h-12 rounded-full bg-blue-100">
                <Mail className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="font-medium mb-2">Email Us</h3>
              <a
                href="mailto:<EMAIL>"
                className="text-sm text-cyber-primary hover:underline"
              >
                <EMAIL>
              </a>
            </motion.div>

            <motion.div
              variants={itemVariants}
              className="bg-background/60 backdrop-blur-sm border rounded-lg p-6 text-center hover:border-cyber-primary/50 transition-all hover:shadow-md"
              whileHover={{ y: -5, transition: { duration: 0.2 } }}
            >
              <div className="mb-3 inline-flex items-center justify-center w-12 h-12 rounded-full bg-green-100">
                <Phone className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="font-medium mb-2">Call Us</h3>
              <a
                href="tel:+2347018643642"
                className="text-sm text-cyber-primary hover:underline"
              >
                07018643642
              </a>
            </motion.div>

            <motion.div
              variants={itemVariants}
              className="bg-background/60 backdrop-blur-sm border rounded-lg p-6 text-center hover:border-cyber-primary/50 transition-all hover:shadow-md"
              whileHover={{ y: -5, transition: { duration: 0.2 } }}
            >
              <div className="mb-3 inline-flex items-center justify-center w-12 h-12 rounded-full bg-purple-100">
                <MessageSquare className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="font-medium mb-2">Chat Us</h3>
              <a
                href="https://wa.me/2347018643642"
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm text-cyber-primary hover:underline"
              >
                On WhatsApp
              </a>
            </motion.div>
          </div>

          <motion.div variants={itemVariants}>
            <Card>
              <CardHeader>
                <CardTitle>Contact Form</CardTitle>
                <CardDescription>
                  Fill out the form below and we'll get back to you as soon as possible.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label htmlFor="name" className="text-sm font-medium">
                        Name
                      </label>
                      <Input
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        placeholder="Your name"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="email" className="text-sm font-medium">
                        Email
                      </label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleChange}
                        placeholder="Your email"
                        required
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="subject" className="text-sm font-medium">
                      Subject
                    </label>
                    <Input
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleChange}
                      placeholder="Subject of your message"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="message" className="text-sm font-medium">
                      Message
                    </label>
                    <Textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      placeholder="Your message"
                      rows={5}
                      required
                    />
                  </div>
                  <Button
                    type="submit"
                    className="w-full bg-cyber-primary hover:bg-cyber-primary/90"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Sending...
                      </>
                    ) : (
                      <>
                        Send Message <Send className="ml-2 h-4 w-4" />
                      </>
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>
      </main>

      <BottomNavigation />
    </div>
  );
};

export default ContactPage;
