/**
 * Comprehensive payment event logging system with metrics and monitoring
 */

import { secureLogger } from './secure-logger.js';
import { supabase } from './supabase.js';

// Payment event types
export enum PaymentEventType {
  PAYMENT_INITIATED = 'payment_initiated',
  PAYMENT_VERIFICATION_STARTED = 'payment_verification_started',
  PAYMENT_VERIFICATION_SUCCESS = 'payment_verification_success',
  PAYMENT_VERIFICATION_FAILED = 'payment_verification_failed',
  SUBSCRIPTION_UPDATED = 'subscription_updated',
  SUBSCRIPTION_UPDATE_FAILED = 'subscription_update_failed',
  WEBHOOK_RECEIVED = 'webhook_received',
  WEBHOOK_PROCESSED = 'webhook_processed',
  WEBHOOK_FAILED = 'webhook_failed',
  PAYMENT_TIMEOUT = 'payment_timeout',
  PAYMENT_ERROR = 'payment_error',
  SUBSCRIPTION_EXPIRED = 'subscription_expired',
  SUBSCRIPTION_RENEWED = 'subscription_renewed'
}

// Payment event severity levels
export enum PaymentEventSeverity {
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  CRITICAL = 'critical'
}

// Payment event interface
export interface PaymentEvent {
  id?: string;
  event_type: PaymentEventType;
  severity: PaymentEventSeverity;
  reference?: string;
  user_id?: string;
  plan_id?: string;
  amount?: number;
  currency?: string;
  status?: string;
  error_code?: string;
  error_message?: string;
  processing_time_ms?: number;
  metadata?: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  created_at?: string;
}

// Payment metrics interface
export interface PaymentMetrics {
  total_payments: number;
  successful_payments: number;
  failed_payments: number;
  success_rate: number;
  average_processing_time: number;
  total_revenue: number;
  webhook_events: number;
  webhook_failures: number;
  period_start: string;
  period_end: string;
}

// Error tracking interface
export interface PaymentError {
  id?: string;
  error_code: string;
  error_message: string;
  event_type: PaymentEventType;
  reference?: string;
  user_id?: string;
  stack_trace?: string;
  context?: Record<string, any>;
  occurrence_count: number;
  first_occurred: string;
  last_occurred: string;
}

class PaymentEventLogger {
  private metricsCache: Map<string, any> = new Map();
  private cacheExpiry: number = 5 * 60 * 1000; // 5 minutes

  /**
   * Log a payment event with comprehensive details
   */
  async logEvent(event: PaymentEvent): Promise<void> {
    try {
      // Add timestamp if not provided
      if (!event.created_at) {
        event.created_at = new Date().toISOString();
      }

      // Sanitize sensitive data
      const sanitizedEvent = this.sanitizeEventData(event);

      // Log to console with secure logger
      this.logToConsole(sanitizedEvent);

      // Store in database for analytics and monitoring
      await this.storeEventInDatabase(sanitizedEvent);

      // Update real-time metrics
      this.updateMetricsCache(sanitizedEvent);

    } catch (error) {
      secureLogger.error('Failed to log payment event', {
        eventType: event.event_type,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Log payment success with detailed metrics
   */
  async logPaymentSuccess(data: {
    reference: string;
    userId?: string;
    planId: string;
    amount: number;
    currency?: string;
    processingTime: number;
    metadata?: Record<string, any>;
  }): Promise<void> {
    await this.logEvent({
      event_type: PaymentEventType.PAYMENT_VERIFICATION_SUCCESS,
      severity: PaymentEventSeverity.INFO,
      reference: data.reference,
      user_id: data.userId,
      plan_id: data.planId,
      amount: data.amount,
      currency: data.currency || 'NGN',
      status: 'success',
      processing_time_ms: data.processingTime,
      metadata: data.metadata
    });
  }

  /**
   * Log payment failure with error details
   */
  async logPaymentFailure(data: {
    reference?: string;
    userId?: string;
    planId?: string;
    amount?: number;
    errorCode: string;
    errorMessage: string;
    processingTime?: number;
    metadata?: Record<string, any>;
  }): Promise<void> {
    await this.logEvent({
      event_type: PaymentEventType.PAYMENT_VERIFICATION_FAILED,
      severity: PaymentEventSeverity.ERROR,
      reference: data.reference,
      user_id: data.userId,
      plan_id: data.planId,
      amount: data.amount,
      status: 'failed',
      error_code: data.errorCode,
      error_message: data.errorMessage,
      processing_time_ms: data.processingTime,
      metadata: data.metadata
    });

    // Track error for alerting
    await this.trackError({
      error_code: data.errorCode,
      error_message: data.errorMessage,
      event_type: PaymentEventType.PAYMENT_VERIFICATION_FAILED,
      reference: data.reference,
      user_id: data.userId,
      context: data.metadata
    });
  }

  /**
   * Log webhook event processing
   */
  async logWebhookEvent(data: {
    eventId: string;
    eventType: string;
    reference?: string;
    status: 'received' | 'processed' | 'failed';
    processingTime?: number;
    errorMessage?: string;
    ipAddress?: string;
    userAgent?: string;
  }): Promise<void> {
    const severity = data.status === 'failed' ? PaymentEventSeverity.ERROR : PaymentEventSeverity.INFO;
    const paymentEventType = data.status === 'failed' ? 
      PaymentEventType.WEBHOOK_FAILED : 
      (data.status === 'processed' ? PaymentEventType.WEBHOOK_PROCESSED : PaymentEventType.WEBHOOK_RECEIVED);

    await this.logEvent({
      event_type: paymentEventType,
      severity,
      reference: data.reference,
      status: data.status,
      error_message: data.errorMessage,
      processing_time_ms: data.processingTime,
      ip_address: data.ipAddress,
      user_agent: data.userAgent,
      metadata: {
        webhook_event_id: data.eventId,
        webhook_event_type: data.eventType
      }
    });
  }

  /**
   * Log subscription events
   */
  async logSubscriptionEvent(data: {
    eventType: PaymentEventType.SUBSCRIPTION_UPDATED | PaymentEventType.SUBSCRIPTION_UPDATE_FAILED | 
               PaymentEventType.SUBSCRIPTION_EXPIRED | PaymentEventType.SUBSCRIPTION_RENEWED;
    userId: string;
    planId?: string;
    reference?: string;
    errorMessage?: string;
    metadata?: Record<string, any>;
  }): Promise<void> {
    const severity = data.eventType === PaymentEventType.SUBSCRIPTION_UPDATE_FAILED ? 
      PaymentEventSeverity.ERROR : PaymentEventSeverity.INFO;

    await this.logEvent({
      event_type: data.eventType,
      severity,
      user_id: data.userId,
      plan_id: data.planId,
      reference: data.reference,
      error_message: data.errorMessage,
      metadata: data.metadata
    });
  }

  /**
   * Get payment metrics for a specific time period
   */
  async getPaymentMetrics(periodHours: number = 24): Promise<PaymentMetrics> {
    const cacheKey = `metrics_${periodHours}h`;
    const cached = this.metricsCache.get(cacheKey);
    
    if (cached && (Date.now() - cached.timestamp) < this.cacheExpiry) {
      return cached.data;
    }

    try {
      const endTime = new Date();
      const startTime = new Date(endTime.getTime() - (periodHours * 60 * 60 * 1000));

      const { data, error } = await supabase.rpc('get_payment_metrics', {
        start_time: startTime.toISOString(),
        end_time: endTime.toISOString()
      });

      if (error) {
        secureLogger.error('Failed to get payment metrics from database', { error: error.message });
        return this.getMetricsFromCache();
      }

      const metrics: PaymentMetrics = {
        total_payments: data?.total_payments || 0,
        successful_payments: data?.successful_payments || 0,
        failed_payments: data?.failed_payments || 0,
        success_rate: data?.success_rate || 0,
        average_processing_time: data?.average_processing_time || 0,
        total_revenue: data?.total_revenue || 0,
        webhook_events: data?.webhook_events || 0,
        webhook_failures: data?.webhook_failures || 0,
        period_start: startTime.toISOString(),
        period_end: endTime.toISOString()
      };

      // Cache the results
      this.metricsCache.set(cacheKey, {
        data: metrics,
        timestamp: Date.now()
      });

      return metrics;

    } catch (error) {
      secureLogger.error('Error calculating payment metrics', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return this.getMetricsFromCache();
    }
  }

  /**
   * Get recent payment errors for monitoring
   */
  async getRecentErrors(limit: number = 50): Promise<PaymentError[]> {
    try {
      const { data, error } = await supabase
        .from('payment_errors')
        .select('*')
        .order('last_occurred', { ascending: false })
        .limit(limit);

      if (error) {
        secureLogger.error('Failed to fetch payment errors', { error: error.message });
        return [];
      }

      return data || [];

    } catch (error) {
      secureLogger.error('Error fetching payment errors', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return [];
    }
  }

  /**
   * Check for critical payment issues that need immediate attention
   */
  async checkCriticalIssues(): Promise<{
    hasIssues: boolean;
    issues: Array<{
      type: string;
      severity: string;
      message: string;
      count?: number;
    }>;
  }> {
    const issues: Array<{ type: string; severity: string; message: string; count?: number }> = [];

    try {
      // Check recent metrics
      const metrics = await this.getPaymentMetrics(1); // Last hour

      // Check success rate
      if (metrics.total_payments > 0 && metrics.success_rate < 0.8) {
        issues.push({
          type: 'low_success_rate',
          severity: 'critical',
          message: `Payment success rate is ${(metrics.success_rate * 100).toFixed(1)}% (below 80%)`,
          count: metrics.failed_payments
        });
      }

      // Check for high error rates
      if (metrics.webhook_events > 0 && metrics.webhook_failures / metrics.webhook_events > 0.1) {
        issues.push({
          type: 'high_webhook_failure_rate',
          severity: 'error',
          message: `Webhook failure rate is ${((metrics.webhook_failures / metrics.webhook_events) * 100).toFixed(1)}%`,
          count: metrics.webhook_failures
        });
      }

      // Check for processing time issues
      if (metrics.average_processing_time > 10000) { // 10 seconds
        issues.push({
          type: 'slow_processing',
          severity: 'warn',
          message: `Average processing time is ${(metrics.average_processing_time / 1000).toFixed(1)}s`,
        });
      }

      // Check for recent critical errors
      const recentErrors = await this.getRecentErrors(10);
      const criticalErrors = recentErrors.filter(error => 
        error.last_occurred > new Date(Date.now() - 60 * 60 * 1000).toISOString() // Last hour
      );

      if (criticalErrors.length > 5) {
        issues.push({
          type: 'high_error_count',
          severity: 'critical',
          message: `${criticalErrors.length} critical errors in the last hour`,
          count: criticalErrors.length
        });
      }

      return {
        hasIssues: issues.length > 0,
        issues
      };

    } catch (error) {
      secureLogger.error('Error checking critical issues', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      return {
        hasIssues: true,
        issues: [{
          type: 'monitoring_error',
          severity: 'error',
          message: 'Failed to check system health'
        }]
      };
    }
  }

  /**
   * Sanitize event data to remove sensitive information
   */
  private sanitizeEventData(event: PaymentEvent): PaymentEvent {
    const sanitized = { ...event };

    // Remove or mask sensitive metadata
    if (sanitized.metadata) {
      sanitized.metadata = Object.keys(sanitized.metadata).reduce((acc, key) => {
        if (/secret|key|token|password/i.test(key)) {
          acc[key] = '[REDACTED]';
        } else if (key === 'email' && typeof sanitized.metadata![key] === 'string') {
          const email = sanitized.metadata![key] as string;
          acc[key] = email.replace(/(.{2}).*@/, '$1***@');
        } else {
          acc[key] = sanitized.metadata![key];
        }
        return acc;
      }, {} as Record<string, any>);
    }

    return sanitized;
  }

  /**
   * Log event to console with appropriate formatting
   */
  private logToConsole(event: PaymentEvent): void {
    const logData = {
      eventType: event.event_type,
      severity: event.severity,
      reference: event.reference,
      userId: event.user_id,
      planId: event.plan_id,
      amount: event.amount,
      status: event.status,
      processingTime: event.processing_time_ms,
      errorCode: event.error_code,
      errorMessage: event.error_message
    };

    switch (event.severity) {
      case PaymentEventSeverity.CRITICAL:
      case PaymentEventSeverity.ERROR:
        secureLogger.error(`Payment Event: ${event.event_type}`, logData);
        break;
      case PaymentEventSeverity.WARN:
        secureLogger.warn(`Payment Event: ${event.event_type}`, logData);
        break;
      default:
        secureLogger.info(`Payment Event: ${event.event_type}`, logData);
    }
  }

  /**
   * Store event in database for analytics
   */
  private async storeEventInDatabase(event: PaymentEvent): Promise<void> {
    try {
      const { error } = await supabase
        .from('payment_events')
        .insert({
          event_type: event.event_type,
          severity: event.severity,
          reference: event.reference,
          user_id: event.user_id,
          plan_id: event.plan_id,
          amount: event.amount,
          currency: event.currency,
          status: event.status,
          error_code: event.error_code,
          error_message: event.error_message,
          processing_time_ms: event.processing_time_ms,
          metadata: event.metadata,
          ip_address: event.ip_address,
          user_agent: event.user_agent,
          created_at: event.created_at
        });

      if (error) {
        secureLogger.error('Failed to store payment event in database', { error: error.message });
      }
    } catch (error) {
      secureLogger.error('Error storing payment event', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Track error for alerting and monitoring
   */
  private async trackError(error: Omit<PaymentError, 'id' | 'occurrence_count' | 'first_occurred' | 'last_occurred'>): Promise<void> {
    try {
      const now = new Date().toISOString();

      const { error: upsertError } = await supabase
        .from('payment_errors')
        .upsert({
          error_code: error.error_code,
          error_message: error.error_message,
          event_type: error.event_type,
          reference: error.reference,
          user_id: error.user_id,
          stack_trace: error.stack_trace,
          context: error.context,
          occurrence_count: 1,
          first_occurred: now,
          last_occurred: now
        }, {
          onConflict: 'error_code,event_type',
          ignoreDuplicates: false
        });

      if (upsertError) {
        secureLogger.error('Failed to track payment error', { error: upsertError.message });
      }
    } catch (error) {
      secureLogger.error('Error tracking payment error', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Update metrics cache with new event data
   */
  private updateMetricsCache(event: PaymentEvent): void {
    // Simple in-memory metrics tracking for real-time updates
    const cacheKey = 'realtime_metrics';
    const cached = this.metricsCache.get(cacheKey) || {
      total_events: 0,
      success_events: 0,
      error_events: 0,
      total_processing_time: 0,
      last_updated: Date.now()
    };

    cached.total_events++;
    
    if (event.severity === PaymentEventSeverity.ERROR || event.severity === PaymentEventSeverity.CRITICAL) {
      cached.error_events++;
    } else if (event.event_type === PaymentEventType.PAYMENT_VERIFICATION_SUCCESS) {
      cached.success_events++;
    }

    if (event.processing_time_ms) {
      cached.total_processing_time += event.processing_time_ms;
    }

    cached.last_updated = Date.now();
    this.metricsCache.set(cacheKey, cached);
  }

  /**
   * Get fallback metrics from cache when database is unavailable
   */
  private getMetricsFromCache(): PaymentMetrics {
    const cached = this.metricsCache.get('realtime_metrics');
    const now = new Date();
    const hourAgo = new Date(now.getTime() - 60 * 60 * 1000);

    if (cached) {
      return {
        total_payments: cached.total_events,
        successful_payments: cached.success_events,
        failed_payments: cached.error_events,
        success_rate: cached.total_events > 0 ? cached.success_events / cached.total_events : 0,
        average_processing_time: cached.total_events > 0 ? cached.total_processing_time / cached.total_events : 0,
        total_revenue: 0, // Not tracked in cache
        webhook_events: 0, // Not tracked in cache
        webhook_failures: 0, // Not tracked in cache
        period_start: hourAgo.toISOString(),
        period_end: now.toISOString()
      };
    }

    return {
      total_payments: 0,
      successful_payments: 0,
      failed_payments: 0,
      success_rate: 0,
      average_processing_time: 0,
      total_revenue: 0,
      webhook_events: 0,
      webhook_failures: 0,
      period_start: hourAgo.toISOString(),
      period_end: now.toISOString()
    };
  }
}

// Export singleton instance
export const paymentEventLogger = new PaymentEventLogger();

// Export convenience functions
export const {
  logEvent,
  logPaymentSuccess,
  logPaymentFailure,
  logWebhookEvent,
  logSubscriptionEvent,
  getPaymentMetrics,
  getRecentErrors,
  checkCriticalIssues
} = paymentEventLogger;