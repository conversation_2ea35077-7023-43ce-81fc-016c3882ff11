/**
 * API Key management utility for secure handling of sensitive keys
 */

import { secureLogger } from './secure-logger.js';

export interface ApiKeyValidation {
  isValid: boolean;
  keyType?: 'test' | 'live';
  errors: string[];
}

/**
 * Validates Paystack secret key format and type
 */
export function validatePaystackSecretKey(key: string): ApiKeyValidation {
  const errors: string[] = [];

  if (!key) {
    errors.push('Secret key is required');
    return { isValid: false, errors };
  }

  if (typeof key !== 'string') {
    errors.push('Secret key must be a string');
    return { isValid: false, errors };
  }

  // Check if it's a test key
  if (key.startsWith('sk_test_')) {
    if (key.length < 40) {
      errors.push('Test secret key appears to be too short');
    }
    return {
      isValid: errors.length === 0,
      keyType: 'test',
      errors
    };
  }

  // Check if it's a live key
  if (key.startsWith('sk_live_')) {
    if (key.length < 40) {
      errors.push('Live secret key appears to be too short');
    }
    return {
      isValid: errors.length === 0,
      keyType: 'live',
      errors
    };
  }

  errors.push('Secret key must start with sk_test_ or sk_live_');
  return { isValid: false, errors };
}

/**
 * Validates Paystack public key format and type
 */
export function validatePaystackPublicKey(key: string): ApiKeyValidation {
  const errors: string[] = [];

  if (!key) {
    errors.push('Public key is required');
    return { isValid: false, errors };
  }

  if (typeof key !== 'string') {
    errors.push('Public key must be a string');
    return { isValid: false, errors };
  }

  // Check if it's a test key
  if (key.startsWith('pk_test_')) {
    if (key.length < 40) {
      errors.push('Test public key appears to be too short');
    }
    return {
      isValid: errors.length === 0,
      keyType: 'test',
      errors
    };
  }

  // Check if it's a live key
  if (key.startsWith('pk_live_')) {
    if (key.length < 40) {
      errors.push('Live public key appears to be too short');
    }
    return {
      isValid: errors.length === 0,
      keyType: 'live',
      errors
    };
  }

  errors.push('Public key must start with pk_test_ or pk_live_');
  return { isValid: false, errors };
}

/**
 * Ensures API keys are properly configured for the environment
 */
export function validateApiKeyEnvironmentConsistency(
  secretKey: string,
  publicKey: string,
  environment: string
): { isValid: boolean; warnings: string[] } {
  const warnings: string[] = [];
  
  const secretValidation = validatePaystackSecretKey(secretKey);
  const publicValidation = validatePaystackPublicKey(publicKey);

  if (!secretValidation.isValid || !publicValidation.isValid) {
    return { isValid: false, warnings: ['Keys must be valid before checking consistency'] };
  }

  const isProduction = environment === 'production';
  const secretIsLive = secretValidation.keyType === 'live';
  const publicIsLive = publicValidation.keyType === 'live';

  // Check if key types match
  if (secretValidation.keyType !== publicValidation.keyType) {
    warnings.push('Secret key and public key types do not match (test vs live)');
  }

  // Check if environment matches key type
  if (isProduction && !secretIsLive) {
    warnings.push('Using test keys in production environment');
  }

  if (!isProduction && secretIsLive) {
    warnings.push('Using live keys in non-production environment');
  }

  return {
    isValid: warnings.length === 0,
    warnings
  };
}

/**
 * Masks sensitive parts of API keys for logging
 */
export function maskApiKey(key: string): string {
  if (!key || typeof key !== 'string') {
    return '[INVALID_KEY]';
  }

  if (key.length < 10) {
    return '[REDACTED]';
  }

  // Show first 8 characters and last 4 characters
  const start = key.substring(0, 8);
  const end = key.substring(key.length - 4);
  const middle = '*'.repeat(Math.max(0, key.length - 12));

  return `${start}${middle}${end}`;
}

/**
 * Logs API key validation results securely
 */
export function logApiKeyValidation(
  keyType: 'secret' | 'public',
  validation: ApiKeyValidation,
  environment: string
): void {
  if (validation.isValid) {
    secureLogger.info(`${keyType} key validation passed`, {
      keyType: validation.keyType,
      environment
    });
  } else {
    secureLogger.error(`${keyType} key validation failed`, {
      errors: validation.errors,
      environment
    });
  }
}

/**
 * Comprehensive API key setup validation
 */
export function validateApiKeySetup(config: {
  secretKey: string;
  publicKey: string;
  environment: string;
}): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate individual keys
  const secretValidation = validatePaystackSecretKey(config.secretKey);
  const publicValidation = validatePaystackPublicKey(config.publicKey);

  if (!secretValidation.isValid) {
    errors.push(...secretValidation.errors.map(e => `Secret key: ${e}`));
  }

  if (!publicValidation.isValid) {
    errors.push(...publicValidation.errors.map(e => `Public key: ${e}`));
  }

  // If individual validations pass, check consistency
  if (secretValidation.isValid && publicValidation.isValid) {
    const consistencyCheck = validateApiKeyEnvironmentConsistency(
      config.secretKey,
      config.publicKey,
      config.environment
    );

    if (!consistencyCheck.isValid) {
      warnings.push(...consistencyCheck.warnings);
    }
  }

  // Log results
  logApiKeyValidation('secret', secretValidation, config.environment);
  logApiKeyValidation('public', publicValidation, config.environment);

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}