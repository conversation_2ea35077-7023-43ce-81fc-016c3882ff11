<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1755076118971" clover="3.2.0">
  <project timestamp="1755076118971" name="All files">
    <metrics statements="3273" coveredstatements="337" conditionals="104" coveredconditionals="57" methods="35" coveredmethods="17" elements="3412" coveredelements="411" complexity="0" loc="3273" ncloc="3273" packages="6" files="20" classes="20"/>
    <package name="server">
      <metrics statements="146" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="backfill-user-profiles.js" path="C:\Users\<USER>\Documents\secquiz\server\backfill-user-profiles.js">
        <metrics statements="67" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="C:\Users\<USER>\Documents\secquiz\server\index.ts">
        <metrics statements="79" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
      </file>
    </package>
    <package name="server.api">
      <metrics statements="42" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="check-expired-subscriptions.js" path="C:\Users\<USER>\Documents\secquiz\server\api\check-expired-subscriptions.js">
        <metrics statements="42" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
      </file>
    </package>
    <package name="server.lib">
      <metrics statements="686" coveredstatements="0" conditionals="6" coveredconditionals="3" methods="6" coveredmethods="3"/>
      <file name="api-key-manager.ts" path="C:\Users\<USER>\Documents\secquiz\server\lib\api-key-manager.ts">
        <metrics statements="154" coveredstatements="0" conditionals="1" coveredconditionals="1" methods="1" coveredmethods="1"/>
        <line num="5" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
      </file>
      <file name="config.ts" path="C:\Users\<USER>\Documents\secquiz\server\lib\config.ts">
        <metrics statements="85" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="4" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
      </file>
      <file name="env-validator.ts" path="C:\Users\<USER>\Documents\secquiz\server\lib\env-validator.ts">
        <metrics statements="194" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
      </file>
      <file name="input-validator.ts" path="C:\Users\<USER>\Documents\secquiz\server\lib\input-validator.ts">
        <metrics statements="159" coveredstatements="0" conditionals="1" coveredconditionals="1" methods="1" coveredmethods="1"/>
        <line num="5" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
      </file>
      <file name="secure-logger.ts" path="C:\Users\<USER>\Documents\secquiz\server\lib\secure-logger.ts">
        <metrics statements="90" coveredstatements="0" conditionals="1" coveredconditionals="1" methods="1" coveredmethods="1"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
      </file>
      <file name="supabase.ts" path="C:\Users\<USER>\Documents\secquiz\server\lib\supabase.ts">
        <metrics statements="4" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
      </file>
    </package>
    <package name="server.middleware">
      <metrics statements="579" coveredstatements="0" conditionals="6" coveredconditionals="6" methods="6" coveredmethods="6"/>
      <file name="cors-config.ts" path="C:\Users\<USER>\Documents\secquiz\server\middleware\cors-config.ts">
        <metrics statements="94" coveredstatements="0" conditionals="1" coveredconditionals="1" methods="1" coveredmethods="1"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
      </file>
      <file name="csp.ts" path="C:\Users\<USER>\Documents\secquiz\server\middleware\csp.ts">
        <metrics statements="47" coveredstatements="0" conditionals="1" coveredconditionals="1" methods="1" coveredmethods="1"/>
        <line num="6" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
      </file>
      <file name="error-handler.ts" path="C:\Users\<USER>\Documents\secquiz\server\middleware\error-handler.ts">
        <metrics statements="21" coveredstatements="0" conditionals="1" coveredconditionals="1" methods="1" coveredmethods="1"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
      </file>
      <file name="rate-limiter.ts" path="C:\Users\<USER>\Documents\secquiz\server\middleware\rate-limiter.ts">
        <metrics statements="104" coveredstatements="0" conditionals="1" coveredconditionals="1" methods="1" coveredmethods="1"/>
        <line num="6" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
      </file>
      <file name="request-validator.ts" path="C:\Users\<USER>\Documents\secquiz\server\middleware\request-validator.ts">
        <metrics statements="189" coveredstatements="0" conditionals="1" coveredconditionals="1" methods="1" coveredmethods="1"/>
        <line num="6" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
      </file>
      <file name="security-logger.ts" path="C:\Users\<USER>\Documents\secquiz\server\middleware\security-logger.ts">
        <metrics statements="124" coveredstatements="0" conditionals="1" coveredconditionals="1" methods="1" coveredmethods="1"/>
        <line num="6" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
      </file>
    </package>
    <package name="server.routes">
      <metrics statements="995" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="payments.ts" path="C:\Users\<USER>\Documents\secquiz\server\routes\payments.ts">
        <metrics statements="315" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="304" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="315" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="317" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="323" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="330" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
        <line num="332" count="0" type="stmt"/>
        <line num="333" count="0" type="stmt"/>
        <line num="334" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="338" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="340" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="343" count="0" type="stmt"/>
        <line num="344" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="346" count="0" type="stmt"/>
        <line num="347" count="0" type="stmt"/>
        <line num="348" count="0" type="stmt"/>
        <line num="349" count="0" type="stmt"/>
        <line num="351" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="353" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="355" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="357" count="0" type="stmt"/>
        <line num="358" count="0" type="stmt"/>
        <line num="359" count="0" type="stmt"/>
        <line num="360" count="0" type="stmt"/>
        <line num="361" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="365" count="0" type="stmt"/>
        <line num="366" count="0" type="stmt"/>
        <line num="367" count="0" type="stmt"/>
        <line num="368" count="0" type="stmt"/>
        <line num="369" count="0" type="stmt"/>
        <line num="370" count="0" type="stmt"/>
        <line num="371" count="0" type="stmt"/>
        <line num="372" count="0" type="stmt"/>
        <line num="373" count="0" type="stmt"/>
        <line num="374" count="0" type="stmt"/>
        <line num="375" count="0" type="stmt"/>
        <line num="376" count="0" type="stmt"/>
        <line num="377" count="0" type="stmt"/>
        <line num="378" count="0" type="stmt"/>
        <line num="379" count="0" type="stmt"/>
        <line num="380" count="0" type="stmt"/>
        <line num="381" count="0" type="stmt"/>
        <line num="382" count="0" type="stmt"/>
        <line num="383" count="0" type="stmt"/>
        <line num="385" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="387" count="0" type="stmt"/>
        <line num="388" count="0" type="stmt"/>
        <line num="389" count="0" type="stmt"/>
        <line num="390" count="0" type="stmt"/>
        <line num="391" count="0" type="stmt"/>
        <line num="392" count="0" type="stmt"/>
        <line num="394" count="0" type="stmt"/>
      </file>
      <file name="paystack-proxy.ts" path="C:\Users\<USER>\Documents\secquiz\server\routes\paystack-proxy.ts">
        <metrics statements="65" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="2" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
      </file>
      <file name="subscriptions.ts" path="C:\Users\<USER>\Documents\secquiz\server\routes\subscriptions.ts">
        <metrics statements="32" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
      </file>
      <file name="webhooks.ts" path="C:\Users\<USER>\Documents\secquiz\server\routes\webhooks.ts">
        <metrics statements="583" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="304" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="315" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="317" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="323" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="330" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
        <line num="332" count="0" type="stmt"/>
        <line num="333" count="0" type="stmt"/>
        <line num="334" count="0" type="stmt"/>
        <line num="335" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="338" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="340" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="343" count="0" type="stmt"/>
        <line num="344" count="0" type="stmt"/>
        <line num="346" count="0" type="stmt"/>
        <line num="347" count="0" type="stmt"/>
        <line num="348" count="0" type="stmt"/>
        <line num="350" count="0" type="stmt"/>
        <line num="351" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="353" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="355" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="359" count="0" type="stmt"/>
        <line num="361" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
        <line num="365" count="0" type="stmt"/>
        <line num="366" count="0" type="stmt"/>
        <line num="367" count="0" type="stmt"/>
        <line num="370" count="0" type="stmt"/>
        <line num="371" count="0" type="stmt"/>
        <line num="372" count="0" type="stmt"/>
        <line num="373" count="0" type="stmt"/>
        <line num="374" count="0" type="stmt"/>
        <line num="376" count="0" type="stmt"/>
        <line num="377" count="0" type="stmt"/>
        <line num="378" count="0" type="stmt"/>
        <line num="379" count="0" type="stmt"/>
        <line num="380" count="0" type="stmt"/>
        <line num="382" count="0" type="stmt"/>
        <line num="383" count="0" type="stmt"/>
        <line num="384" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="387" count="0" type="stmt"/>
        <line num="389" count="0" type="stmt"/>
        <line num="390" count="0" type="stmt"/>
        <line num="392" count="0" type="stmt"/>
        <line num="393" count="0" type="stmt"/>
        <line num="395" count="0" type="stmt"/>
        <line num="396" count="0" type="stmt"/>
        <line num="398" count="0" type="stmt"/>
        <line num="399" count="0" type="stmt"/>
        <line num="401" count="0" type="stmt"/>
        <line num="402" count="0" type="stmt"/>
        <line num="404" count="0" type="stmt"/>
        <line num="405" count="0" type="stmt"/>
        <line num="407" count="0" type="stmt"/>
        <line num="408" count="0" type="stmt"/>
        <line num="410" count="0" type="stmt"/>
        <line num="411" count="0" type="stmt"/>
        <line num="412" count="0" type="stmt"/>
        <line num="413" count="0" type="stmt"/>
        <line num="414" count="0" type="stmt"/>
        <line num="415" count="0" type="stmt"/>
        <line num="416" count="0" type="stmt"/>
        <line num="417" count="0" type="stmt"/>
        <line num="418" count="0" type="stmt"/>
        <line num="421" count="0" type="stmt"/>
        <line num="422" count="0" type="stmt"/>
        <line num="423" count="0" type="stmt"/>
        <line num="425" count="0" type="stmt"/>
        <line num="426" count="0" type="stmt"/>
        <line num="427" count="0" type="stmt"/>
        <line num="428" count="0" type="stmt"/>
        <line num="429" count="0" type="stmt"/>
        <line num="430" count="0" type="stmt"/>
        <line num="431" count="0" type="stmt"/>
        <line num="432" count="0" type="stmt"/>
        <line num="434" count="0" type="stmt"/>
        <line num="435" count="0" type="stmt"/>
        <line num="436" count="0" type="stmt"/>
        <line num="437" count="0" type="stmt"/>
        <line num="438" count="0" type="stmt"/>
        <line num="439" count="0" type="stmt"/>
        <line num="440" count="0" type="stmt"/>
        <line num="441" count="0" type="stmt"/>
        <line num="443" count="0" type="stmt"/>
        <line num="444" count="0" type="stmt"/>
        <line num="445" count="0" type="stmt"/>
        <line num="446" count="0" type="stmt"/>
        <line num="447" count="0" type="stmt"/>
        <line num="448" count="0" type="stmt"/>
        <line num="449" count="0" type="stmt"/>
        <line num="450" count="0" type="stmt"/>
        <line num="453" count="0" type="stmt"/>
        <line num="455" count="0" type="stmt"/>
        <line num="457" count="0" type="stmt"/>
        <line num="458" count="0" type="stmt"/>
        <line num="459" count="0" type="stmt"/>
        <line num="460" count="0" type="stmt"/>
        <line num="461" count="0" type="stmt"/>
        <line num="462" count="0" type="stmt"/>
        <line num="464" count="0" type="stmt"/>
        <line num="465" count="0" type="stmt"/>
        <line num="466" count="0" type="stmt"/>
        <line num="468" count="0" type="stmt"/>
        <line num="469" count="0" type="stmt"/>
        <line num="470" count="0" type="stmt"/>
        <line num="471" count="0" type="stmt"/>
        <line num="474" count="0" type="stmt"/>
        <line num="475" count="0" type="stmt"/>
        <line num="476" count="0" type="stmt"/>
        <line num="477" count="0" type="stmt"/>
        <line num="478" count="0" type="stmt"/>
        <line num="479" count="0" type="stmt"/>
        <line num="480" count="0" type="stmt"/>
        <line num="481" count="0" type="stmt"/>
        <line num="483" count="0" type="stmt"/>
        <line num="484" count="0" type="stmt"/>
        <line num="485" count="0" type="stmt"/>
        <line num="486" count="0" type="stmt"/>
        <line num="487" count="0" type="stmt"/>
        <line num="488" count="0" type="stmt"/>
        <line num="489" count="0" type="stmt"/>
        <line num="492" count="0" type="stmt"/>
        <line num="493" count="0" type="stmt"/>
        <line num="494" count="0" type="stmt"/>
        <line num="495" count="0" type="stmt"/>
        <line num="496" count="0" type="stmt"/>
        <line num="497" count="0" type="stmt"/>
        <line num="499" count="0" type="stmt"/>
        <line num="500" count="0" type="stmt"/>
        <line num="501" count="0" type="stmt"/>
        <line num="502" count="0" type="stmt"/>
        <line num="503" count="0" type="stmt"/>
        <line num="504" count="0" type="stmt"/>
        <line num="505" count="0" type="stmt"/>
        <line num="507" count="0" type="stmt"/>
        <line num="508" count="0" type="stmt"/>
        <line num="509" count="0" type="stmt"/>
        <line num="510" count="0" type="stmt"/>
        <line num="511" count="0" type="stmt"/>
        <line num="512" count="0" type="stmt"/>
        <line num="513" count="0" type="stmt"/>
        <line num="514" count="0" type="stmt"/>
        <line num="515" count="0" type="stmt"/>
        <line num="516" count="0" type="stmt"/>
        <line num="517" count="0" type="stmt"/>
        <line num="518" count="0" type="stmt"/>
        <line num="520" count="0" type="stmt"/>
        <line num="521" count="0" type="stmt"/>
        <line num="522" count="0" type="stmt"/>
        <line num="523" count="0" type="stmt"/>
        <line num="524" count="0" type="stmt"/>
        <line num="525" count="0" type="stmt"/>
        <line num="526" count="0" type="stmt"/>
        <line num="527" count="0" type="stmt"/>
        <line num="528" count="0" type="stmt"/>
        <line num="529" count="0" type="stmt"/>
        <line num="530" count="0" type="stmt"/>
        <line num="531" count="0" type="stmt"/>
        <line num="532" count="0" type="stmt"/>
        <line num="534" count="0" type="stmt"/>
        <line num="535" count="0" type="stmt"/>
        <line num="536" count="0" type="stmt"/>
        <line num="537" count="0" type="stmt"/>
        <line num="538" count="0" type="stmt"/>
        <line num="539" count="0" type="stmt"/>
        <line num="540" count="0" type="stmt"/>
        <line num="541" count="0" type="stmt"/>
        <line num="544" count="0" type="stmt"/>
        <line num="545" count="0" type="stmt"/>
        <line num="546" count="0" type="stmt"/>
        <line num="547" count="0" type="stmt"/>
        <line num="548" count="0" type="stmt"/>
        <line num="549" count="0" type="stmt"/>
        <line num="550" count="0" type="stmt"/>
        <line num="555" count="0" type="stmt"/>
        <line num="556" count="0" type="stmt"/>
        <line num="557" count="0" type="stmt"/>
        <line num="558" count="0" type="stmt"/>
        <line num="559" count="0" type="stmt"/>
        <line num="560" count="0" type="stmt"/>
        <line num="563" count="0" type="stmt"/>
        <line num="564" count="0" type="stmt"/>
        <line num="565" count="0" type="stmt"/>
        <line num="566" count="0" type="stmt"/>
        <line num="567" count="0" type="stmt"/>
        <line num="568" count="0" type="stmt"/>
        <line num="570" count="0" type="stmt"/>
        <line num="571" count="0" type="stmt"/>
        <line num="572" count="0" type="stmt"/>
        <line num="573" count="0" type="stmt"/>
        <line num="574" count="0" type="stmt"/>
        <line num="575" count="0" type="stmt"/>
        <line num="578" count="0" type="stmt"/>
        <line num="579" count="0" type="stmt"/>
        <line num="580" count="0" type="stmt"/>
        <line num="581" count="0" type="stmt"/>
        <line num="582" count="0" type="stmt"/>
        <line num="583" count="0" type="stmt"/>
        <line num="584" count="0" type="stmt"/>
        <line num="587" count="0" type="stmt"/>
        <line num="588" count="0" type="stmt"/>
        <line num="589" count="0" type="stmt"/>
        <line num="590" count="0" type="stmt"/>
        <line num="591" count="0" type="stmt"/>
        <line num="593" count="0" type="stmt"/>
        <line num="594" count="0" type="stmt"/>
        <line num="595" count="0" type="stmt"/>
        <line num="596" count="0" type="stmt"/>
        <line num="597" count="0" type="stmt"/>
        <line num="598" count="0" type="stmt"/>
        <line num="599" count="0" type="stmt"/>
        <line num="600" count="0" type="stmt"/>
        <line num="601" count="0" type="stmt"/>
        <line num="602" count="0" type="stmt"/>
        <line num="603" count="0" type="stmt"/>
        <line num="604" count="0" type="stmt"/>
        <line num="605" count="0" type="stmt"/>
        <line num="607" count="0" type="stmt"/>
        <line num="609" count="0" type="stmt"/>
        <line num="610" count="0" type="stmt"/>
        <line num="611" count="0" type="stmt"/>
        <line num="612" count="0" type="stmt"/>
        <line num="613" count="0" type="stmt"/>
        <line num="614" count="0" type="stmt"/>
        <line num="615" count="0" type="stmt"/>
        <line num="617" count="0" type="stmt"/>
        <line num="618" count="0" type="stmt"/>
        <line num="619" count="0" type="stmt"/>
        <line num="620" count="0" type="stmt"/>
        <line num="621" count="0" type="stmt"/>
        <line num="622" count="0" type="stmt"/>
        <line num="623" count="0" type="stmt"/>
        <line num="624" count="0" type="stmt"/>
        <line num="625" count="0" type="stmt"/>
        <line num="626" count="0" type="stmt"/>
        <line num="627" count="0" type="stmt"/>
        <line num="628" count="0" type="stmt"/>
        <line num="629" count="0" type="stmt"/>
        <line num="632" count="0" type="stmt"/>
        <line num="633" count="0" type="stmt"/>
        <line num="634" count="0" type="stmt"/>
        <line num="635" count="0" type="stmt"/>
        <line num="636" count="0" type="stmt"/>
        <line num="637" count="0" type="stmt"/>
        <line num="638" count="0" type="stmt"/>
        <line num="640" count="0" type="stmt"/>
        <line num="641" count="0" type="stmt"/>
        <line num="642" count="0" type="stmt"/>
        <line num="643" count="0" type="stmt"/>
        <line num="644" count="0" type="stmt"/>
        <line num="645" count="0" type="stmt"/>
        <line num="646" count="0" type="stmt"/>
        <line num="648" count="0" type="stmt"/>
        <line num="649" count="0" type="stmt"/>
        <line num="650" count="0" type="stmt"/>
        <line num="651" count="0" type="stmt"/>
        <line num="652" count="0" type="stmt"/>
        <line num="653" count="0" type="stmt"/>
        <line num="654" count="0" type="stmt"/>
        <line num="656" count="0" type="stmt"/>
        <line num="657" count="0" type="stmt"/>
        <line num="658" count="0" type="stmt"/>
        <line num="659" count="0" type="stmt"/>
        <line num="660" count="0" type="stmt"/>
        <line num="661" count="0" type="stmt"/>
        <line num="662" count="0" type="stmt"/>
        <line num="663" count="0" type="stmt"/>
        <line num="664" count="0" type="stmt"/>
        <line num="665" count="0" type="stmt"/>
        <line num="666" count="0" type="stmt"/>
        <line num="668" count="0" type="stmt"/>
        <line num="669" count="0" type="stmt"/>
        <line num="670" count="0" type="stmt"/>
        <line num="671" count="0" type="stmt"/>
        <line num="672" count="0" type="stmt"/>
        <line num="673" count="0" type="stmt"/>
        <line num="674" count="0" type="stmt"/>
        <line num="675" count="0" type="stmt"/>
        <line num="678" count="0" type="stmt"/>
        <line num="679" count="0" type="stmt"/>
        <line num="680" count="0" type="stmt"/>
        <line num="681" count="0" type="stmt"/>
        <line num="682" count="0" type="stmt"/>
        <line num="684" count="0" type="stmt"/>
        <line num="685" count="0" type="stmt"/>
        <line num="686" count="0" type="stmt"/>
        <line num="687" count="0" type="stmt"/>
        <line num="688" count="0" type="stmt"/>
        <line num="689" count="0" type="stmt"/>
        <line num="692" count="0" type="stmt"/>
        <line num="693" count="0" type="stmt"/>
        <line num="694" count="0" type="stmt"/>
        <line num="695" count="0" type="stmt"/>
        <line num="696" count="0" type="stmt"/>
        <line num="697" count="0" type="stmt"/>
        <line num="699" count="0" type="stmt"/>
        <line num="700" count="0" type="stmt"/>
        <line num="701" count="0" type="stmt"/>
        <line num="702" count="0" type="stmt"/>
        <line num="703" count="0" type="stmt"/>
        <line num="704" count="0" type="stmt"/>
        <line num="707" count="0" type="stmt"/>
        <line num="708" count="0" type="stmt"/>
        <line num="709" count="0" type="stmt"/>
        <line num="710" count="0" type="stmt"/>
        <line num="711" count="0" type="stmt"/>
        <line num="712" count="0" type="stmt"/>
        <line num="714" count="0" type="stmt"/>
        <line num="715" count="0" type="stmt"/>
        <line num="716" count="0" type="stmt"/>
        <line num="717" count="0" type="stmt"/>
        <line num="718" count="0" type="stmt"/>
        <line num="719" count="0" type="stmt"/>
        <line num="722" count="0" type="stmt"/>
        <line num="723" count="0" type="stmt"/>
        <line num="724" count="0" type="stmt"/>
        <line num="725" count="0" type="stmt"/>
        <line num="726" count="0" type="stmt"/>
        <line num="728" count="0" type="stmt"/>
        <line num="729" count="0" type="stmt"/>
        <line num="730" count="0" type="stmt"/>
        <line num="731" count="0" type="stmt"/>
        <line num="732" count="0" type="stmt"/>
        <line num="733" count="0" type="stmt"/>
        <line num="736" count="0" type="stmt"/>
        <line num="737" count="0" type="stmt"/>
        <line num="738" count="0" type="stmt"/>
        <line num="739" count="0" type="stmt"/>
        <line num="740" count="0" type="stmt"/>
        <line num="741" count="0" type="stmt"/>
        <line num="743" count="0" type="stmt"/>
        <line num="744" count="0" type="stmt"/>
        <line num="745" count="0" type="stmt"/>
        <line num="746" count="0" type="stmt"/>
        <line num="747" count="0" type="stmt"/>
        <line num="748" count="0" type="stmt"/>
        <line num="750" count="0" type="stmt"/>
      </file>
    </package>
    <package name="server.services">
      <metrics statements="825" coveredstatements="337" conditionals="85" coveredconditionals="48" methods="16" coveredmethods="8"/>
      <file name="subscription.ts" path="C:\Users\<USER>\Documents\secquiz\server\services\subscription.ts">
        <metrics statements="825" coveredstatements="337" conditionals="85" coveredconditionals="48" methods="16" coveredmethods="8"/>
        <line num="1" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="59" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="60" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="77" count="13" type="cond" truecount="1" falsecount="0"/>
        <line num="79" count="13" type="cond" truecount="4" falsecount="0"/>
        <line num="80" count="1" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="88" count="12" type="stmt"/>
        <line num="89" count="13" type="cond" truecount="1" falsecount="0"/>
        <line num="90" count="1" type="stmt"/>
        <line num="91" count="1" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="93" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
        <line num="95" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="98" count="11" type="stmt"/>
        <line num="99" count="13" type="cond" truecount="1" falsecount="0"/>
        <line num="100" count="1" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="105" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="108" count="13" type="cond" truecount="1" falsecount="0"/>
        <line num="109" count="1" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="111" count="1" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="113" count="1" type="stmt"/>
        <line num="114" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="116" count="9" type="stmt"/>
        <line num="117" count="9" type="stmt"/>
        <line num="120" count="9" type="stmt"/>
        <line num="121" count="9" type="stmt"/>
        <line num="122" count="9" type="stmt"/>
        <line num="124" count="13" type="cond" truecount="0" falsecount="1"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="cond" truecount="1" falsecount="0"/>
        <line num="133" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="134" count="13" type="cond" truecount="1" falsecount="0"/>
        <line num="135" count="1" type="stmt"/>
        <line num="136" count="1" type="stmt"/>
        <line num="137" count="1" type="stmt"/>
        <line num="138" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="141" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="143" count="8" type="stmt"/>
        <line num="144" count="13" type="cond" truecount="0" falsecount="1"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="cond" truecount="1" falsecount="0"/>
        <line num="154" count="8" type="stmt"/>
        <line num="155" count="8" type="stmt"/>
        <line num="156" count="8" type="stmt"/>
        <line num="157" count="8" type="stmt"/>
        <line num="158" count="8" type="stmt"/>
        <line num="159" count="8" type="stmt"/>
        <line num="160" count="8" type="stmt"/>
        <line num="162" count="13" type="cond" truecount="1" falsecount="0"/>
        <line num="163" count="3" type="stmt"/>
        <line num="166" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="167" count="1" type="stmt"/>
        <line num="168" count="1" type="stmt"/>
        <line num="169" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="171" count="2" type="stmt"/>
        <line num="172" count="2" type="stmt"/>
        <line num="173" count="2" type="stmt"/>
        <line num="174" count="2" type="stmt"/>
        <line num="175" count="2" type="stmt"/>
        <line num="176" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="179" count="13" type="cond" truecount="0" falsecount="1"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="cond" truecount="1" falsecount="0"/>
        <line num="188" count="13" type="cond" truecount="1" falsecount="0"/>
        <line num="189" count="2" type="stmt"/>
        <line num="192" count="2" type="stmt"/>
        <line num="193" count="2" type="stmt"/>
        <line num="194" count="2" type="stmt"/>
        <line num="195" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="199" count="2" type="stmt"/>
        <line num="200" count="2" type="stmt"/>
        <line num="201" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="202" count="2" type="stmt"/>
        <line num="203" count="2" type="stmt"/>
        <line num="204" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="206" count="13" type="stmt"/>
        <line num="207" count="13" type="stmt"/>
        <line num="208" count="13" type="stmt"/>
        <line num="209" count="13" type="stmt"/>
        <line num="210" count="13" type="stmt"/>
        <line num="211" count="13" type="stmt"/>
        <line num="212" count="13" type="stmt"/>
        <line num="213" count="13" type="stmt"/>
        <line num="214" count="13" type="stmt"/>
        <line num="215" count="13" type="stmt"/>
        <line num="216" count="13" type="stmt"/>
        <line num="217" count="13" type="stmt"/>
        <line num="219" count="13" type="cond" truecount="0" falsecount="1"/>
        <line num="220" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="13" type="stmt"/>
        <line num="248" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="249" count="1" type="stmt"/>
        <line num="251" count="1" type="stmt"/>
        <line num="253" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="254" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="262" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="264" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="265" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="272" count="1" type="stmt"/>
        <line num="274" count="1" type="stmt"/>
        <line num="277" count="1" type="stmt"/>
        <line num="278" count="1" type="stmt"/>
        <line num="279" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="281" count="1" type="stmt"/>
        <line num="284" count="1" type="stmt"/>
        <line num="285" count="1" type="stmt"/>
        <line num="286" count="1" type="stmt"/>
        <line num="287" count="1" type="stmt"/>
        <line num="288" count="1" type="stmt"/>
        <line num="290" count="1" type="stmt"/>
        <line num="292" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="293" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="304" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="307" count="1" type="stmt"/>
        <line num="308" count="1" type="stmt"/>
        <line num="310" count="1" type="stmt"/>
        <line num="311" count="1" type="stmt"/>
        <line num="312" count="1" type="stmt"/>
        <line num="313" count="1" type="stmt"/>
        <line num="314" count="1" type="stmt"/>
        <line num="315" count="1" type="stmt"/>
        <line num="316" count="1" type="stmt"/>
        <line num="317" count="1" type="stmt"/>
        <line num="318" count="1" type="stmt"/>
        <line num="319" count="1" type="stmt"/>
        <line num="320" count="1" type="stmt"/>
        <line num="321" count="1" type="stmt"/>
        <line num="322" count="1" type="stmt"/>
        <line num="323" count="1" type="stmt"/>
        <line num="325" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="326" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="330" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
        <line num="333" count="1" type="stmt"/>
        <line num="336" count="1" type="stmt"/>
        <line num="337" count="1" type="stmt"/>
        <line num="338" count="1" type="stmt"/>
        <line num="339" count="1" type="stmt"/>
        <line num="340" count="1" type="stmt"/>
        <line num="341" count="1" type="stmt"/>
        <line num="342" count="1" type="stmt"/>
        <line num="343" count="1" type="stmt"/>
        <line num="344" count="1" type="stmt"/>
        <line num="345" count="1" type="stmt"/>
        <line num="346" count="1" type="stmt"/>
        <line num="348" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="349" count="0" type="stmt"/>
        <line num="351" count="1" type="stmt"/>
        <line num="352" count="1" type="stmt"/>
        <line num="353" count="1" type="stmt"/>
        <line num="355" count="1" type="stmt"/>
        <line num="358" count="1" type="stmt"/>
        <line num="359" count="1" type="stmt"/>
        <line num="360" count="1" type="stmt"/>
        <line num="361" count="1" type="stmt"/>
        <line num="362" count="1" type="stmt"/>
        <line num="364" count="1" type="stmt"/>
        <line num="365" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="366" count="0" type="stmt"/>
        <line num="368" count="0" type="stmt"/>
        <line num="369" count="0" type="stmt"/>
        <line num="370" count="0" type="stmt"/>
        <line num="371" count="0" type="stmt"/>
        <line num="372" count="0" type="stmt"/>
        <line num="373" count="0" type="stmt"/>
        <line num="374" count="0" type="stmt"/>
        <line num="375" count="1" type="stmt"/>
        <line num="376" count="1" type="stmt"/>
        <line num="378" count="1" type="stmt"/>
        <line num="379" count="1" type="stmt"/>
        <line num="380" count="1" type="stmt"/>
        <line num="381" count="1" type="stmt"/>
        <line num="382" count="1" type="stmt"/>
        <line num="383" count="1" type="stmt"/>
        <line num="384" count="1" type="stmt"/>
        <line num="385" count="1" type="stmt"/>
        <line num="387" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="388" count="0" type="stmt"/>
        <line num="390" count="1" type="stmt"/>
        <line num="391" count="1" type="stmt"/>
        <line num="392" count="1" type="stmt"/>
        <line num="394" count="1" type="stmt"/>
        <line num="396" count="1" type="stmt"/>
        <line num="397" count="1" type="stmt"/>
        <line num="398" count="1" type="stmt"/>
        <line num="399" count="1" type="stmt"/>
        <line num="400" count="1" type="stmt"/>
        <line num="401" count="1" type="stmt"/>
        <line num="402" count="1" type="stmt"/>
        <line num="403" count="1" type="stmt"/>
        <line num="404" count="1" type="stmt"/>
        <line num="405" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="406" count="0" type="stmt"/>
        <line num="407" count="0" type="stmt"/>
        <line num="408" count="0" type="stmt"/>
        <line num="409" count="0" type="stmt"/>
        <line num="410" count="0" type="stmt"/>
        <line num="411" count="0" type="stmt"/>
        <line num="412" count="1" type="stmt"/>
        <line num="414" count="0" type="stmt"/>
        <line num="415" count="0" type="stmt"/>
        <line num="416" count="0" type="stmt"/>
        <line num="417" count="0" type="stmt"/>
        <line num="418" count="0" type="stmt"/>
        <line num="419" count="0" type="stmt"/>
        <line num="422" count="0" type="stmt"/>
        <line num="424" count="0" type="stmt"/>
        <line num="425" count="0" type="stmt"/>
        <line num="426" count="0" type="stmt"/>
        <line num="427" count="0" type="stmt"/>
        <line num="429" count="0" type="stmt"/>
        <line num="431" count="0" type="stmt"/>
        <line num="432" count="0" type="stmt"/>
        <line num="433" count="0" type="stmt"/>
        <line num="434" count="0" type="stmt"/>
        <line num="435" count="0" type="stmt"/>
        <line num="440" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="442" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="443" count="0" type="stmt"/>
        <line num="444" count="0" type="stmt"/>
        <line num="445" count="0" type="stmt"/>
        <line num="446" count="0" type="stmt"/>
        <line num="447" count="0" type="stmt"/>
        <line num="448" count="0" type="stmt"/>
        <line num="450" count="2" type="stmt"/>
        <line num="452" count="2" type="stmt"/>
        <line num="453" count="2" type="stmt"/>
        <line num="454" count="2" type="stmt"/>
        <line num="456" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="457" count="0" type="stmt"/>
        <line num="460" count="0" type="stmt"/>
        <line num="461" count="0" type="stmt"/>
        <line num="462" count="0" type="stmt"/>
        <line num="464" count="0" type="stmt"/>
        <line num="465" count="0" type="stmt"/>
        <line num="466" count="0" type="stmt"/>
        <line num="467" count="0" type="stmt"/>
        <line num="468" count="0" type="stmt"/>
        <line num="469" count="0" type="stmt"/>
        <line num="471" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="472" count="0" type="stmt"/>
        <line num="473" count="0" type="stmt"/>
        <line num="474" count="0" type="stmt"/>
        <line num="475" count="0" type="stmt"/>
        <line num="476" count="0" type="stmt"/>
        <line num="477" count="0" type="stmt"/>
        <line num="480" count="2" type="stmt"/>
        <line num="481" count="2" type="stmt"/>
        <line num="483" count="2" type="stmt"/>
        <line num="484" count="2" type="stmt"/>
        <line num="485" count="2" type="stmt"/>
        <line num="486" count="2" type="stmt"/>
        <line num="487" count="2" type="stmt"/>
        <line num="489" count="2" type="stmt"/>
        <line num="490" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="491" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="492" count="2" type="stmt"/>
        <line num="493" count="2" type="stmt"/>
        <line num="494" count="2" type="stmt"/>
        <line num="496" count="2" type="stmt"/>
        <line num="497" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="498" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="500" count="1" type="stmt"/>
        <line num="501" count="1" type="stmt"/>
        <line num="502" count="1" type="stmt"/>
        <line num="503" count="1" type="stmt"/>
        <line num="504" count="2" type="stmt"/>
        <line num="506" count="2" type="stmt"/>
        <line num="507" count="2" type="stmt"/>
        <line num="508" count="2" type="stmt"/>
        <line num="509" count="2" type="stmt"/>
        <line num="510" count="2" type="stmt"/>
        <line num="511" count="2" type="stmt"/>
        <line num="512" count="2" type="stmt"/>
        <line num="513" count="2" type="stmt"/>
        <line num="514" count="2" type="stmt"/>
        <line num="515" count="2" type="stmt"/>
        <line num="516" count="2" type="stmt"/>
        <line num="518" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="519" count="0" type="stmt"/>
        <line num="521" count="0" type="stmt"/>
        <line num="522" count="0" type="stmt"/>
        <line num="523" count="0" type="stmt"/>
        <line num="524" count="0" type="stmt"/>
        <line num="525" count="0" type="stmt"/>
        <line num="526" count="0" type="stmt"/>
        <line num="527" count="0" type="stmt"/>
        <line num="528" count="0" type="stmt"/>
        <line num="530" count="0" type="stmt"/>
        <line num="531" count="0" type="stmt"/>
        <line num="532" count="0" type="stmt"/>
        <line num="533" count="0" type="stmt"/>
        <line num="534" count="0" type="stmt"/>
        <line num="535" count="0" type="stmt"/>
        <line num="536" count="2" type="stmt"/>
        <line num="541" count="0" type="stmt"/>
        <line num="542" count="0" type="stmt"/>
        <line num="544" count="0" type="stmt"/>
        <line num="545" count="0" type="stmt"/>
        <line num="546" count="0" type="stmt"/>
        <line num="547" count="0" type="stmt"/>
        <line num="548" count="0" type="stmt"/>
        <line num="550" count="0" type="stmt"/>
        <line num="551" count="0" type="stmt"/>
        <line num="552" count="0" type="stmt"/>
        <line num="553" count="0" type="stmt"/>
        <line num="554" count="0" type="stmt"/>
        <line num="555" count="0" type="stmt"/>
        <line num="556" count="0" type="stmt"/>
        <line num="557" count="0" type="stmt"/>
        <line num="559" count="0" type="stmt"/>
        <line num="560" count="0" type="stmt"/>
        <line num="561" count="0" type="stmt"/>
        <line num="562" count="0" type="stmt"/>
        <line num="563" count="0" type="stmt"/>
        <line num="565" count="0" type="stmt"/>
        <line num="566" count="0" type="stmt"/>
        <line num="567" count="0" type="stmt"/>
        <line num="568" count="0" type="stmt"/>
        <line num="569" count="0" type="stmt"/>
        <line num="572" count="0" type="stmt"/>
        <line num="573" count="0" type="stmt"/>
        <line num="574" count="0" type="stmt"/>
        <line num="575" count="0" type="stmt"/>
        <line num="577" count="0" type="stmt"/>
        <line num="579" count="0" type="stmt"/>
        <line num="580" count="0" type="stmt"/>
        <line num="581" count="0" type="stmt"/>
        <line num="582" count="0" type="stmt"/>
        <line num="583" count="0" type="stmt"/>
        <line num="584" count="0" type="stmt"/>
        <line num="586" count="0" type="stmt"/>
        <line num="587" count="0" type="stmt"/>
        <line num="588" count="0" type="stmt"/>
        <line num="589" count="0" type="stmt"/>
        <line num="590" count="0" type="stmt"/>
        <line num="591" count="0" type="stmt"/>
        <line num="592" count="0" type="stmt"/>
        <line num="593" count="0" type="stmt"/>
        <line num="594" count="0" type="stmt"/>
        <line num="595" count="0" type="stmt"/>
        <line num="596" count="0" type="stmt"/>
        <line num="598" count="0" type="stmt"/>
        <line num="599" count="0" type="stmt"/>
        <line num="600" count="0" type="stmt"/>
        <line num="601" count="0" type="stmt"/>
        <line num="602" count="0" type="stmt"/>
        <line num="603" count="0" type="stmt"/>
        <line num="604" count="0" type="stmt"/>
        <line num="605" count="0" type="stmt"/>
        <line num="606" count="0" type="stmt"/>
        <line num="611" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="612" count="3" type="stmt"/>
        <line num="613" count="3" type="stmt"/>
        <line num="614" count="3" type="stmt"/>
        <line num="615" count="3" type="stmt"/>
        <line num="617" count="3" type="cond" truecount="0" falsecount="1"/>
        <line num="618" count="0" type="stmt"/>
        <line num="619" count="0" type="stmt"/>
        <line num="620" count="0" type="stmt"/>
        <line num="621" count="0" type="stmt"/>
        <line num="622" count="0" type="stmt"/>
        <line num="623" count="0" type="stmt"/>
        <line num="625" count="3" type="stmt"/>
        <line num="626" count="3" type="cond" truecount="0" falsecount="1"/>
        <line num="627" count="0" type="stmt"/>
        <line num="628" count="0" type="stmt"/>
        <line num="629" count="0" type="stmt"/>
        <line num="630" count="0" type="stmt"/>
        <line num="631" count="0" type="stmt"/>
        <line num="632" count="0" type="stmt"/>
        <line num="634" count="3" type="stmt"/>
        <line num="635" count="3" type="stmt"/>
        <line num="636" count="3" type="stmt"/>
        <line num="637" count="3" type="stmt"/>
        <line num="638" count="3" type="cond" truecount="0" falsecount="1"/>
        <line num="639" count="3" type="stmt"/>
        <line num="641" count="3" type="cond" truecount="0" falsecount="1"/>
        <line num="642" count="0" type="stmt"/>
        <line num="643" count="0" type="stmt"/>
        <line num="644" count="0" type="stmt"/>
        <line num="645" count="0" type="stmt"/>
        <line num="646" count="0" type="stmt"/>
        <line num="647" count="0" type="stmt"/>
        <line num="648" count="0" type="stmt"/>
        <line num="650" count="3" type="cond" truecount="0" falsecount="1"/>
        <line num="651" count="0" type="stmt"/>
        <line num="652" count="0" type="stmt"/>
        <line num="653" count="0" type="stmt"/>
        <line num="654" count="0" type="stmt"/>
        <line num="655" count="0" type="stmt"/>
        <line num="656" count="0" type="stmt"/>
        <line num="658" count="3" type="stmt"/>
        <line num="659" count="3" type="stmt"/>
        <line num="660" count="3" type="stmt"/>
        <line num="661" count="3" type="stmt"/>
        <line num="662" count="3" type="stmt"/>
        <line num="663" count="3" type="stmt"/>
        <line num="664" count="3" type="stmt"/>
        <line num="665" count="3" type="stmt"/>
        <line num="667" count="3" type="cond" truecount="0" falsecount="1"/>
        <line num="668" count="0" type="stmt"/>
        <line num="669" count="0" type="stmt"/>
        <line num="670" count="0" type="stmt"/>
        <line num="671" count="0" type="stmt"/>
        <line num="672" count="0" type="stmt"/>
        <line num="673" count="0" type="stmt"/>
        <line num="674" count="0" type="stmt"/>
        <line num="675" count="3" type="stmt"/>
        <line num="677" count="0" type="stmt"/>
        <line num="678" count="0" type="stmt"/>
        <line num="679" count="0" type="stmt"/>
        <line num="680" count="0" type="stmt"/>
        <line num="681" count="0" type="stmt"/>
        <line num="682" count="0" type="stmt"/>
        <line num="685" count="0" type="stmt"/>
        <line num="687" count="0" type="stmt"/>
        <line num="688" count="0" type="stmt"/>
        <line num="689" count="0" type="stmt"/>
        <line num="690" count="0" type="stmt"/>
        <line num="692" count="0" type="stmt"/>
        <line num="693" count="0" type="stmt"/>
        <line num="694" count="0" type="stmt"/>
        <line num="695" count="0" type="stmt"/>
        <line num="696" count="0" type="stmt"/>
        <line num="697" count="0" type="stmt"/>
        <line num="698" count="0" type="stmt"/>
        <line num="701" count="0" type="stmt"/>
        <line num="702" count="0" type="stmt"/>
        <line num="703" count="0" type="stmt"/>
        <line num="704" count="0" type="stmt"/>
        <line num="705" count="0" type="stmt"/>
        <line num="706" count="0" type="stmt"/>
        <line num="707" count="0" type="stmt"/>
        <line num="708" count="0" type="stmt"/>
        <line num="709" count="0" type="stmt"/>
        <line num="711" count="0" type="stmt"/>
        <line num="712" count="0" type="stmt"/>
        <line num="713" count="0" type="stmt"/>
        <line num="714" count="0" type="stmt"/>
        <line num="715" count="0" type="stmt"/>
        <line num="716" count="0" type="stmt"/>
        <line num="717" count="0" type="stmt"/>
        <line num="718" count="0" type="stmt"/>
        <line num="719" count="0" type="stmt"/>
        <line num="720" count="0" type="stmt"/>
        <line num="721" count="0" type="stmt"/>
        <line num="722" count="0" type="stmt"/>
        <line num="725" count="0" type="stmt"/>
        <line num="726" count="0" type="stmt"/>
        <line num="727" count="0" type="stmt"/>
        <line num="728" count="0" type="stmt"/>
        <line num="729" count="0" type="stmt"/>
        <line num="730" count="0" type="stmt"/>
        <line num="732" count="0" type="stmt"/>
        <line num="733" count="0" type="stmt"/>
        <line num="734" count="0" type="stmt"/>
        <line num="735" count="0" type="stmt"/>
        <line num="736" count="0" type="stmt"/>
        <line num="741" count="6" type="cond" truecount="1" falsecount="0"/>
        <line num="742" count="6" type="stmt"/>
        <line num="743" count="6" type="stmt"/>
        <line num="746" count="6" type="stmt"/>
        <line num="748" count="6" type="cond" truecount="0" falsecount="1"/>
        <line num="749" count="0" type="stmt"/>
        <line num="752" count="0" type="stmt"/>
        <line num="753" count="0" type="stmt"/>
        <line num="754" count="0" type="stmt"/>
        <line num="756" count="0" type="stmt"/>
        <line num="757" count="0" type="stmt"/>
        <line num="758" count="0" type="stmt"/>
        <line num="759" count="0" type="stmt"/>
        <line num="760" count="0" type="stmt"/>
        <line num="761" count="0" type="stmt"/>
        <line num="763" count="6" type="cond" truecount="1" falsecount="0"/>
        <line num="764" count="4" type="stmt"/>
        <line num="765" count="4" type="stmt"/>
        <line num="766" count="4" type="cond" truecount="0" falsecount="1"/>
        <line num="767" count="4" type="stmt"/>
        <line num="768" count="4" type="stmt"/>
        <line num="769" count="4" type="cond" truecount="1" falsecount="0"/>
        <line num="771" count="2" type="stmt"/>
        <line num="773" count="2" type="stmt"/>
        <line num="774" count="2" type="stmt"/>
        <line num="775" count="2" type="stmt"/>
        <line num="776" count="6" type="cond" truecount="0" falsecount="1"/>
        <line num="777" count="6" type="cond" truecount="0" falsecount="1"/>
        <line num="778" count="6" type="stmt"/>
        <line num="779" count="6" type="stmt"/>
        <line num="781" count="6" type="cond" truecount="0" falsecount="1"/>
        <line num="782" count="0" type="stmt"/>
        <line num="784" count="0" type="stmt"/>
        <line num="785" count="0" type="stmt"/>
        <line num="786" count="0" type="stmt"/>
        <line num="787" count="0" type="stmt"/>
        <line num="788" count="0" type="stmt"/>
        <line num="789" count="0" type="stmt"/>
        <line num="790" count="0" type="stmt"/>
        <line num="791" count="0" type="stmt"/>
        <line num="793" count="0" type="stmt"/>
        <line num="794" count="0" type="stmt"/>
        <line num="795" count="0" type="stmt"/>
        <line num="796" count="0" type="stmt"/>
        <line num="797" count="0" type="stmt"/>
        <line num="798" count="0" type="stmt"/>
        <line num="799" count="6" type="stmt"/>
        <line num="804" count="0" type="stmt"/>
        <line num="805" count="0" type="stmt"/>
        <line num="806" count="0" type="stmt"/>
        <line num="807" count="0" type="stmt"/>
        <line num="810" count="0" type="stmt"/>
        <line num="811" count="0" type="stmt"/>
        <line num="812" count="0" type="stmt"/>
        <line num="813" count="0" type="stmt"/>
        <line num="814" count="0" type="stmt"/>
        <line num="816" count="0" type="stmt"/>
        <line num="817" count="0" type="stmt"/>
        <line num="818" count="0" type="stmt"/>
        <line num="819" count="0" type="stmt"/>
        <line num="820" count="0" type="stmt"/>
        <line num="821" count="0" type="stmt"/>
        <line num="822" count="0" type="stmt"/>
        <line num="823" count="0" type="stmt"/>
        <line num="825" count="0" type="stmt"/>
        <line num="826" count="0" type="stmt"/>
        <line num="827" count="0" type="stmt"/>
        <line num="828" count="0" type="stmt"/>
        <line num="829" count="0" type="stmt"/>
        <line num="830" count="0" type="stmt"/>
        <line num="831" count="0" type="stmt"/>
        <line num="832" count="0" type="stmt"/>
        <line num="833" count="0" type="stmt"/>
        <line num="836" count="0" type="stmt"/>
        <line num="837" count="0" type="stmt"/>
        <line num="838" count="0" type="stmt"/>
        <line num="840" count="0" type="stmt"/>
        <line num="841" count="0" type="stmt"/>
        <line num="842" count="0" type="stmt"/>
        <line num="843" count="0" type="stmt"/>
        <line num="844" count="0" type="stmt"/>
        <line num="846" count="0" type="stmt"/>
        <line num="847" count="0" type="stmt"/>
        <line num="848" count="0" type="stmt"/>
        <line num="849" count="0" type="stmt"/>
        <line num="850" count="0" type="stmt"/>
        <line num="851" count="0" type="stmt"/>
        <line num="852" count="0" type="stmt"/>
        <line num="853" count="0" type="stmt"/>
        <line num="854" count="0" type="stmt"/>
        <line num="855" count="0" type="stmt"/>
        <line num="856" count="0" type="stmt"/>
        <line num="857" count="0" type="stmt"/>
        <line num="858" count="0" type="stmt"/>
        <line num="861" count="0" type="stmt"/>
        <line num="862" count="0" type="stmt"/>
        <line num="863" count="0" type="stmt"/>
        <line num="865" count="0" type="stmt"/>
        <line num="866" count="0" type="stmt"/>
        <line num="867" count="0" type="stmt"/>
        <line num="868" count="0" type="stmt"/>
        <line num="870" count="0" type="stmt"/>
        <line num="871" count="0" type="stmt"/>
        <line num="872" count="0" type="stmt"/>
        <line num="873" count="0" type="stmt"/>
        <line num="874" count="0" type="stmt"/>
        <line num="875" count="0" type="stmt"/>
        <line num="876" count="0" type="stmt"/>
        <line num="878" count="0" type="stmt"/>
        <line num="879" count="0" type="stmt"/>
        <line num="880" count="0" type="stmt"/>
        <line num="881" count="0" type="stmt"/>
        <line num="882" count="0" type="stmt"/>
        <line num="883" count="0" type="stmt"/>
        <line num="884" count="0" type="stmt"/>
        <line num="885" count="0" type="stmt"/>
        <line num="886" count="0" type="stmt"/>
        <line num="891" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="892" count="2" type="stmt"/>
        <line num="894" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="895" count="4" type="stmt"/>
        <line num="896" count="4" type="stmt"/>
        <line num="898" count="4" type="stmt"/>
        <line num="900" count="4" type="cond" truecount="1" falsecount="0"/>
        <line num="901" count="1" type="stmt"/>
        <line num="902" count="1" type="stmt"/>
        <line num="903" count="1" type="stmt"/>
        <line num="904" count="1" type="stmt"/>
        <line num="905" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="907" count="3" type="stmt"/>
        <line num="909" count="4" type="cond" truecount="1" falsecount="0"/>
        <line num="911" count="2" type="stmt"/>
        <line num="912" count="2" type="stmt"/>
        <line num="913" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="914" count="2" type="stmt"/>
        <line num="916" count="4" type="cond" truecount="0" falsecount="1"/>
        <line num="917" count="0" type="stmt"/>
        <line num="918" count="0" type="stmt"/>
        <line num="920" count="0" type="stmt"/>
        <line num="921" count="0" type="stmt"/>
        <line num="922" count="0" type="stmt"/>
        <line num="923" count="0" type="stmt"/>
        <line num="924" count="0" type="stmt"/>
        <line num="925" count="4" type="cond" truecount="1" falsecount="0"/>
        <line num="927" count="3" type="stmt"/>
        <line num="928" count="3" type="stmt"/>
        <line num="929" count="3" type="stmt"/>
        <line num="930" count="3" type="stmt"/>
        <line num="931" count="3" type="stmt"/>
        <line num="932" count="3" type="stmt"/>
        <line num="936" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="941" count="5" type="stmt"/>
        <line num="942" count="5" type="stmt"/>
        <line num="944" count="5" type="cond" truecount="0" falsecount="1"/>
        <line num="945" count="0" type="stmt"/>
        <line num="946" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="947" count="1" type="stmt"/>
        <line num="948" count="1" type="stmt"/>
        <line num="950" count="5" type="cond" truecount="0" falsecount="1"/>
        <line num="951" count="0" type="stmt"/>
        <line num="952" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="953" count="1" type="stmt"/>
        <line num="954" count="1" type="stmt"/>
        <line num="956" count="5" type="cond" truecount="0" falsecount="1"/>
        <line num="957" count="0" type="stmt"/>
        <line num="958" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="959" count="1" type="stmt"/>
        <line num="960" count="1" type="stmt"/>
        <line num="962" count="5" type="cond" truecount="0" falsecount="1"/>
        <line num="963" count="0" type="stmt"/>
        <line num="964" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="965" count="1" type="stmt"/>
        <line num="966" count="1" type="stmt"/>
        <line num="968" count="5" type="stmt"/>
        <line num="969" count="5" type="stmt"/>
        <line num="970" count="5" type="stmt"/>
        <line num="971" count="5" type="stmt"/>
        <line num="972" count="5" type="stmt"/>
        <line num="977" count="1" type="stmt"/>
        <line num="978" count="0" type="stmt"/>
        <line num="979" count="0" type="stmt"/>
        <line num="980" count="0" type="stmt"/>
        <line num="981" count="0" type="stmt"/>
        <line num="982" count="0" type="stmt"/>
        <line num="983" count="0" type="stmt"/>
        <line num="984" count="0" type="stmt"/>
        <line num="985" count="0" type="stmt"/>
        <line num="986" count="0" type="stmt"/>
        <line num="987" count="0" type="stmt"/>
        <line num="988" count="0" type="stmt"/>
        <line num="989" count="0" type="stmt"/>
        <line num="990" count="0" type="stmt"/>
        <line num="991" count="0" type="stmt"/>
        <line num="992" count="0" type="stmt"/>
        <line num="993" count="0" type="stmt"/>
        <line num="994" count="0" type="stmt"/>
        <line num="995" count="0" type="stmt"/>
        <line num="996" count="0" type="stmt"/>
        <line num="1001" count="0" type="stmt"/>
        <line num="1002" count="0" type="stmt"/>
        <line num="1003" count="0" type="stmt"/>
        <line num="1004" count="0" type="stmt"/>
        <line num="1005" count="0" type="stmt"/>
        <line num="1006" count="0" type="stmt"/>
        <line num="1007" count="0" type="stmt"/>
        <line num="1008" count="0" type="stmt"/>
        <line num="1009" count="0" type="stmt"/>
        <line num="1010" count="0" type="stmt"/>
        <line num="1011" count="0" type="stmt"/>
        <line num="1012" count="0" type="stmt"/>
        <line num="1013" count="0" type="stmt"/>
        <line num="1014" count="0" type="stmt"/>
        <line num="1015" count="0" type="stmt"/>
        <line num="1016" count="0" type="stmt"/>
        <line num="1017" count="0" type="stmt"/>
        <line num="1018" count="0" type="stmt"/>
        <line num="1019" count="0" type="stmt"/>
        <line num="1020" count="0" type="stmt"/>
        <line num="1021" count="0" type="stmt"/>
        <line num="1022" count="0" type="stmt"/>
        <line num="1023" count="0" type="stmt"/>
        <line num="1025" count="0" type="stmt"/>
        <line num="1026" count="0" type="stmt"/>
        <line num="1027" count="0" type="stmt"/>
        <line num="1029" count="0" type="stmt"/>
        <line num="1030" count="0" type="stmt"/>
        <line num="1031" count="0" type="stmt"/>
        <line num="1032" count="0" type="stmt"/>
        <line num="1033" count="0" type="stmt"/>
        <line num="1034" count="0" type="stmt"/>
        <line num="1035" count="0" type="stmt"/>
        <line num="1040" count="0" type="stmt"/>
        <line num="1044" count="0" type="stmt"/>
        <line num="1045" count="0" type="stmt"/>
        <line num="1046" count="0" type="stmt"/>
        <line num="1048" count="0" type="stmt"/>
        <line num="1050" count="0" type="stmt"/>
        <line num="1051" count="0" type="stmt"/>
        <line num="1052" count="0" type="stmt"/>
        <line num="1053" count="0" type="stmt"/>
        <line num="1055" count="0" type="stmt"/>
        <line num="1056" count="0" type="stmt"/>
        <line num="1057" count="0" type="stmt"/>
        <line num="1058" count="0" type="stmt"/>
        <line num="1061" count="0" type="stmt"/>
        <line num="1062" count="0" type="stmt"/>
        <line num="1063" count="0" type="stmt"/>
        <line num="1065" count="0" type="stmt"/>
        <line num="1066" count="0" type="stmt"/>
        <line num="1067" count="0" type="stmt"/>
        <line num="1068" count="0" type="stmt"/>
        <line num="1071" count="0" type="stmt"/>
        <line num="1072" count="0" type="stmt"/>
        <line num="1073" count="0" type="stmt"/>
        <line num="1074" count="0" type="stmt"/>
        <line num="1076" count="0" type="stmt"/>
        <line num="1077" count="0" type="stmt"/>
        <line num="1078" count="0" type="stmt"/>
        <line num="1079" count="0" type="stmt"/>
        <line num="1082" count="0" type="stmt"/>
        <line num="1083" count="0" type="stmt"/>
        <line num="1084" count="0" type="stmt"/>
        <line num="1085" count="0" type="stmt"/>
        <line num="1087" count="0" type="stmt"/>
        <line num="1088" count="0" type="stmt"/>
        <line num="1089" count="0" type="stmt"/>
        <line num="1090" count="0" type="stmt"/>
        <line num="1092" count="0" type="stmt"/>
        <line num="1094" count="0" type="stmt"/>
        <line num="1095" count="0" type="stmt"/>
        <line num="1096" count="0" type="stmt"/>
        <line num="1097" count="0" type="stmt"/>
        <line num="1098" count="0" type="stmt"/>
        <line num="1100" count="0" type="stmt"/>
        <line num="1101" count="0" type="stmt"/>
        <line num="1102" count="0" type="stmt"/>
        <line num="1103" count="0" type="stmt"/>
        <line num="1104" count="0" type="stmt"/>
        <line num="1105" count="0" type="stmt"/>
        <line num="1106" count="0" type="stmt"/>
        <line num="1107" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
