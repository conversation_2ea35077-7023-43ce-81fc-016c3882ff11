import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function applyMigrationFix() {
  console.log('Applying migration fix for get_all_user_profiles function...');
  
  try {
    // Read the migration file
    const migrationPath = 'supabase/migrations/20250113000001_fix_get_all_user_profiles_ambiguous_column.sql';
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('1. Attempting to apply migration using execute_sql...');
    
    // Try using execute_sql function
    const { data, error } = await supabase.rpc('execute_sql', { query: migrationSQL });
    
    if (error) {
      console.error('❌ Migration failed:', error);
      
      // If execute_sql doesn't work, let's try a different approach
      console.log('2. Trying alternative approach...');
      
      // Let's try to create the function directly using a simpler SQL
      const simpleFunctionSQL = `
        -- Drop the existing function first
        DROP FUNCTION IF EXISTS public.get_all_user_profiles() CASCADE;
        
        -- Create the corrected function with proper table aliases
        CREATE OR REPLACE FUNCTION public.get_all_user_profiles()
        RETURNS TABLE (
          id UUID,
          user_id UUID,
          email TEXT,
          full_name TEXT,
          is_subscribed BOOLEAN,
          is_admin BOOLEAN,
          subscription_expires_at TIMESTAMPTZ,
          subscription_status TEXT,
          subscription_plan TEXT,
          created_at TIMESTAMPTZ,
          updated_at TIMESTAMPTZ,
          last_sign_in_at TIMESTAMPTZ
        ) AS $$
        BEGIN
          -- Check if current user is admin
          IF NOT EXISTS (
            SELECT 1 FROM public.user_profiles up_check
            WHERE up_check.user_id = auth.uid() AND up_check.is_admin = true
          ) THEN
            RAISE EXCEPTION 'Access denied. Admin privileges required.';
          END IF;

          RETURN QUERY
          SELECT
            up.id,
            up.user_id,
            up.email,
            up.full_name,
            up.is_subscribed,
            up.is_admin,
            up.subscription_expires_at,
            up.subscription_status,
            up.subscription_plan,
            up.created_at,
            up.updated_at,
            au.last_sign_in_at
          FROM public.user_profiles up
          LEFT JOIN auth.users au ON up.user_id = au.id
          ORDER BY up.created_at DESC;
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;
        
        -- Grant execute permission to authenticated users
        GRANT EXECUTE ON FUNCTION public.get_all_user_profiles() TO authenticated;
      `;
      
      const { data: altData, error: altError } = await supabase.rpc('execute_sql', { query: simpleFunctionSQL });
      
      if (altError) {
        console.error('❌ Alternative approach also failed:', altError);
        return false;
      } else {
        console.log('✓ Alternative approach succeeded');
        console.log('Result:', altData);
      }
    } else {
      console.log('✓ Migration applied successfully');
      console.log('Result:', data);
    }
    
    // Test the function
    console.log('3. Testing the fixed function...');
    const { data: testData, error: testError } = await supabase.rpc('get_all_user_profiles');
    
    if (testError) {
      if (testError.message.includes('Access denied') || testError.message.includes('Admin privileges')) {
        console.log('✓ Function is working correctly (requires admin authentication)');
        return true;
      } else if (testError.code === '42702') {
        console.error('❌ Ambiguous column reference error still exists');
        return false;
      } else {
        console.error('❌ Function test failed:', testError);
        return false;
      }
    } else {
      console.log('✓ Function test successful');
      console.log('Data returned:', testData ? testData.length : 0, 'records');
      return true;
    }
    
  } catch (error) {
    console.error('Migration application failed:', error);
    return false;
  }
}

applyMigrationFix().then(success => {
  if (success) {
    console.log('\n✅ Migration fix completed successfully!');
  } else {
    console.log('\n❌ Migration fix failed. Manual intervention may be required.');
  }
});