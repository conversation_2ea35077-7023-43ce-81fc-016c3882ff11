import { describe, it, expect, vi, beforeEach } from 'vitest';
import crypto from 'crypto';
import { mockSupabaseClient } from '../setup.js';

// Mock the webhook processing functions directly
vi.mock('../../services/subscription.js');

describe('Webhook Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Webhook Signature Verification', () => {
    const webhookSecret = 'test_webhook_secret';

    function createWebhookSignature(payload: string): string {
      return crypto
        .createHmac('sha512', webhookSecret)
        .update(payload)
        .digest('hex');
    }

    function verifyWebhookSignature(payload: string, signature: string, secret: string): boolean {
      const computedHash = crypto
        .createHmac('sha512', secret)
        .update(payload)
        .digest('hex');

      // Use timing-safe comparison
      const maxLength = Math.max(computedHash.length, signature.length);
      const paddedHash = computedHash.padEnd(maxLength, '0');
      const paddedSignature = signature.padEnd(maxLength, '0');

      const hashBuffer = Buffer.from(paddedHash, 'utf8');
      const signatureBuffer = Buffer.from(paddedSignature, 'utf8');

      return crypto.timingSafeEqual(hashBuffer, signatureBuffer) && 
             computedHash.length === signature.length;
    }

    it('should verify valid webhook signatures', () => {
      const payload = JSON.stringify({
        event: 'charge.success',
        data: { id: 'test_123', reference: 'test_ref' }
      });

      const signature = createWebhookSignature(payload);
      const isValid = verifyWebhookSignature(payload, signature, webhookSecret);

      expect(isValid).toBe(true);
    });

    it('should reject invalid webhook signatures', () => {
      const payload = JSON.stringify({
        event: 'charge.success',
        data: { id: 'test_123', reference: 'test_ref' }
      });

      const invalidSignature = 'invalid_signature_123';
      const isValid = verifyWebhookSignature(payload, invalidSignature, webhookSecret);

      expect(isValid).toBe(false);
    });

    it('should reject signatures with different payload', () => {
      const originalPayload = JSON.stringify({
        event: 'charge.success',
        data: { id: 'test_123', reference: 'test_ref' }
      });

      const modifiedPayload = JSON.stringify({
        event: 'charge.success',
        data: { id: 'test_456', reference: 'test_ref' }
      });

      const signature = createWebhookSignature(originalPayload);
      const isValid = verifyWebhookSignature(modifiedPayload, signature, webhookSecret);

      expect(isValid).toBe(false);
    });

    it('should handle timing attacks safely', () => {
      const payload = JSON.stringify({ event: 'test' });
      const validSignature = createWebhookSignature(payload);
      
      // Test with signatures of different lengths
      const shortSignature = validSignature.substring(0, 10);
      const longSignature = validSignature + 'extra';

      expect(verifyWebhookSignature(payload, shortSignature, webhookSecret)).toBe(false);
      expect(verifyWebhookSignature(payload, longSignature, webhookSecret)).toBe(false);
    });
  });

  describe('Webhook Event Processing', () => {
    let updateUserSubscription: any;

    beforeEach(async () => {
      const subscriptionModule = await import('../../services/subscription.js');
      updateUserSubscription = vi.mocked(subscriptionModule.updateUserSubscription);
    });

    it('should process charge.success events', async () => {
      const chargeSuccessEvent = {
        event: 'charge.success',
        data: {
          id: 'trx_success_123',
          reference: 'webhook_success_ref',
          amount: 199600, // 1996 NGN in kobo
          currency: 'NGN',
          status: 'success',
          customer: {
            email: '<EMAIL>',
            first_name: 'Webhook',
            last_name: 'Test'
          },
          metadata: {
            plan: 'pro'
          },
          gateway_response: 'Successful'
        }
      };

      // Mock successful subscription update
      updateUserSubscription.mockResolvedValue({
        success: true,
        data: {
          userId: 'user_webhook_123',
          planId: 'pro',
          reference: 'webhook_success_ref'
        }
      });

      // Simulate webhook processing logic
      const { data } = chargeSuccessEvent;
      const { reference, amount, customer, metadata } = data;

      // Extract plan from metadata
      let planId = 'pro'; // Default
      if (metadata?.plan) {
        planId = metadata.plan;
      }

      // Process the payment
      const result = await updateUserSubscription(
        customer.email,
        planId,
        amount,
        reference
      );

      expect(result.success).toBe(true);
      expect(updateUserSubscription).toHaveBeenCalledWith(
        '<EMAIL>',
        'pro',
        199600,
        'webhook_success_ref'
      );
    });

    it('should handle charge.success events with missing customer email', async () => {
      const invalidEvent = {
        event: 'charge.success',
        data: {
          id: 'trx_invalid_123',
          reference: 'invalid_ref',
          amount: 99800,
          status: 'success',
          customer: null // Missing customer
        }
      };

      // Simulate validation logic
      const { data } = invalidEvent;
      const hasValidCustomer = data.customer?.email;

      expect(hasValidCustomer).toBeFalsy();
      
      // Should not call subscription update for invalid data
      expect(updateUserSubscription).not.toHaveBeenCalled();
    });

    it('should handle charge.success events with missing reference', async () => {
      const invalidEvent = {
        event: 'charge.success',
        data: {
          id: 'trx_no_ref_123',
          reference: null, // Missing reference
          amount: 99800,
          status: 'success',
          customer: {
            email: '<EMAIL>'
          }
        }
      };

      // Simulate validation logic
      const { data } = invalidEvent;
      const hasValidReference = data.reference && data.reference.trim().length > 0;

      expect(hasValidReference).toBeFalsy();
      
      // Should not call subscription update for invalid data
      expect(updateUserSubscription).not.toHaveBeenCalled();
    });

    it('should extract plan from different metadata structures', async () => {
      const testCases = [
        {
          metadata: { plan: 'basic' },
          expectedPlan: 'basic'
        },
        {
          metadata: { 
            custom_fields: [
              { variable_name: 'plan', value: 'premium' }
            ]
          },
          expectedPlan: 'premium'
        },
        {
          metadata: { subscription_type: 'enterprise' },
          expectedPlan: 'enterprise'
        },
        {
          metadata: {},
          expectedPlan: 'pro' // Default
        },
        {
          metadata: null,
          expectedPlan: 'pro' // Default
        }
      ];

      for (const testCase of testCases) {
        // Simulate plan extraction logic
        let planId = 'pro'; // Default

        if (testCase.metadata) {
          // Check custom_fields first
          if (testCase.metadata.custom_fields && Array.isArray(testCase.metadata.custom_fields)) {
            const planField = testCase.metadata.custom_fields.find(
              (field: any) => field.variable_name === 'plan'
            );
            if (planField?.value) {
              planId = planField.value;
            }
          }
          
          // Check direct plan field
          if (!planId || planId === 'pro') {
            if (testCase.metadata.plan) {
              planId = testCase.metadata.plan;
            }
          }
          
          // Check subscription_type field
          if (!planId || planId === 'pro') {
            if (testCase.metadata.subscription_type) {
              planId = testCase.metadata.subscription_type;
            }
          }
        }

        expect(planId).toBe(testCase.expectedPlan);
      }
    });

    it('should handle charge.failed events', async () => {
      const chargeFailedEvent = {
        event: 'charge.failed',
        data: {
          id: 'trx_failed_123',
          reference: 'failed_ref_123',
          amount: 99800,
          status: 'failed',
          customer: {
            email: '<EMAIL>'
          },
          gateway_response: 'Declined by bank'
        }
      };

      // For failed charges, we typically just log them
      // No subscription update should occur
      const { data } = chargeFailedEvent;
      
      expect(data.status).toBe('failed');
      expect(data.gateway_response).toBe('Declined by bank');
      
      // Should not call subscription update for failed payments
      expect(updateUserSubscription).not.toHaveBeenCalled();
    });
  });

  describe('Webhook Idempotency', () => {
    function createPayloadHash(payload: any): string {
      return crypto.createHash('sha256')
        .update(JSON.stringify(payload))
        .digest('hex');
    }

    it('should generate consistent hashes for same payload', () => {
      const payload = {
        event: 'charge.success',
        data: { id: 'test_123', reference: 'test_ref' }
      };

      const hash1 = createPayloadHash(payload);
      const hash2 = createPayloadHash(payload);

      expect(hash1).toBe(hash2);
    });

    it('should generate different hashes for different payloads', () => {
      const payload1 = {
        event: 'charge.success',
        data: { id: 'test_123', reference: 'test_ref_1' }
      };

      const payload2 = {
        event: 'charge.success',
        data: { id: 'test_123', reference: 'test_ref_2' }
      };

      const hash1 = createPayloadHash(payload1);
      const hash2 = createPayloadHash(payload2);

      expect(hash1).not.toBe(hash2);
    });

    it('should detect duplicate events', async () => {
      const eventId = 'trx_duplicate_123';
      const payload = {
        event: 'charge.success',
        data: { id: eventId, reference: 'duplicate_ref' }
      };
      const payloadHash = createPayloadHash(payload);

      // Mock that event was already processed
      mockSupabaseClient.from.mockImplementation((table: string) => {
        if (table === 'webhook_events') {
          return {
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            single: vi.fn().mockResolvedValue({
              data: {
                event_id: eventId,
                status: 'completed',
                payload_hash: payloadHash
              },
              error: null
            })
          };
        }
        return mockSupabaseClient.from(table);
      });

      // Simulate idempotency check
      const { data, error } = await mockSupabaseClient
        .from('webhook_events')
        .select('event_id, status, payload_hash')
        .eq('event_id', eventId)
        .single();

      expect(data).toBeTruthy();
      expect(data.status).toBe('completed');
      expect(data.payload_hash).toBe(payloadHash);
    });

    it('should handle replay attacks (same event ID, different payload)', async () => {
      const eventId = 'trx_replay_123';
      const originalPayload = {
        event: 'charge.success',
        data: { id: eventId, reference: 'original_ref', amount: 99800 }
      };
      const replayPayload = {
        event: 'charge.success',
        data: { id: eventId, reference: 'original_ref', amount: 199600 } // Different amount
      };

      const originalHash = createPayloadHash(originalPayload);
      const replayHash = createPayloadHash(replayPayload);

      // Mock existing event with original hash
      mockSupabaseClient.from.mockImplementation((table: string) => {
        if (table === 'webhook_events') {
          return {
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            single: vi.fn().mockResolvedValue({
              data: {
                event_id: eventId,
                status: 'completed',
                payload_hash: originalHash
              },
              error: null
            })
          };
        }
        return mockSupabaseClient.from(table);
      });

      // Check if replay payload matches existing
      const { data } = await mockSupabaseClient
        .from('webhook_events')
        .select('event_id, status, payload_hash')
        .eq('event_id', eventId)
        .single();

      const isReplayAttack = data && data.payload_hash !== replayHash;
      
      expect(isReplayAttack).toBe(true);
      expect(originalHash).not.toBe(replayHash);
    });
  });

  describe('Webhook Event Logging', () => {
    it('should log webhook events to database', async () => {
      const eventId = 'trx_logging_123';
      const eventType = 'charge.success';
      const payload = {
        event: eventType,
        data: { id: eventId, reference: 'logging_ref' }
      };

      // Mock successful logging
      mockSupabaseClient.from.mockImplementation((table: string) => {
        if (table === 'webhook_events') {
          return {
            upsert: vi.fn().mockResolvedValue({ data: {}, error: null })
          };
        }
        return mockSupabaseClient.from(table);
      });

      // Simulate logging
      const payloadHash = crypto.createHash('sha256')
        .update(JSON.stringify(payload))
        .digest('hex');

      const logData = {
        event_id: eventId,
        event_type: eventType,
        reference: 'logging_ref',
        status: 'received',
        payload_hash: payloadHash,
        processing_attempts: 1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      await mockSupabaseClient
        .from('webhook_events')
        .upsert(logData);

      expect(mockSupabaseClient.from).toHaveBeenCalledWith('webhook_events');
    });

    it('should handle logging failures gracefully', async () => {
      const eventId = 'trx_log_fail_123';

      // Mock logging failure
      mockSupabaseClient.from.mockImplementation((table: string) => {
        if (table === 'webhook_events') {
          return {
            upsert: vi.fn().mockResolvedValue({
              data: null,
              error: { message: 'Logging table unavailable' }
            })
          };
        }
        return mockSupabaseClient.from(table);
      });

      // Simulate logging attempt
      const { error } = await mockSupabaseClient
        .from('webhook_events')
        .upsert({
          event_id: eventId,
          event_type: 'charge.success',
          status: 'received'
        });

      expect(error).toBeTruthy();
      expect(error.message).toBe('Logging table unavailable');
      
      // Application should continue processing despite logging failure
    });
  });

  describe('Webhook Data Sanitization', () => {
    function sanitizeWebhookPayload(payload: any): any {
      if (!payload || typeof payload !== 'object') return payload;

      const sanitized = { ...payload };
      
      if (sanitized.data) {
        const data = { ...sanitized.data };
        
        // Mask customer information
        if (data.customer) {
          data.customer = {
            ...data.customer,
            email: data.customer.email ? 
              data.customer.email.replace(/(.{2}).*@/, '$1***@') : undefined,
            phone: data.customer.phone ? '[REDACTED]' : undefined
          };
        }
        
        // Mask authorization data
        if (data.authorization) {
          data.authorization = '[REDACTED]';
        }
        
        // Keep metadata structure but mask sensitive values
        if (data.metadata) {
          data.metadata = Object.keys(data.metadata).reduce((acc, key) => {
            if (typeof data.metadata[key] === 'string' && key !== 'plan' && key !== 'subscription_type') {
              acc[key] = '[REDACTED]';
            } else {
              acc[key] = data.metadata[key];
            }
            return acc;
          }, {} as unknown);
        }
        
        sanitized.data = data;
      }
      
      return sanitized;
    }

    it('should sanitize customer email for logging', () => {
      const payload = {
        event: 'charge.success',
        data: {
          customer: {
            email: '<EMAIL>',
            phone: '+1234567890'
          }
        }
      };

      const sanitized = sanitizeWebhookPayload(payload);

      expect(sanitized.data.customer.email).toBe('se***@example.com');
      expect(sanitized.data.customer.phone).toBe('[REDACTED]');
    });

    it('should sanitize authorization data', () => {
      const payload = {
        event: 'charge.success',
        data: {
          authorization: {
            authorization_code: 'AUTH_sensitive123',
            card_type: 'visa',
            last4: '1234'
          }
        }
      };

      const sanitized = sanitizeWebhookPayload(payload);

      expect(sanitized.data.authorization).toBe('[REDACTED]');
    });

    it('should preserve non-sensitive metadata', () => {
      const payload = {
        event: 'charge.success',
        data: {
          metadata: {
            plan: 'basic',
            subscription_type: 'monthly',
            user_id: 'sensitive_user_123',
            custom_field: 'sensitive_value'
          }
        }
      };

      const sanitized = sanitizeWebhookPayload(payload);

      expect(sanitized.data.metadata.plan).toBe('basic');
      expect(sanitized.data.metadata.subscription_type).toBe('monthly');
      expect(sanitized.data.metadata.user_id).toBe('[REDACTED]');
      expect(sanitized.data.metadata.custom_field).toBe('[REDACTED]');
    });

    it('should handle null and undefined values', () => {
      const payload = {
        event: 'charge.success',
        data: {
          customer: null,
          authorization: undefined,
          metadata: {
            plan: 'basic',
            empty_field: null
          }
        }
      };

      const sanitized = sanitizeWebhookPayload(payload);

      expect(sanitized.data.customer).toBeNull();
      expect(sanitized.data.authorization).toBeUndefined();
      expect(sanitized.data.metadata.plan).toBe('basic');
      expect(sanitized.data.metadata.empty_field).toBeNull();
    });
  });
});