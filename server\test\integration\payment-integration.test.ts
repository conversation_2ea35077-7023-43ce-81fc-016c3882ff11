import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mockSupabaseClient } from '../setup.js';

// Import the services directly for integration testing
import {
  updateUserSubscription,
  getSubscriptionStatus,
  checkAndUpdateExpiredSubscriptions,
  cleanupExpiredSubscriptionsWithRetry
} from '../../services/subscription.js';

describe('Payment Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Complete Payment Verification Flow', () => {
    it('should successfully process payment and update subscription', async () => {
      const testEmail = '<EMAIL>';
      const testPlanId = 'basic';
      const testAmount = 99800; // 998 NGN in kobo
      const testReference = 'integration_test_ref_123';

      // Mock successful user lookup
      mockSupabaseClient.auth.admin.listUsers.mockResolvedValue({
        data: {
          users: [{
            id: 'user_integration_123',
            email: testEmail,
            name: 'Integration Test User'
          }]
        },
        error: null
      });

      // Mock successful atomic function call
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          user_id: 'user_integration_123',
          plan_id: testPlanId,
          end_date: '2024-01-08T00:00:00Z',
          payment_id: 'payment_integration_123',
          subscription_id: 'sub_integration_123',
          reference: testReference
        },
        error: null
      });

      const result = await updateUserSubscription(
        testEmail,
        testPlanId,
        testAmount,
        testReference
      );

      expect(result.success).toBe(true);
      expect(result.data).toMatchObject({
        userId: 'user_integration_123',
        planId: testPlanId,
        reference: testReference
      });

      // Verify atomic function was called with correct parameters
      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith('handle_payment_success_atomic', {
        p_user_id: 'user_integration_123',
        p_plan_id: testPlanId,
        p_amount: 998, // Converted from kobo
        p_reference: testReference,
        p_currency: 'NGN'
      });
    });

    it('should handle duplicate payment processing', async () => {
      const testEmail = '<EMAIL>';
      const testReference = 'duplicate_test_ref_123';

      // Mock successful user lookup
      mockSupabaseClient.auth.admin.listUsers.mockResolvedValue({
        data: {
          users: [{
            id: 'user_duplicate_123',
            email: testEmail
          }]
        },
        error: null
      });

      // Mock duplicate payment error
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: false,
          error: 'Payment already processed'
        },
        error: null
      });

      const result = await updateUserSubscription(
        testEmail,
        'basic',
        99800,
        testReference
      );

      expect(result.success).toBe(false);
      expect(result.error).toBe('Payment already processed');
      expect(result.errorCode).toBe('DUPLICATE_PAYMENT');
    });

    it('should handle user not found scenario', async () => {
      const testEmail = '<EMAIL>';

      // Mock user not found
      mockSupabaseClient.auth.admin.listUsers.mockResolvedValue({
        data: {
          users: []
        },
        error: null
      });

      const result = await updateUserSubscription(
        testEmail,
        'basic',
        99800,
        'notfound_test_ref'
      );

      expect(result.success).toBe(false);
      expect(result.error).toBe('User not found');
      expect(result.errorCode).toBe('USER_NOT_FOUND');
    });

    it('should validate input parameters', async () => {
      // Test empty email
      let result = await updateUserSubscription('', 'basic', 99800, 'test_ref');
      expect(result.success).toBe(false);
      expect(result.errorCode).toBe('VALIDATION_ERROR');

      // Test invalid email format
      result = await updateUserSubscription('invalid-email', 'basic', 99800, 'test_ref');
      expect(result.success).toBe(false);
      expect(result.errorCode).toBe('VALIDATION_ERROR');

      // Test invalid plan
      result = await updateUserSubscription('<EMAIL>', 'invalid_plan', 99800, 'test_ref');
      expect(result.success).toBe(false);
      expect(result.errorCode).toBe('INVALID_PLAN');

      // Test invalid amount
      result = await updateUserSubscription('<EMAIL>', 'basic', -100, 'test_ref');
      expect(result.success).toBe(false);
      expect(result.errorCode).toBe('VALIDATION_ERROR');
    });
  });

  describe('Database Consistency During Payment Processing', () => {
    it('should maintain consistency with atomic operations', async () => {
      const testEmail = '<EMAIL>';
      const testUserId = 'user_atomic_123';

      // Mock successful user lookup
      mockSupabaseClient.auth.admin.listUsers.mockResolvedValue({
        data: {
          users: [{ id: testUserId, email: testEmail }]
        },
        error: null
      });

      // Mock successful atomic operation
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          user_id: testUserId,
          plan_id: 'basic',
          subscription_id: 'sub_atomic_123',
          payment_id: 'pay_atomic_123'
        },
        error: null
      });

      const result = await updateUserSubscription(
        testEmail,
        'basic',
        99800,
        'atomic_test_ref'
      );

      expect(result.success).toBe(true);
      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith('handle_payment_success_atomic', {
        p_user_id: testUserId,
        p_plan_id: 'basic',
        p_amount: 998,
        p_reference: 'atomic_test_ref',
        p_currency: 'NGN'
      });
    });

    it('should handle atomic function failures gracefully', async () => {
      const testEmail = '<EMAIL>';

      // Mock successful user lookup
      mockSupabaseClient.auth.admin.listUsers.mockResolvedValue({
        data: {
          users: [{ id: 'user_atomic_fail', email: testEmail }]
        },
        error: null
      });

      // Mock atomic function database error
      mockSupabaseClient.rpc.mockResolvedValue({
        data: null,
        error: { message: 'Database constraint violation' }
      });

      const result = await updateUserSubscription(
        testEmail,
        'basic',
        99800,
        'atomic_fail_ref'
      );

      expect(result.success).toBe(false);
      expect(result.errorCode).toBe('DATABASE_ERROR');
    });

    it('should fallback to manual operations when atomic function unavailable', async () => {
      const testEmail = '<EMAIL>';
      const testUserId = 'user_fallback_123';

      // Mock successful user lookup
      mockSupabaseClient.auth.admin.listUsers.mockResolvedValue({
        data: {
          users: [{ id: testUserId, email: testEmail }]
        },
        error: null
      });

      // Mock atomic function not available
      mockSupabaseClient.rpc.mockResolvedValue({
        data: null,
        error: { message: 'function handle_payment_success_atomic does not exist' }
      });

      // Mock successful fallback operations
      const mockQuery = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        maybeSingle: vi.fn().mockResolvedValue({ data: null, error: null }),
        insert: vi.fn().mockResolvedValue({ data: [{}], error: null })
      };

      mockSupabaseClient.from.mockReturnValue(mockQuery);

      const result = await updateUserSubscription(
        testEmail,
        'basic',
        99800,
        'fallback_ref'
      );

      expect(result.success).toBe(true);
      expect(mockSupabaseClient.from).toHaveBeenCalledWith('subscriptions');
    });
  });

  describe('Subscription Status Management', () => {
    it('should retrieve subscription status correctly', async () => {
      const testUserId = 'user_status_123';

      // Mock successful status retrieval
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          profile: {
            is_subscribed: true,
            subscription_status: 'active',
            subscription_expires_at: '2024-12-31T00:00:00Z',
            subscription_plan: 'pro'
          },
          subscription: {
            is_expired: false
          }
        },
        error: null
      });

      const result = await getSubscriptionStatus(testUserId);

      expect(result.success).toBe(true);
      expect(result.data).toMatchObject({
        userId: testUserId,
        isSubscribed: true,
        subscriptionStatus: 'active',
        planId: 'pro',
        isExpired: false
      });
    });

    it('should handle expired subscription detection', async () => {
      const testUserId = 'user_expired_123';

      // Mock status check that finds expired subscription
      mockSupabaseClient.rpc
        .mockResolvedValueOnce({
          data: {
            success: true,
            profile: {
              is_subscribed: true,
              subscription_status: 'active',
              subscription_expires_at: '2023-12-31T00:00:00Z', // Past date
              subscription_plan: 'basic'
            },
            subscription: {
              is_expired: true
            }
          },
          error: null
        })
        .mockResolvedValueOnce({
          data: {
            success: true,
            user_id: testUserId
          },
          error: null
        });

      const result = await getSubscriptionStatus(testUserId);

      expect(result.success).toBe(true);
      expect(result.data?.isSubscribed).toBe(false);
      expect(result.data?.subscriptionStatus).toBe('expired');

      // Verify that status update was called
      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith('update_subscription_status_atomic', {
        p_user_id: testUserId,
        p_status: 'expired',
        p_reason: 'Subscription expired during status check'
      });
    });
  });

  describe('Bulk Subscription Operations', () => {
    it('should handle bulk subscription cleanup', async () => {
      const expiredUsers = ['user1', 'user2', 'user3'];

      // Mock successful bulk cleanup
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: true,
          expired_count: expiredUsers.length,
          expired_users: expiredUsers
        },
        error: null
      });

      const result = await checkAndUpdateExpiredSubscriptions();

      expect(result.success).toBe(true);
      expect(result.data?.expiredCount).toBe(3);
      expect(result.data?.cleanedUsers).toEqual(expiredUsers);
    });

    it('should handle bulk cleanup failures', async () => {
      // Mock cleanup failure
      mockSupabaseClient.rpc.mockResolvedValue({
        data: {
          success: false,
          error: 'Database error during cleanup'
        },
        error: null
      });

      const result = await checkAndUpdateExpiredSubscriptions();

      expect(result.success).toBe(false);
      expect(result.error).toContain('Database error during cleanup');
    });
  });

  describe('Error Recovery Mechanisms', () => {
    beforeEach(() => {
      vi.useFakeTimers();
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('should retry cleanup operations with exponential backoff', async () => {
      // Mock initial failures followed by success
      mockSupabaseClient.rpc
        .mockResolvedValueOnce({
          data: { success: false, error: 'Temporary failure' },
          error: null
        })
        .mockResolvedValueOnce({
          data: {
            success: true,
            expired_count: 2,
            expired_users: ['user1', 'user2']
          },
          error: null
        });

      const resultPromise = cleanupExpiredSubscriptionsWithRetry(2);

      // Fast-forward through retry delay
      vi.advanceTimersByTime(1000);
      await vi.runAllTimersAsync();

      const result = await resultPromise;

      expect(result.success).toBe(true);
      expect(result.data?.expiredCount).toBe(2);
      expect(mockSupabaseClient.rpc).toHaveBeenCalledTimes(2);
    });

    it('should fail after maximum retry attempts', async () => {
      // Mock persistent failures
      mockSupabaseClient.rpc.mockResolvedValue({
        data: { success: false, error: 'Persistent failure' },
        error: null
      });

      const resultPromise = cleanupExpiredSubscriptionsWithRetry(2);

      // Fast-forward through all retry delays
      vi.advanceTimersByTime(1000); // First retry
      await vi.runAllTimersAsync();
      vi.advanceTimersByTime(2000); // Second retry
      await vi.runAllTimersAsync();

      const result = await resultPromise;

      expect(result.success).toBe(false);
      expect(result.error).toContain('Cleanup failed after 2 attempts');
      expect(mockSupabaseClient.rpc).toHaveBeenCalledTimes(2);
    });
  });

  describe('Concurrent Operations', () => {
    it('should handle concurrent payment processing for same user', async () => {
      const testEmail = '<EMAIL>';
      const testUserId = 'user_concurrent_123';

      // Mock successful user lookup
      mockSupabaseClient.auth.admin.listUsers.mockResolvedValue({
        data: {
          users: [{ id: testUserId, email: testEmail }]
        },
        error: null
      });

      // Mock first call succeeds, second call detects duplicate
      mockSupabaseClient.rpc
        .mockResolvedValueOnce({
          data: {
            success: true,
            user_id: testUserId,
            plan_id: 'basic',
            reference: 'concurrent_ref_1'
          },
          error: null
        })
        .mockResolvedValueOnce({
          data: {
            success: false,
            error: 'Payment already processed'
          },
          error: null
        });

      // Simulate concurrent payment processing
      const [result1, result2] = await Promise.all([
        updateUserSubscription(testEmail, 'basic', 99800, 'concurrent_ref_1'),
        updateUserSubscription(testEmail, 'basic', 99800, 'concurrent_ref_2')
      ]);

      expect(result1.success).toBe(true);
      expect(result2.success).toBe(false);
      expect(result2.errorCode).toBe('DUPLICATE_PAYMENT');
    });

    it('should handle concurrent status updates', async () => {
      const testUserId = 'user_concurrent_status';

      // Mock successful status updates
      mockSupabaseClient.rpc.mockResolvedValue({
        data: { success: true, user_id: testUserId },
        error: null
      });

      // Import the function for testing
      const { updateSubscriptionStatusAtomic } = await import('../../services/subscription.js');

      // Simulate concurrent status updates
      const [result1, result2] = await Promise.all([
        updateSubscriptionStatusAtomic(testUserId, 'expired', 'Expired - reason 1'),
        updateSubscriptionStatusAtomic(testUserId, 'cancelled', 'Cancelled - reason 2')
      ]);

      expect(result1.success).toBe(true);
      expect(result2.success).toBe(true);
      expect(mockSupabaseClient.rpc).toHaveBeenCalledTimes(2);
    });
  });

  describe('Data Validation and Integrity', () => {
    it('should validate subscription data comprehensively', async () => {
      // Import validation function
      const { validateSubscriptionData } = await import('../../services/subscription.js');

      // Test valid data
      const validData = {
        email: '<EMAIL>',
        planId: 'basic',
        amount: 99800,
        reference: 'valid_ref_123456'
      };

      let result = validateSubscriptionData(validData);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);

      // Test invalid email
      const invalidEmailData = { ...validData, email: 'invalid-email' };
      result = validateSubscriptionData(invalidEmailData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid email format');

      // Test invalid plan
      const invalidPlanData = { ...validData, planId: 'invalid_plan' };
      result = validateSubscriptionData(invalidPlanData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid plan ID');

      // Test invalid amount
      const invalidAmountData = { ...validData, amount: -100 };
      result = validateSubscriptionData(invalidAmountData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Amount must be greater than 0');

      // Test short reference
      const shortRefData = { ...validData, reference: 'short' };
      result = validateSubscriptionData(shortRefData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Payment reference must be at least 10 characters');
    });

    it('should handle database constraint violations', async () => {
      const testEmail = '<EMAIL>';

      // Mock successful user lookup
      mockSupabaseClient.auth.admin.listUsers.mockResolvedValue({
        data: {
          users: [{ id: 'user_constraint', email: testEmail }]
        },
        error: null
      });

      // Mock constraint violation
      mockSupabaseClient.rpc.mockResolvedValue({
        data: null,
        error: {
          message: 'duplicate key value violates unique constraint',
          code: '23505'
        }
      });

      const result = await updateUserSubscription(
        testEmail,
        'basic',
        99800,
        'constraint_test_ref'
      );

      expect(result.success).toBe(false);
      expect(result.errorCode).toBe('DATABASE_ERROR');
    });
  });
});