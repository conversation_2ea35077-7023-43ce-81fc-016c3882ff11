
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider, useAuth } from "./hooks/use-auth";
import { usePageStatePersistence } from "./hooks/use-page-state-persistence";
import Index from "./pages/Index";
import QuizPage from "./pages/QuizPage";
import QuizzesPage from "./pages/QuizzesPage";
import ProfilePage from "./pages/ProfilePage";
import AdminDashboard from "./pages/AdminDashboard";
import AuthPage from "./pages/AuthPage";
import VerifyEmailPage from "./pages/VerifyEmailPage";
import NotFound from "./pages/NotFound";
import AboutPage from "./pages/AboutPage";
import ContactPage from "./pages/ContactPage";
import PaymentSuccess from "./pages/PaymentSuccess";
import PaymentTroubleshoot from "./pages/PaymentTroubleshoot";
import LearningMaterialsPage from "./pages/LearningMaterialsPage";
import LearningMaterialPage from "./pages/LearningMaterialPage";
import { isUserAdmin } from "./utils/auth-helpers";
import { useEffect, useState } from "react";
import { appConfig, featureFlags } from "./config";
import InstallPWA from "./components/InstallPWA";
import ScrollToTop from "./components/ScrollToTop";
import DeveloperTools from "./pages/DeveloperTools";

const queryClient = new QueryClient();

// Protected route component
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { user, isLoading } = useAuth();

  if (isLoading) return <div className="flex items-center justify-center min-h-screen">Loading...</div>;
  if (!user) return <Navigate to="/auth" replace />;

  return <>{children}</>;
};

// Admin route component - now with proper admin check
const AdminRoute = ({ children }: { children: React.ReactNode }) => {
  const { user, isLoading } = useAuth();
  const [isAdmin, setIsAdmin] = useState(false);
  const [isCheckingAdmin, setIsCheckingAdmin] = useState(true);

  useEffect(() => {
    async function checkAdminStatus() {
      if (user) {
        setIsCheckingAdmin(true);
        const adminStatus = await isUserAdmin(user);
        setIsAdmin(adminStatus);
        setIsCheckingAdmin(false);
      } else {
        setIsAdmin(false);
        setIsCheckingAdmin(false);
      }
    }

    checkAdminStatus();
  }, [user]);

  if (isLoading || isCheckingAdmin) {
    return <div className="flex items-center justify-center min-h-screen">Loading...</div>;
  }

  if (!user) return <Navigate to="/auth" replace />;

  // Check if user is an admin
  if (!isAdmin) return <Navigate to="/" replace />;

  return <>{children}</>;
};

const App = () => {
  // Set document title from environment variables
  useEffect(() => {
    document.title = appConfig.name;

    // Log debug information if debug mode is enabled
    if (featureFlags.enableDebugMode) {
      console.log('App config:', appConfig);
      console.log('Feature flags:', featureFlags);
    }
  }, []);

  // Component to handle page state persistence
  const AppWithPersistence = () => {
    usePageStatePersistence();

    return (
      <>
        <ScrollToTop />
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/auth" element={<AuthPage />} />
          <Route path="/auth/verify" element={<VerifyEmailPage />} />
          <Route path="/verify" element={<VerifyEmailPage />} />
          <Route path="/quizzes" element={<QuizzesPage />} />
          <Route path="/quiz/:topicId" element={<QuizPage />} />
          <Route path="/learn" element={<LearningMaterialsPage />} />
          <Route path="/learn/:materialId" element={<LearningMaterialPage />} />
          <Route path="/profile" element={<ProtectedRoute><ProfilePage /></ProtectedRoute>} />
          <Route path="/admin" element={<AdminRoute><AdminDashboard /></AdminRoute>} />
          <Route path="/developer" element={<AdminRoute><DeveloperTools /></AdminRoute>} />
          <Route path="/about" element={<AboutPage />} />
          <Route path="/contact" element={<ContactPage />} />
          <Route path="/payment/success" element={<PaymentSuccess />} />
          <Route path="/payment/troubleshoot" element={<PaymentTroubleshoot />} />
          <Route path="*" element={<NotFound />} />
        </Routes>
        <InstallPWA />
      </>
    );
  };

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <AppWithPersistence />
          </BrowserRouter>
        </TooltipProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
};

export default App;
