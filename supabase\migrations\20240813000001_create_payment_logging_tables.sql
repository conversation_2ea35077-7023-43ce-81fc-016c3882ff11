-- Create payment events table for comprehensive logging
CREATE TABLE IF NOT EXISTS payment_events (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    event_type TEXT NOT NULL,
    severity TEXT NOT NULL CHECK (severity IN ('info', 'warn', 'error', 'critical')),
    reference TEXT,
    user_id UUID,
    plan_id TEXT,
    amount DECIMAL(10,2),
    currency TEXT DEFAULT 'NGN',
    status TEXT,
    error_code TEXT,
    error_message TEXT,
    processing_time_ms INTEGER,
    metadata JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_payment_events_event_type ON payment_events(event_type);
CREATE INDEX IF NOT EXISTS idx_payment_events_severity ON payment_events(severity);
CREATE INDEX IF NOT EXISTS idx_payment_events_reference ON payment_events(reference);
CREATE INDEX IF NOT EXISTS idx_payment_events_user_id ON payment_events(user_id);
CREATE INDEX IF NOT EXISTS idx_payment_events_created_at ON payment_events(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_payment_events_status ON payment_events(status);

-- Create payment errors table for error tracking and alerting
CREATE TABLE IF NOT EXISTS payment_errors (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    error_code TEXT NOT NULL,
    error_message TEXT NOT NULL,
    event_type TEXT NOT NULL,
    reference TEXT,
    user_id UUID,
    stack_trace TEXT,
    context JSONB,
    occurrence_count INTEGER DEFAULT 1,
    first_occurred TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_occurred TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(error_code, event_type)
);

-- Create indexes for error tracking
CREATE INDEX IF NOT EXISTS idx_payment_errors_error_code ON payment_errors(error_code);
CREATE INDEX IF NOT EXISTS idx_payment_errors_event_type ON payment_errors(event_type);
CREATE INDEX IF NOT EXISTS idx_payment_errors_last_occurred ON payment_errors(last_occurred DESC);
CREATE INDEX IF NOT EXISTS idx_payment_errors_occurrence_count ON payment_errors(occurrence_count DESC);

-- Create webhook events table if it doesn't exist (referenced in webhook code)
CREATE TABLE IF NOT EXISTS webhook_events (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    event_id TEXT UNIQUE NOT NULL,
    event_type TEXT NOT NULL,
    reference TEXT,
    status TEXT NOT NULL CHECK (status IN ('received', 'processing', 'completed', 'failed', 'duplicate')),
    payload_hash TEXT NOT NULL,
    processing_attempts INTEGER DEFAULT 1,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for webhook events
CREATE INDEX IF NOT EXISTS idx_webhook_events_event_id ON webhook_events(event_id);
CREATE INDEX IF NOT EXISTS idx_webhook_events_event_type ON webhook_events(event_type);
CREATE INDEX IF NOT EXISTS idx_webhook_events_status ON webhook_events(status);
CREATE INDEX IF NOT EXISTS idx_webhook_events_created_at ON webhook_events(created_at DESC);

-- Function to get payment metrics for a time period
CREATE OR REPLACE FUNCTION get_payment_metrics(
    start_time TIMESTAMP WITH TIME ZONE,
    end_time TIMESTAMP WITH TIME ZONE
)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_payments', COALESCE(payment_stats.total_payments, 0),
        'successful_payments', COALESCE(payment_stats.successful_payments, 0),
        'failed_payments', COALESCE(payment_stats.failed_payments, 0),
        'success_rate', COALESCE(
            CASE 
                WHEN payment_stats.total_payments > 0 
                THEN payment_stats.successful_payments::DECIMAL / payment_stats.total_payments::DECIMAL 
                ELSE 0 
            END, 0
        ),
        'average_processing_time', COALESCE(payment_stats.avg_processing_time, 0),
        'total_revenue', COALESCE(payment_stats.total_revenue, 0),
        'webhook_events', COALESCE(webhook_stats.total_webhooks, 0),
        'webhook_failures', COALESCE(webhook_stats.failed_webhooks, 0)
    ) INTO result
    FROM (
        SELECT 
            COUNT(*) FILTER (WHERE event_type IN ('payment_verification_started', 'payment_verification_success', 'payment_verification_failed')) as total_payments,
            COUNT(*) FILTER (WHERE event_type = 'payment_verification_success') as successful_payments,
            COUNT(*) FILTER (WHERE event_type = 'payment_verification_failed') as failed_payments,
            AVG(processing_time_ms) FILTER (WHERE processing_time_ms IS NOT NULL) as avg_processing_time,
            SUM(amount) FILTER (WHERE event_type = 'payment_verification_success' AND amount IS NOT NULL) as total_revenue
        FROM payment_events 
        WHERE created_at BETWEEN start_time AND end_time
    ) payment_stats
    CROSS JOIN (
        SELECT 
            COUNT(*) as total_webhooks,
            COUNT(*) FILTER (WHERE status = 'failed') as failed_webhooks
        FROM webhook_events 
        WHERE created_at BETWEEN start_time AND end_time
    ) webhook_stats;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Function to update payment error occurrence count
CREATE OR REPLACE FUNCTION upsert_payment_error(
    p_error_code TEXT,
    p_error_message TEXT,
    p_event_type TEXT,
    p_reference TEXT DEFAULT NULL,
    p_user_id UUID DEFAULT NULL,
    p_stack_trace TEXT DEFAULT NULL,
    p_context JSONB DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    error_id UUID;
BEGIN
    INSERT INTO payment_errors (
        error_code, error_message, event_type, reference, user_id, 
        stack_trace, context, occurrence_count, first_occurred, last_occurred
    )
    VALUES (
        p_error_code, p_error_message, p_event_type, p_reference, p_user_id,
        p_stack_trace, p_context, 1, NOW(), NOW()
    )
    ON CONFLICT (error_code, event_type) 
    DO UPDATE SET
        occurrence_count = payment_errors.occurrence_count + 1,
        last_occurred = NOW(),
        error_message = EXCLUDED.error_message,
        reference = COALESCE(EXCLUDED.reference, payment_errors.reference),
        user_id = COALESCE(EXCLUDED.user_id, payment_errors.user_id),
        stack_trace = COALESCE(EXCLUDED.stack_trace, payment_errors.stack_trace),
        context = COALESCE(EXCLUDED.context, payment_errors.context)
    RETURNING id INTO error_id;
    
    RETURN error_id;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up old payment events (for maintenance)
CREATE OR REPLACE FUNCTION cleanup_old_payment_events(
    retention_days INTEGER DEFAULT 90
)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM payment_events 
    WHERE created_at < NOW() - INTERVAL '1 day' * retention_days;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Also clean up old webhook events
    DELETE FROM webhook_events 
    WHERE created_at < NOW() - INTERVAL '1 day' * retention_days;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create RLS policies for payment events (if RLS is enabled)
ALTER TABLE payment_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_errors ENABLE ROW LEVEL SECURITY;
ALTER TABLE webhook_events ENABLE ROW LEVEL SECURITY;

-- Allow service role to access all records
CREATE POLICY "Service role can access payment events" ON payment_events
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can access payment errors" ON payment_errors
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can access webhook events" ON webhook_events
    FOR ALL USING (auth.role() = 'service_role');

-- Grant necessary permissions
GRANT ALL ON payment_events TO service_role;
GRANT ALL ON payment_errors TO service_role;
GRANT ALL ON webhook_events TO service_role;
GRANT EXECUTE ON FUNCTION get_payment_metrics(TIMESTAMP WITH TIME ZONE, TIMESTAMP WITH TIME ZONE) TO service_role;
GRANT EXECUTE ON FUNCTION upsert_payment_error(TEXT, TEXT, TEXT, TEXT, UUID, TEXT, JSONB) TO service_role;
GRANT EXECUTE ON FUNCTION cleanup_old_payment_events(INTEGER) TO service_role;