import express, { Request, Response } from 'express';
import { checkAndUpdateExpiredSubscriptions } from '../services/subscription.js';

const router = express.Router();

// Endpoint to check for expired subscriptions
router.post('/check-expired', async (_req: Request, res: Response) => {
  try {
    const result = await checkAndUpdateExpiredSubscriptions();
    
    if (result.success) {
      const expiredCount = result.data?.expiredCount || 0;
      const cleanedUsers = result.data?.cleanedUsers || [];
      return res.status(200).json({
        success: true,
        message: `Successfully checked for expired subscriptions. Updated ${expiredCount} subscriptions.`,
        updated: expiredCount,
        cleanedUsers: cleanedUsers
      });
    } else {
      return res.status(500).json({
        success: false,
        message: 'Failed to check for expired subscriptions',
        error: result.error
      });
    }
  } catch (error) {
    console.error('Error checking expired subscriptions:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during subscription check',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
